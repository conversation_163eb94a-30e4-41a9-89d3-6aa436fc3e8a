import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { nanoid } from "nanoid";
// @ts-ignore
import slugify from "@sindresorhus/slugify";
/**
 * 生成组织的唯一slug
 * @param name 组织名称
 * @returns 唯一的slug
 */
async function generateOrganizationSlug(name: string): Promise<string> {

	// 使用 slugify 生成基础 slug
	let baseSlug = slugify(name, {
		lowercase: true,
	});

	// 当 slugify 返回空字符串时（如纯中文输入），使用小写随机字符串
	if (!baseSlug) {
		baseSlug = nanoid(8).toLowerCase();
	}

	let slug = baseSlug;
	let hasAvailableSlug = false;

	// 尝试最多3次找到可用的slug
	for (let i = 0; i < 3; i++) {
		const existing = await db.organization.findUnique({
			where: {
				slug,
			},
		});

		if (!existing) {
			hasAvailableSlug = true;
			break;
		}

		// 如果slug已存在，添加随机后缀
		slug = `${baseSlug}-${nanoid(5)}`;
	}

	if (!hasAvailableSlug) {
		throw new Error("无法生成唯一的组织slug，请稍后重试");
	}

	return slug;
}

/**
 * 根据邮箱或ID查找用户
 * @param identifier 用户邮箱或ID
 * @returns 用户对象或null
 */
async function findUserByIdentifier(identifier: string) {
	// 首先尝试通过邮箱查找
	let user = await db.user.findUnique({
		where: {
			email: identifier,
		},
	});

	// 如果通过邮箱没找到，尝试通过ID查找
	if (!user) {
		user = await db.user.findUnique({
			where: {
				id: identifier,
			},
		});
	}

	return user;
}

/**
 * 创建组织并添加创建者为管理员
 * @param name 组织名称
 * @param creatorIdentifier 创建者邮箱或ID
 */
async function createOrganization(
	name: string,
	creatorIdentifier: string,
) {
	// 检查创建者用户是否存在
	const creator = await findUserByIdentifier(creatorIdentifier);

	if (!creator) {
		throw new Error(`用户 ${creatorIdentifier} 不存在，请先创建用户`);
	}

	// 生成唯一的slug
	const slug = await generateOrganizationSlug(name);
	const organizationId = nanoid();
	const memberId = nanoid();
	const now = new Date();

	// 使用事务创建组织和成员关系
	const result = await db.$transaction(async (tx) => {
		// 创建组织
		const organization = await tx.organization.create({
			data: {
				id: organizationId,
				name,
				slug,
				createdAt: now,
			},
		});

		// 将创建者添加为组织的所有者
		const member = await tx.member.create({
			data: {
				id: memberId,
				organizationId: organization.id,
				userId: creator.id,
				role: "owner", // 设置为所有者角色
				createdAt: now,
			},
		});

		return { organization, member };
	});

	return result;
}

async function main() {
	logger.info("🏢 让我们为您的应用创建一个新组织！");

	// 获取组织名称
	const name = await logger.prompt("请输入组织名称:", {
		required: true,
		placeholder: "我的公司",
		type: "text",
	});

	// 验证组织名称长度
	if (name.length < 3 || name.length > 32) {
		logger.error("组织名称长度必须在3-32个字符之间！");
		return;
	}

	// 获取创建者标识（邮箱或ID）
	const creatorIdentifier = await logger.prompt("请输入创建者邮箱或用户ID:", {
		required: true,
		placeholder: "<EMAIL> 或 user_id_123",
		type: "text",
	});

	try {
		logger.info("正在查找用户...");

		// 先验证用户是否存在并显示用户信息
		const creator = await findUserByIdentifier(creatorIdentifier);
		if (!creator) {
			logger.error(`❌ 用户 ${creatorIdentifier} 不存在，请先创建用户`);
			return;
		}

		logger.info(`✅ 找到用户: ${creator.name} (${creator.email})`);
		logger.info("正在创建组织...");

		const { organization, member } = await createOrganization(
			name,
			creatorIdentifier,
		);

		logger.success("✅ 组织创建成功！");
		logger.info("📋 组织信息:");
		logger.info(`   ID: ${organization.id}`);
		logger.info(`   名称: ${organization.name}`);
		logger.info(`   Slug: ${organization.slug}`);
		logger.info(`   创建时间: ${organization.createdAt.toISOString()}`);
		logger.info("👤 创建者已设置为组织所有者");
		logger.info(`   用户: ${creator.name} (${creator.email})`);
		logger.info(`   成员ID: ${member.id}`);
		logger.info(`   角色: ${member.role}`);
	} catch (error) {
		logger.error(`❌ 创建组织失败: ${error instanceof Error ? error.message : String(error)}`);
	}
}

main().catch((error) => {
	logger.error("脚本执行失败:", error);
	process.exit(1);
});
