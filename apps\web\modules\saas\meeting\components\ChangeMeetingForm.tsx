"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { useRouter} from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useSession } from "../../auth/hooks/use-session";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@ui/components/select";
import { toast } from "sonner";
import { useState, useEffect } from "react";
import { useTheme } from "next-themes";

// 定义表单数据验证模式
const formSchema = z
	.object({
		title: z
			.string()
			.min(2, "会议标题至少需要2个字符")
			.max(100, "会议标题不能超过100个字符"),
		date: z.string().min(1, "请选择会议日期"),
		startTime: z.string().min(1, "请选择开始时间"),
		endTime: z.string().min(1, "请选择结束时间"),
		location: z.string().optional(),
		isRecurring: z.boolean().default(false),
		timezone: z.string().min(1, "请选择时区"),
		password: z.string().optional(),
		requirePassword: z.boolean().default(false),
		muteParticipantsOnEntry: z
			.enum(["muteOn", "muteOff", "muteAuto"])
			.default("muteOn"),
		waitingRoom: z.boolean().default(false),
		recordMeeting: z.boolean().default(false),
		allowChat: z.boolean().default(true),
		enterInAdvance: z.boolean().default(false),
		multiPlatform: z.boolean().default(false),
		allowScreenShare: z.boolean().default(true),
		allowParticipantsToRename: z.boolean().default(true),
		enableHostKey: z.boolean().default(false),
		hostKey: z.string().optional(),
		enableDocUpload: z.boolean().default(true),
		screenWatermark: z.boolean().default(false),
		disableScreenshot: z.boolean().default(false),
		autoTranscribe: z.boolean().default(false),
		playIvrOnJoin: z.boolean().default(false),
		playIvrOnLeave: z.boolean().default(false),
		onlyEnterpriseUserAllowed: z.boolean().default(false),
		recurringType: z.number().default(0),
		untilType: z.number().default(0),
		untilDate: z.string().optional(),
		untilCount: z.number().min(1).max(200).optional(),
	})
	.refine(
		(data) => {
			// 验证开始时间不能早于当前时间
			const now = new Date();
			const startDateTime = new Date(`${data.date}T${data.startTime}:00`);

			if (startDateTime <= now) {
				return false;
			}

			// 验证结束时间是否晚于开始时间至少15分钟
			const endDateTime = new Date(`${data.date}T${data.endTime}:00`);
			const diffInMinutes =
				(endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60);
			return diffInMinutes >= 15;
		},
		{
			message: "开始时间不能早于当前时间",
			path: ["startTime"],
		},
	)
	.refine(
		(data) => {
			// 验证结束时间是否晚于开始时间至少15分钟
			const startDateTime = new Date(`${data.date}T${data.startTime}:00`);
			const endDateTime = new Date(`${data.date}T${data.endTime}:00`);
			const diffInMinutes =
				(endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60);
			return diffInMinutes >= 15;
		},
		{
			message: "结束时间必须比开始时间晚至少15分钟",
			path: ["endTime"],
		},
	);

// 表单数据类型
type FormValues = z.infer<typeof formSchema>;

/**
 * 预定会议表单组件
 * 
 * @param organizationSlug - 组织的 slug
 * @returns 渲染的表单组件
 */
export function ChangeMeetingForm({ organizationSlug, meetingId }: { organizationSlug: string, meetingId: string }) {
  const router = useRouter();
  const { user } = useSession();
  const [meetingData, setMeetingData] = useState<any>(null);
  const { resolvedTheme } = useTheme();

  

	useEffect(() => {
		const fetchMeetingDetail = async (id: string) => {
			try {
				const response = await fetch(`/api/meetings/details/${id}`);

				if (!response.ok) {
					throw new Error(
						`获取会议详情失败 (状态码: ${response.status})`,
					);
				}

				const data = await response.json();
				setMeetingData(data.data.meeting_info_list[0]);
			} catch (error: any) {
				const errorMessage = error.message || "获取会议详情时发生错误";
				toast.error("获取会议详情失败", errorMessage);
			}
		};
		fetchMeetingDetail(meetingId);
	}, []); 

	

  // 初始化表单
		const form = useForm<FormValues>({
			resolver: zodResolver(formSchema),
			defaultValues: {
				title: "",
				date: new Date().toISOString().split("T")[0],
				startTime: (() => {
					// 获取最近的未来30分钟刻度时间
					const now = new Date();
					const currentMinutes = now.getMinutes();
					const currentHour = now.getHours();

					// 计算下一个30分钟刻度（必须晚于当前时间）
					let nextMinutes: number;
					let nextHour = currentHour;

					if (currentMinutes < 30) {
						nextMinutes = 30;
					} else {
						nextMinutes = 0;
						nextHour = (currentHour + 1) % 24;
					}

					return `${nextHour.toString().padStart(2, "0")}:${nextMinutes.toString().padStart(2, "0")}`;
				})(),
				endTime: (() => {
					// 计算开始时间1小时后作为结束时间
					const now = new Date();
					const currentMinutes = now.getMinutes();
					const currentHour = now.getHours();

					// 先计算开始时间
					let startMinutes: number;
					let startHour = currentHour;

					if (currentMinutes < 30) {
						startMinutes = 30;
					} else {
						startMinutes = 0;
						startHour = (currentHour + 1) % 24;
					}

					// 结束时间 = 开始时间 + 1小时
					const endHour = (startHour + 1) % 24;
					const endMinutes = startMinutes;

					return `${endHour.toString().padStart(2, "0")}:${endMinutes.toString().padStart(2, "0")}`;
				})(),
				location: "",
				isRecurring: false,
				timezone: "Asia/Shanghai",
				password: "",
				requirePassword: false,
				muteParticipantsOnEntry: "muteOn",
				waitingRoom: false,
				recordMeeting: true,
				multiPlatform: false,
				enterInAdvance: false,
				allowChat: true,
				allowScreenShare: true,
				allowParticipantsToRename: true,
				enableHostKey: false,
				hostKey: "",
				enableDocUpload: true,
				screenWatermark: false,
				disableScreenshot: false,
				autoTranscribe: true,
				playIvrOnJoin: false,
				playIvrOnLeave: false,
				onlyEnterpriseUserAllowed: false,
				recurringType: 0,
				untilType: 0,
				untilDate: (() => {
					const date = new Date();
					date.setDate(date.getDate() + 7);
					return date.toISOString().split("T")[0];
				})(),
				untilCount: 7,
			},
		});


  useEffect(() => {
    if (meetingData) {
      // 处理时间戳转换为日期和时间格式
      const startDateTime = new Date(Number.parseInt(meetingData.start_time) * 1000);
      const endDateTime = new Date(Number.parseInt(meetingData.end_time) * 1000);
      
      // 处理base64编码的时区
      let timezone = 'Asia/Shanghai';
      try {
        timezone = atob(meetingData.time_zone || '');
      } catch (e) {
        console.warn('时区解码失败，使用默认时区');
      }
      
	  
      // 使用真实数据重置表单
      form.reset({
        title: meetingData.subject || "",
        date: startDateTime.toISOString().split('T')[0],
        startTime: startDateTime.toTimeString().slice(0, 5), // HH:MM格式
        endTime: endDateTime.toTimeString().slice(0, 5), // HH:MM格式
        location: meetingData.location || "",
        isRecurring: meetingData.meeting_type === 1,
        timezone: timezone,
        password: "", // 密码不会在API中返回
        requirePassword: false, // 根据实际需要判断
        muteParticipantsOnEntry: meetingData.settings?.mute_enable_type_join === 0 ? "muteOff" : 
                                meetingData.settings?.mute_enable_type_join === 1 ? "muteOn" : "muteAuto",
        waitingRoom: meetingData.settings?.auto_in_waiting_room || false,
        recordMeeting: meetingData.settings?.auto_record_type === "cloud",
        multiPlatform: meetingData.settings?.allow_multi_device || false,
        enterInAdvance: meetingData.settings?.allow_in_before_host || false,
        allowChat: meetingData.settings?.allow_in_before_host || true, // API中没有对应字段，使用默认值
        allowScreenShare: true, // API中没有对应字段，使用默认值
        allowParticipantsToRename: meetingData.settings?.change_nickname === 1,
        enableHostKey: meetingData.enable_host_key || false,
        hostKey: "", // 主持人密钥不会在API中返回
        enableDocUpload: meetingData.enable_doc_upload_permission || false,
        screenWatermark: meetingData.settings?.allow_screen_shared_watermark || false,
        disableScreenshot: false, // API中没有对应字段，使用默认值
        autoTranscribe: meetingData.settings?.auto_asr || false,
        playIvrOnJoin: false, // API中没有对应字段，使用默认值
        playIvrOnLeave: false, // API中没有对应字段，使用默认值
        onlyEnterpriseUserAllowed: meetingData.settings?.only_enterprise_user_allowed || false,
        recurringType: 0, // 周期性会议的具体规则需要额外API获取
        untilType: 0,
        untilDate: (() => {
          const date = new Date();
          date.setDate(date.getDate() + 7);
          return date.toISOString().split('T')[0];
        })(),
        untilCount: 7,
      });
    }
  }, [meetingData, form]);

  console.log("form时区", form.getValues("timezone"));
  /* 修改块结束: 使用真实会议数据初始化表单
   * 修改时间: 2024-12-19
   */

  /* 修改块开始: 修改会议API调用优化
   * 修改范围: 传入真实用户ID而非使用固定operatorId
   * 对应需求: 使用user.id作为会议修改者ID
   * 恢复方法: 删除userId参数和用户检查，恢复原有的API调用
   */
  // 提交表单处理函数
  const onSubmit = async (values: FormValues) => {
    try {
      // 检查用户信息是否存在
      if (!user?.id) {
        form.setError("root", {
          type: "validation",
          message: "用户信息不存在，请重新登录"
        });
        return;
      }

      // console.log("表单数据:", values);
      
      // 获取当前日期和时间值
      const dateStr = values.date; // 格式：YYYY-MM-DD
      const startTimeStr = values.startTime; // 格式：HH:MM
      
      // 将日期和时间合并并转换为Date对象
      const startDateTime = new Date(`${dateStr}T${startTimeStr}:00`);
      
      // 将结束日期和时间合并并转换为Date对象
      const endTimeStr = values.endTime; // 格式：HH:MM
      const endDateTime = new Date(`${dateStr}T${endTimeStr}:00`);
      
      // 转换为Unix时间戳（秒）
      const startTimeUnix = Math.floor(startDateTime.getTime() / 1000).toString();
      const endTimeUnix = Math.floor(endDateTime.getTime() / 1000).toString();
      
      // 设置会议参数
      const settings = {
        mute_enable_type_join: values.muteParticipantsOnEntry === "muteOff" ? 0 : 
                               values.muteParticipantsOnEntry === "muteOn" ? 1 : 2,
        mute_enable_join: values.muteParticipantsOnEntry !== "muteOff",
        allow_unmute_self: true,
        allow_in_before_host: values.enterInAdvance,
        auto_in_waiting_room: values.waitingRoom,
        allow_screen_shared_watermark: values.screenWatermark,
        water_mark_type: values.screenWatermark ? 0 : undefined,
        auto_record_type: 'cloud',
        participant_join_auto_record: true,
        enable_host_pause_auto_record: true,
        allow_multi_device: values.multiPlatform,
        change_nickname: values.allowParticipantsToRename ? 1 : 2,
        auto_asr: true,
        open_asr_view: 0,
        play_ivr_on_join: values.playIvrOnJoin,
        play_ivr_on_leave: values.playIvrOnLeave,
        only_enterprise_user_allowed: values.onlyEnterpriseUserAllowed,
      };
      
      // 准备修改会议的请求参数
      const meetingData = {
        userid: user.id,
        subject: values.title,
        type: 0,
        meeting_type: values.isRecurring ? 1 : 0,
        start_time: startTimeUnix,
        end_time: endTimeUnix,
        settings: settings,
        // 如果是周期性会议，添加重复规则
        ...(values.isRecurring && {
          recurring_rule: {
            recurring_type: values.recurringType,
            until_type: values.untilType,
            ...(values.untilType === 0 && values.untilDate && {
              until_date: Math.floor(new Date(values.untilDate).getTime() / 1000)
            }),
            ...(values.untilType === 1 && values.untilCount && {
              until_count: values.untilCount
            })
          }
        }),
        // 条件性添加参数
        ...(values.requirePassword && values.password && { password: values.password }),
        ...(values.timezone && { time_zone: values.timezone }),
        ...(values.location && { location: values.location }),
        enable_host_key: values.enableHostKey,
        ...(values.enableHostKey && values.hostKey && { host_key: values.hostKey }),
        enable_doc_upload_permission: values.enableDocUpload,
      };
      
      // console.log("发送到API的数据:", meetingData);
      
      // 发送修改会议请求
      const response = await fetch(`/api/meetings/modify/${meetingId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(meetingData),
      });
      
      const data = await response.json();
      
      if (!response.ok || !data.success) {
        throw new Error(data.error || "修改会议失败");
      }
      
      toast.success("会议修改成功");
      
      // 重定向到会议列表页面
      router.push(`/app/${organizationSlug}/meeting/list`);
    } catch (error: any) {
      toast.error(error instanceof Error ? error.message : "修改会议失败");
      // 设置表单错误信息
      form.setError("root", {
        type: "server",
        message: `保存会议信息失败: ${error instanceof Error ? error.message : '未知错误'}`
      });
    }
  };
  /* 修改块结束: 修改会议API调用优化
   */

  // 获取密码设置状态和主持人密钥状态，用于条件渲染
  const requirePassword = form.watch("requirePassword");
  const enableHostKey = form.watch("enableHostKey");

  return (
			<Form {...form}>
				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="space-y-6"
				>
					{/* 会议标题 */}
					<FormField
						control={form.control}
						name="title"
						render={({ field }) => (
							<FormItem className="max-w-[400px]">
								<FormLabel className="text-sm font-medium">
									会议标题
								</FormLabel>
								<FormControl>
									<Input
										placeholder="输入会议标题"
										className="text-xs"
										{...field}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* 会议类型 - 改为勾选框 */}
					<FormField
						control={form.control}
						name="isRecurring"
						render={({ field }) => (
							<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
								<FormControl>
									<input
										type="checkbox"
										checked={field.value}
										onChange={field.onChange}
										className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
									/>
								</FormControl>
								<div className="space-y-1 leading-none">
									<FormLabel className="text-sm font-medium">
										周期性会议
									</FormLabel>
									<FormDescription className="text-xs">
										勾选此项将创建周期性会议，可以设置重复规则
									</FormDescription>
								</div>
							</FormItem>
						)}
					/>

					{/* 周期性会议设置 - 仅在勾选了周期性会议时显示 */}
					{form.watch("isRecurring") && (
						<div className="space-y-4">
							<div>
								{/* 重复类型 */}
								<FormField
									control={form.control}
									name="recurringType"
									render={({ field }) => (
										<FormItem className="max-w-[400px]">
											<FormLabel className="text-sm font-medium">
												重复频率
											</FormLabel>
											<Select
												onValueChange={(value) =>
													field.onChange(
														Number(value),
													)
												}
												value={field.value.toString()}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="选择重复类型" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="0">
														<p className="text-xs">
															每天
														</p>
													</SelectItem>
													<SelectItem value="1">
														<p className="text-xs">
															每个工作日
														</p>
													</SelectItem>
													<SelectItem value="2">
														<p className="text-xs">
															每周（周二）
														</p>
													</SelectItem>
													<SelectItem value="3">
														<p className="text-xs">
															每两周（周二）
														</p>
													</SelectItem>
													<SelectItem value="4">
														<p className="text-xs">
															每月（27日）
														</p>
													</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* 结束类型 */}
								<FormField
									control={form.control}
									name="untilType"
									render={({ field }) => (
										<FormItem className="max-w-[400px]">
											<FormLabel className="text-sm font-medium">
												结束重复
											</FormLabel>
											<Select
												onValueChange={(value) =>
													field.onChange(
														Number(value),
													)
												}
												value={field.value.toString()}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue
															placeholder="选择结束类型"
															className="text-xs"
														/>
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="0">
														<p className="text-xs">
															结束于某天
														</p>
													</SelectItem>
													<SelectItem value="1">
														<p className="text-xs">
															限定会议次数
														</p>
													</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* 结束日期 - 仅在选择了按日期结束时显示 */}
								{form.watch("untilType") === 0 && (
									<FormField
										control={form.control}
										name="untilDate"
										render={({ field }) => (
											<FormItem className="max-w-[400px]">
												<FormLabel className="text-sm font-medium">
													结束日期
												</FormLabel>
												<FormControl>
													<Input
														className="text-xs"
														type="date"
														{...field}
													/>
												</FormControl>
												<FormDescription className="text-xs">
													结束日期与第一场会议的开始时间换算成的场次数不能超过限制
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}

								{/* 会议次数 - 仅在选择了按次数结束时显示 */}
								{form.watch("untilType") === 1 && (
									<FormField
										control={form.control}
										name="untilCount"
										render={({ field }) => (
											<FormItem className="max-w-[400px]">
												<FormLabel className="text-sm font-medium">
													会议次数
												</FormLabel>
												<FormControl>
													<Input
														type="number"
														min={1}
														max={200}
														className="text-xs"
														{...field}
														onChange={(e) =>
															field.onChange(
																Number(
																	e.target
																		.value,
																),
															)
														}
													/>
												</FormControl>
												<FormDescription className="text-xs">
													每天、每个工作日、每周最大支持200场子会议；每两周、每月最大支持50场子会议
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}
							</div>
						</div>
					)}

					<div className="grid grid-cols-3 gap-x-4 max-w-[400px]">
						{/* 会议日期 */}
						{/* 会议日期 */}
						<FormField
							control={form.control}
							name="date"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium">
										会议日期
									</FormLabel>
									<FormControl>
										<Input
											type="date"
											className="h-9 w-full text-xs"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* 开始时间 */}
						<FormField
							control={form.control}
							name="startTime"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium">
										开始时间
									</FormLabel>
									<FormControl>
										<Select
											onValueChange={field.onChange}
											value={field.value}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="选择时间" />
											</SelectTrigger>
											<SelectContent className="max-h-48 overflow-y-auto">
												{/* 生成30分钟间隔的时间选项 */}
												{Array.from(
													{ length: 48 },
													(_, i) => {
														const hour = Math.floor(
															i / 2,
														);
														const minute =
															(i % 2) * 30;
														const timeString = `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
														return (
															<SelectItem
																key={timeString}
																value={
																	timeString
																}
															>
																{timeString}
															</SelectItem>
														);
													},
												)}
											</SelectContent>
										</Select>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* 结束时间 */}
						<FormField
							control={form.control}
							name="endTime"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium">
										结束时间
									</FormLabel>
									<FormControl>
										<Select
											onValueChange={field.onChange}
											value={field.value}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="选择时间" />
											</SelectTrigger>
											<SelectContent className="max-h-48 overflow-y-auto">
												{/* 生成30分钟间隔的时间选项 */}
												{Array.from(
													{ length: 48 },
													(_, i) => {
														const hour = Math.floor(
															i / 2,
														);
														const minute =
															(i % 2) * 30;
														const timeString = `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
														return (
															<SelectItem
																key={timeString}
																value={
																	timeString
																}
															>
																{timeString}
															</SelectItem>
														);
													},
												)}
											</SelectContent>
										</Select>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					{/* 时区选择 */}
					<FormField
						control={form.control}
						name="timezone"
						render={({ field }) => (
							<FormItem className="max-w-[400px]">
								<FormLabel className="text-sm font-medium">
									时区
								</FormLabel>
								<Select
									onValueChange={field.onChange}
									value={field.value}
								>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder="选择时区" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="Asia/Shanghai">
											<p className="text-xs">
												中国标准时间 (UTC+8)
											</p>
										</SelectItem>
										<SelectItem value="Asia/Hong_Kong">
											<p className="text-xs">
												香港时间 (UTC+8)
											</p>
										</SelectItem>
										<SelectItem value="Asia/Tokyo">
											<p className="text-xs">
												日本标准时间 (UTC+9)
											</p>
										</SelectItem>
										<SelectItem value="America/New_York">
											<p className="text-xs">
												美国东部时间 (UTC-5/4)
											</p>
										</SelectItem>
										<SelectItem value="America/Los_Angeles">
											<p className="text-xs">
												美国太平洋时间 (UTC-8/7)
											</p>
										</SelectItem>
										<SelectItem value="Europe/London">
											<p className="text-xs">
												格林威治标准时间 (UTC+0/1)
											</p>
										</SelectItem>
										<SelectItem value="Europe/Paris">
											<p className="text-xs">
												中欧时间 (UTC+1/2)
											</p>
										</SelectItem>
										<SelectItem value="Australia/Sydney">
											<p className="text-xs">
												澳大利亚东部时间 (UTC+10/11)
											</p>
										</SelectItem>
									</SelectContent>
								</Select>
								<FormDescription className="text-xs">
									选择会议的时区
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* 会议地点 */}
					<FormField
						control={form.control}
						name="location"
						render={({ field }) => (
							<FormItem className="max-w-[400px]">
								<FormLabel className="text-sm font-medium">
									会议地点（可选）
								</FormLabel>
								<FormControl>
									<Input
										placeholder="输入会议地点"
										className="text-xs"
										{...field}
									/>
								</FormControl>
								<FormDescription className="text-xs">
									可以输入线下会议地点或线上会议平台
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* 安全设置 */}
					<div className="space-y-4">
						{/* 是否需要密码 */}
						<FormLabel className="text-sm font-medium">
							保密
						</FormLabel>
						<FormField
							control={form.control}
							name="requirePassword"
							render={({ field }) => (
								<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
									<FormControl>
										<input
											type="checkbox"
											checked={field.value}
											onChange={field.onChange}
											className="h-4 w-4 rounded border-gray-300 text-primary text-sm focus:ring-primary"
										/>
									</FormControl>
									<div className="space-y-1 leading-none">
										<FormLabel className="text-xs font-medium">
											需要密码
										</FormLabel>
										<FormDescription className="text-xs">
											参会者需要输入密码才能加入会议
										</FormDescription>
									</div>
								</FormItem>
							)}
						/>

						{/* 密码输入框 - 条件渲染 */}
						{requirePassword && (
							<FormField
								control={form.control}
								name="password"
								render={({ field }) => (
									<FormItem className="max-w-[400px]">
										<FormLabel>会议密码</FormLabel>
										<FormControl>
											<Input
												type="password"
												className="text-xs"
												placeholder="设置会议密码"
												{...field}
											/>
										</FormControl>
										<FormDescription className="text-xs">
											密码将在会议邀请中分享给参会者
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						)}

						{/* 主持人密钥 */}
						<FormField
							control={form.control}
							name="enableHostKey"
							render={({ field }) => (
								<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
									<FormControl>
										<input
											type="checkbox"
											checked={field.value}
											onChange={field.onChange}
											className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
										/>
									</FormControl>
									<div className="space-y-1 leading-none">
										<FormLabel className="text-xs font-medium">
											开启主持人密钥
										</FormLabel>
										<FormDescription className="text-xs">
											使用密钥验证主持人身份
										</FormDescription>
									</div>
								</FormItem>
							)}
						/>

						{/* 主持人密钥输入框 - 条件渲染 */}
						{enableHostKey && (
							<FormField
								control={form.control}
								name="hostKey"
								render={({ field }) => (
									<FormItem className="max-w-[400px]">
										<FormLabel>主持人密钥</FormLabel>
										<FormControl>
											<Input
												type="password"
												className="text-xs"
												{...field}
											/>
										</FormControl>
										<FormDescription className="text-xs">
											密钥仅分享给会议主持人
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						)}
					</div>

					{/* 成员入会时静音设置 */}
					<div className="space-y-4">
						{/* 自动静音 */}
						<FormField
							control={form.control}
							name="muteParticipantsOnEntry"
							render={({ field }) => (
								<FormItem className="space-y-4">
									<FormLabel className="text-sm font-medium">
										成员入会时静音
									</FormLabel>
									<FormControl>
										<div className="space-y-2">
											<div className="flex items-center space-x-2">
												<input
													type="radio"
													id="mute-on"
													value="muteOn"
													checked={
														field.value === "muteOn"
													}
													onChange={() =>
														field.onChange("muteOn")
													}
													className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
												/>
												<label
													htmlFor="mute-on"
													className="text-xs font-medium"
												>
													开启
												</label>
											</div>

											<div className="flex items-center space-x-2">
												<input
													type="radio"
													id="mute-off"
													value="muteOff"
													checked={
														field.value ===
														"muteOff"
													}
													onChange={() =>
														field.onChange(
															"muteOff",
														)
													}
													className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
												/>
												<label
													htmlFor="mute-off"
													className="text-xs font-medium"
												>
													关闭
												</label>
											</div>

											<div className="flex items-center space-x-2">
												<input
													type="radio"
													id="mute-auto"
													value="muteAuto"
													checked={
														field.value ===
														"muteAuto"
													}
													onChange={() =>
														field.onChange(
															"muteAuto",
														)
													}
													className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
												/>
												<label
													htmlFor="mute-auto"
													className="text-xs font-medium"
												>
													超过6人后自动开启静音
												</label>
											</div>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					{/* 会议功能设置 */}
					<div className="space-y-4">
						<FormLabel className="text-sm font-medium">
							设置
						</FormLabel>

						{/* UI提示自动增强功能已开启 */}
						{/* 修改于2025-06-12: 添加主题适配的自动增强功能提示UI */}
						{/* 原问题: 缺少背景色和文字颜色，在暗色主题下显示不清楚 */}
						{/* 修改范围: 添加动态背景色和文字颜色类名 */}
						{/* 恢复方法: 删除bg-muted/bg-slate-100和文字颜色类名，恢复原有的空白类名 */}
						<div
							className={`flex items-start col-span-2 mb-2 p-2 rounded-md ${
								resolvedTheme === "dark"
									? "bg-slate-800 border border-slate-700"
									: "bg-blue-50 border border-blue-200"
							}`}
						>
							<div className="space-y-1 leading-none">
								<span
									className={`text-xs font-medium ${
										resolvedTheme === "dark"
											? "text-blue-300"
											: "text-blue-700"
									}`}
								>
									自动增强功能已开启
								</span>
								<p
									className={`text-xs ${
										resolvedTheme === "dark"
											? "text-blue-200"
											: "text-blue-600"
									}`}
								>
									为确保会议质量，系统已默认开启自动录制和自动文字转写功能
								</p>
							</div>
						</div>

						<div className="grid grid-cols-2 gap-4  p-4">
							{/* 自动录制 - 固定开启 */}
							<FormField
								control={form.control}
								name="recordMeeting"
								render={() => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={true}
												disabled
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary cursor-not-allowed"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												自动录制
											</FormLabel>
											<FormDescription className="text-xs">
												会议将自动录制并保存
											</FormDescription>
										</div>
									</FormItem>
								)}
							/>

							{/* 自动文字转写 - 固定开启 */}
							<FormField
								control={form.control}
								name="autoTranscribe"
								render={() => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={true}
												disabled
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary cursor-not-allowed"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												自动文字转写
											</FormLabel>
											<FormDescription className="text-xs">
												会议内容将自动转写为文字
											</FormDescription>
										</div>
									</FormItem>
								)}
							/>

							{/*等候室*/}
							<FormField
								control={form.control}
								name="waitingRoom"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={field.value}
												onChange={field.onChange}
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												启用等候室
											</FormLabel>
											<FormDescription className="text-xs">
												参会者需要等待主持人批准才能加入会议
											</FormDescription>
										</div>
									</FormItem>
								)}
							/>

							{/* 允许聊天 */}
							{/* <FormField
								control={form.control}
								name="allowChat"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={field.value}
												onChange={field.onChange}
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												启用会议聊天
											</FormLabel>
											<FormDescription className="text-xs">
												允许参会者在会议中发送文字消息
											</FormDescription>
										</div>
									</FormItem>
								)}
							/> */}

							{/* 允许成员多端入会 */}
							<FormField
								control={form.control}
								name="multiPlatform"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={field.value}
												onChange={field.onChange}
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												允许成员多端入会
											</FormLabel>
											<FormDescription className="text-xs">
												成员可以从多平台入会
											</FormDescription>
										</div>
									</FormItem>
								)}
							/>

							{/* 成员可提前进入会议 */}
							<FormField
								control={form.control}
								name="enterInAdvance"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={field.value}
												onChange={field.onChange}
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
											/>
										</FormControl>

										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												允许成员在主持人在进会前加入会议
											</FormLabel>
											<FormDescription className="text-xs">
												成员可提前进入会议，消耗会议时长
											</FormDescription>
										</div>
									</FormItem>
								)}
							/>

							{/* 允许屏幕共享 */}
							{/* <FormField
								control={form.control}
								name="allowScreenShare"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={field.value}
												onChange={field.onChange}
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<div className="text-xs text-gray-500">
												当前状态:{" "}
												{field.value ? "开启" : "关闭"}
											</div>
											<FormLabel className="text-xs font-medium">
												允许屏幕共享
											</FormLabel>
											<FormDescription className="text-xs">
												允许参会者共享自己的屏幕
											</FormDescription>
										</div>
									</FormItem>
								)}
							/> */}

							{/* 开启屏幕水印 */}
							<FormField
								control={form.control}
								name="screenWatermark"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={field.value}
												onChange={field.onChange}
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												开启屏幕共享水印
											</FormLabel>
											<FormDescription className="text-xs">
												在共享的内容上显示用户身份水印
											</FormDescription>
										</div>
									</FormItem>
								)}
							/>

							{/* 禁用笔记截屏 */}
							{/* <FormField
								control={form.control}
								name="disableScreenshot"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={field.value}
												onChange={field.onChange}
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												禁用笔记截屏
											</FormLabel>
											<FormDescription className="text-xs">
												禁止参会者对会议内容进行截屏
											</FormDescription>
										</div>
									</FormItem>
								)}
							/> */}

							{/* 仅限企业内部成员入会 */}
							{/* <FormField
								control={form.control}
								name="onlyEnterpriseUserAllowed"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={field.value}
												onChange={field.onChange}
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												仅限企业内部成员入会
											</FormLabel>
											<FormDescription className="text-xs">
												只允许企业内部成员参加会议
											</FormDescription>
										</div>
									</FormItem>
								)}
							/> */}

							{/* 入会提示音 */}
							{/* <FormField
								control={form.control}
								name="playIvrOnJoin"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={field.value}
												onChange={field.onChange}
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												入会提示音
											</FormLabel>
											<FormDescription className="text-xs">
												有新参会者加入时播放提示音
											</FormDescription>
										</div>
									</FormItem>
								)}
							/> */}

							{/* 离会提示音 */}
							{/* <FormField
								control={form.control}
								name="playIvrOnLeave"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={field.value}
												onChange={field.onChange}
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												离会提示音
											</FormLabel>
											<FormDescription className="text-xs">
												参会者离开时播放提示音
											</FormDescription>
										</div>
									</FormItem>
								)}
							/> */}
						</div>
					</div>

					{/* 文档上传设置 */}
					<div className="space-y-4">
						<FormLabel className="text-sm font-medium">
							文档管理
						</FormLabel>
						<div className="space-y-4 rounded-md p-4">
							{/* 允许成员上传文档 */}
							<FormField
								control={form.control}
								name="enableDocUpload"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
										<FormControl>
											<input
												type="checkbox"
												checked={field.value}
												onChange={field.onChange}
												className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-xs font-medium">
												允许成员上传文档
											</FormLabel>
											<FormDescription className="text-xs">
												参会者可以在会议中上传和分享文档
											</FormDescription>
										</div>
									</FormItem>
								)}
							/>
						</div>
					</div>

					{/* 错误提示和提交按钮 */}
					{form.formState.errors.root && (
						<p className="text-xs font-medium text-destructive">
							{form.formState.errors.root.message}
						</p>
					)}

					<div className="flex justify-start gap-4">
						<Button
							type="submit"
							loading={form.formState.isSubmitting}
							className="w-[150px]"
						>
							修改会议
						</Button>
						<Button
							type="button"
							variant="outline"
							onClick={() =>
								router.push(
									`/app/${organizationSlug}/meeting/list`,
								)
							}
							className="w-[150px]"
						>
							取消修改
						</Button>
					</div>
				</form>
			</Form>
		);
} 