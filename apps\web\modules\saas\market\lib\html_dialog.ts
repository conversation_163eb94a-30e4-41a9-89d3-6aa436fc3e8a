// 使用 @repo/utils/lib/crypto 工具函数
// 请求后端接口 获取投资人 html 和 markdown 数据
import { encryptRequestData, generateSign, decryptData } from "@repo/utils/lib/crypto";
import type { FundCard } from "./TestData";


// 获取投资人 html 或者 markdown 数据
export async function fetchFundDetail(card: FundCard): Promise<{ html?: string; markdown?: string; error?: string }> {
  try {
    const businessData = { data: { keywords: card.code }, timestamp: Date.now() };
    const content = await encryptRequestData(businessData);
    const sign = await generateSign(content);
    const body = JSON.stringify({ content, sign });
    const res = await fetch(`/api/n8n_proxy/template?keywords=${card.code}`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body
    });
    const data = await res.json();
    if (data.code === 200 && data.data) {
      let decrypted: any = await decryptData(data.data);
      if (typeof decrypted === "string") {
        try { decrypted = JSON.parse(decrypted); } catch {}
      }
      if (decrypted && typeof decrypted === "object" && decrypted.html) {
        let htmlStr = decrypted.html;
        try {
          const doc = new window.DOMParser().parseFromString(htmlStr, "text/html");
          htmlStr = doc.body ? doc.body.innerHTML : htmlStr;
        } catch {}
        return { html: htmlStr };
      }
      if (decrypted && typeof decrypted === "object" && decrypted.mark_down) {
        return { markdown: decrypted.mark_down };
      }
      return { error: "暂无内容" };
    }
    return { error: data.message || "接口请求失败" };
  } catch (e) {
    return { error: "加载失败" };
  }
}