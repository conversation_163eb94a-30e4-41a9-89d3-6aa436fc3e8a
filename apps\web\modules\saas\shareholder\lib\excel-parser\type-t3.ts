/**
 * t3名册Excel文件解析器
 * 专门用于处理t3类型股东名册的Excel文件解析
 * 
 * 主要功能:
 * 1. 解析t3名册特有的字段结构
 * 2. 提取t3名册中的公司信息和股东信息
 * 3. 处理t3名册特有的数据格式和规则
 * 
 * t3名册特点:
 * - 文件名格式为t3XXXXXXxxyyyymmddzz.mdd
 * - zz可以是"all"(全体股东名册)或"tN"(前N名股东名册)
 * - 包含普通持股数量(PTZQZHCYSL)、信用持股数量(XYCYSL)和总持股数量(ZCYSL)
 * 
 * @version 1.2.1 (2025-06-04)
 * <AUTHOR> 2025-06-03 19:30:24
 * @update 2025-06-03 19:46:41 增强编码处理能力，解决中文乱码问题
 * @update 2025-06-03 20:15:00 使用fixEncoding函数处理ZQJC字段，解决中文乱码问题
 * @update 2025-06-04 11:52:14 添加对SJLX=801（股东数据）的识别功能
 * @update 2025-06-04 13:46:02 修复所有字段的中文编码问题，确保每个字段都经过fixEncoding处理
 */

import * as XLSX from "xlsx";
import { 
  checkRequiredFields,
  cleanString,
  extractCompanyInfo,
  extractRegularRecords,
  identifySpecialRecords,
  parseExcelFileName,
  readExcelWorksheet,
  validateRequiredFieldsContent,
  extractUnifiedCompanyInfo,
  fixEncoding
} from "./common";
import { MAX_RECORDS_COUNT } from "../config";

// t3名册必填字段列表
/**
 * @update 2025-06-24 18:57:52 hayden 修复字段名不一致问题
 * 将GDLB改为CYRLB，与后端验证保持一致
 */
const REQUIRED_FIELDS_T3 = [
	{ name: "YMTZHHM", label: "YMTZHHM" },
	{ name: "CYRMC", label: "YMTZHHM" },
	{ name: "ZJHM", label: "YMTZHHM" },
	{ name: "CYRLB", label: "YMTZHHM" },
];

// 根据参照表中的字段列表，定义t3名册所有可能的字段
const T3_ALL_FIELDS = [
	"YMTZHHM", // 一码通账户号码 (unifiedAccountNumber)
	"CYRMC", // 持有人名称 (securitiesAccountName)
	"ZJHM", // 证件号码 (shareholderId)
	"CYRLB", // 持有人类别 (shareholderCategory)
	"QYDJR", // 权益登记日 (reportDate)
	"PTZQZHCYSL", // 普通持股数量 (sharesInCashAccount)
	"XYCYSL", // 信用持股数量 (sharesInMarginAccount)
	"ZCYSL", // 总持股数量 (numberOfShares)
	"ZYDJZS", // 质押冻结总数 (frozenShares)
	"TXDZ", // 通讯地址 (contactAddress)
	"LXDH", // 联系电话 (contactNumber)
	"YZBM", // 邮政编码 (zipCode)
	"GLGXSFQR", // 关联关系确认标识 (relatedPartyIndicator)
	"PTZQZH", // 普通证券账户 (cashAccount)
	"XYZQZH", // 信用证券账户 (marginAccount)
	"BZ", // 备注 (remarks)
	"CYBL", // 持股比例 (shareholdingRatio)
	"ZQDM", // 证券代码 (securityCode)
	"ZQJC", // 证券简称 (securityName)
	"ZQZSL",
	"CYRS",
];

/**
 * t3名册字段映射
 * @update 2025-06-24 18:57:52 hayden 添加GDLB到CYRLB的映射，处理字段名不一致问题
 */
const T3_FIELD_MAPPING: { [key: string]: string } = {
  // 保留原始字段
  "CYRMC": "CYRMC",
  "CYRLB": "CYRLB",
  "GDLB": "CYRLB",        // 映射股东类别到持有人类别
  "QYDJR": "QYDJR",
  "ZYDJZS": "ZYDJZS",
  "TXDZ": "TXDZ",
  "LXDH": "LXDH",
  "YZBM": "YZBM",
  "GLGXSFQR": "GLGXSFQR",
  "BZ": "BZ",
  "CYBL": "CYBL",
  "PTZQZHHM": "PTZQZH",    // 映射普通证券账户号码
  "XYZQZHHM": "XYZQZH",     // 映射信用证券账户号码
  "ZQDM": "ZQDM",
  "ZQJC": "ZQJC",
  "ZQZSL": "ZQZSL",
  "CYRS": "CYRS"
};

/**
 * 检查记录是否包含参照表中列出的所有t3名册字段
 * 
 * @param record 记录对象
 * @returns 缺失的字段列表
 */
function checkT3FieldsCompleteness(record: Record<string, any>): string[] {
  const missingFields: string[] = [];
  
  // 检查每个字段是否存在
  for (const field of T3_ALL_FIELDS) {
    if (!Object.prototype.hasOwnProperty.call(record, field)) {
      missingFields.push(field);
    }
  }
  
  return missingFields;
}


/**
 * 校验文件名中的公司代码和期数时间与SJLX=802记录中的信息是否一致
 * 
 * @param fileNameInfo 从文件名中提取的信息
 * @param basicInfo 从SJLX=802记录中提取的基本信息
 * @returns 校验结果
 */
function validateFileNameWithBasicInfo(
  fileNameInfo: { companyCode?: string; registerDate?: string },
  basicInfo: { companyCode: string; registerDate: string }
): {
  isValid: boolean;
  error?: {
    type: "COMPANY_MISMATCH" | "DATE_MISMATCH";
    message: string;
  };
} {
  // 默认校验通过
  const result = {
    isValid: true
  };
  
  // 如果文件名和基本信息中都有公司代码，进行校验
  if (fileNameInfo.companyCode && basicInfo.companyCode && 
      // 修复：在比较前对公司代码进行格式化处理，去除空格和不可见字符
      String(fileNameInfo.companyCode).trim() !== String(basicInfo.companyCode).trim()) {
    
    return {
      isValid: false,
      error: {
        type: "COMPANY_MISMATCH",
        message: `文件名中的公司代码(${fileNameInfo.companyCode})与名册中的公司代码(${basicInfo.companyCode})不一致`
      }
    };
  }
  
  // 如果文件名和基本信息中都有期数时间，进行校验
  if (fileNameInfo.registerDate && basicInfo.registerDate) {
    // 标准化日期格式为YYYY-MM-DD
    const fileNameDate = fileNameInfo.registerDate.replace(/\//g, '-');
    const basicInfoDate = basicInfo.registerDate.replace(/\//g, '-');
    
    // 比较日期，忽略格式差异
    const fileNameDateObj = new Date(fileNameDate);
    const basicInfoDateObj = new Date(basicInfoDate);
    
    if (!Number.isNaN(fileNameDateObj.getTime()) && !Number.isNaN(basicInfoDateObj.getTime()) && 
        fileNameDateObj.getTime() !== basicInfoDateObj.getTime()) {
      return {
        isValid: false,
        error: {
          type: "DATE_MISMATCH",
          message: `文件名中的期数时间(${fileNameInfo.registerDate})与名册中的权益登记日(${basicInfo.registerDate})不一致`
        }
      };
    }
  }
  
  return result;
}

/**
 * 从原始数据中提取SJLX=801的记录，获取股东数据
 * 
 * @param data 原始数据
 * @returns 股东数据记录数组
 * @version 1.0.0 (2025-06-04)
 * <AUTHOR> 2025-06-04 11:52:14
 */
function extractShareholderRecords(data: Record<string, any>[]): Record<string, any>[] {
  // 寻找SJLX=801的记录，这通常是股东数据
  const shareholderRecords = data.filter((record) => record.SJLX === 801 || record.SJLX === "801");
  
  // 如果找不到SJLX=801的记录，返回空数组
  if (!shareholderRecords || shareholderRecords.length === 0) {
    // 调试日志已移除 - 2025-06-24 19:17:24 hayden 问题已解决
    return [];
  }
  
  return shareholderRecords;
}

/**
 * 解析t3名册Excel文件
 * 
 * @param file t3名册Excel文件
 * @returns 解析结果
 * @update 2025-06-04 11:52:14 添加对SJLX=801（股东数据）的识别和处理
 * @update 2025-06-04 12:14:27 使用统一提取函数extractUnifiedCompanyInfo替换原有的extractCompanyBasicInfo函数
 * @update 2025-06-04 13:46:02 增强对所有字段的编码处理，确保中文正确显示
 */
export async function parseExcelFileT3(file: File): Promise<{
  success: boolean;
  fileName: string;
  companyCode?: string;
  registerDate?: string;
  records?: any[];
  recordCount?: number;
  companyInfo?: {
    companyName: string;
    totalShares: string;
    totalShareholders: number;
    totalInstitutions: number;
    largeSharesCount: string;
    largeShareholdersCount: number;
    institutionShares: string;
    /**
     * 新增信用股东统计字段
     *
     * <AUTHOR>
     * @created 2025-06-13 13:49:20
     * @description 支持T3名册的信用股东统计数据
     */
    marginAccounts?: number; // 信用总户数（全量数据统计）
    marginShares?: string; // 信用总持股（全量数据统计）
  };
  error?: {
    type: "FILE_ERROR" | "COMPANY_MISMATCH" | "DATE_MISMATCH" | "MISSING_FIELDS" | "EMPTY_FIELDS";
    message: string;
    details?: string[];
  };
}> {
  try {
		// 读取Excel文件
		const { worksheet } = await readExcelWorksheet(file);

		// 将工作表转换为JSON对象数组，使用默认选项
		const rawData = XLSX.utils.sheet_to_json(worksheet, {
			raw: true,
			defval: null, // 空单元格的默认值
			blankrows: false, // 跳过空行
		}) as Record<string, any>[];

		// 如果没有数据，返回错误
		if (!rawData || rawData.length === 0) {
			return {
				success: false,
				fileName: file.name,
				error: {
					type: "FILE_ERROR",
					message: "Excel文件不包含有效数据",
				},
			};
		}

		// 分析文件名，提取公司代码和报告日期
		const fileNameInfo = parseExcelFileName(file.name);

		// 从原始数据中提取SJLX=802的公司基本信息
		// 使用统一提取函数，指定名册类型为TYPE_T3
		const companyBasicInfo = extractUnifiedCompanyInfo(
			rawData as Record<string, any>[],
			'TYPE_T3'
		);

		// 校验文件名中的公司代码和期数时间与SJLX=802记录中的信息是否一致
		const validationResult = validateFileNameWithBasicInfo(
			fileNameInfo,
			companyBasicInfo,
		);
		if (!validationResult.isValid && validationResult.error) {
			return {
				success: false,
				fileName: file.name,
				error: validationResult.error,
			};
		}

		// 从原始数据中提取SJLX=801的股东数据记录
		const shareholderRecordsFrom801 = extractShareholderRecords(
			rawData as Record<string, any>[],
		);

		// 尝试识别特殊记录（通常是表头或汇总行）
		const specialRecords = identifySpecialRecords(
			rawData as Record<string, any>[],
		);
		
		// 如果存在SJLX=801的股东数据记录，优先使用这些记录
		let regularRecords: Record<string, any>[] = [];
		if (shareholderRecordsFrom801.length > 0) {

			regularRecords = shareholderRecordsFrom801;
		} else {
			// 如果没有SJLX=801的记录，则使用常规方法提取记录

			regularRecords = extractRegularRecords(rawData, specialRecords);
		}

		/**
		 * 检查是否有有效的记录数据
		 * <AUTHOR>
		 * @created 2025-07-01 10:29:07
		 * @description 在进行字段校验之前，确保有有效的记录数据
		 */
		if (!regularRecords || regularRecords.length === 0) {
			return {
				success: false,
				fileName: file.name,
				error: {
					type: "FILE_ERROR",
					message: "Excel文件中没有找到有效的股东记录数据",
				},
			};
		}

		// 第一步：进行基础字段映射，但不设置默认值
		const mappedRecords = regularRecords.map((record) => {
			const mappedRecord: Record<string, any> = {};

			// 复制原始记录的所有字段
			for (const key in record) {
				if (Object.prototype.hasOwnProperty.call(record, key)) {
					// 保留原始字段
					mappedRecord[key] =
						typeof record[key] === "string"
							? record[key].trim()
							: record[key];

					// 如果字段名在映射表中，同时添加标准字段名
					const standardKey = T3_FIELD_MAPPING[key];
					if (standardKey && standardKey !== key) {
						mappedRecord[standardKey] =
							typeof record[key] === "string"
								? record[key].trim()
								: record[key];
					}
				}
			}

			/**
			 * 处理字段映射，但不设置默认值
			 * @update 2025-07-01 10:29:07 hayden 修复字段校验问题
			 * 将字段映射和默认值设置分离，确保字段校验能正确工作
			 */
			// 强制确保GDLB映射到CYRLB（防止映射逻辑失效）
			if (mappedRecord.GDLB && !mappedRecord.CYRLB) {
				mappedRecord.CYRLB =
					typeof mappedRecord.GDLB === "string"
						? mappedRecord.GDLB.trim()
						: mappedRecord.GDLB;
			}

			return mappedRecord;
		});

		/**
		 * 再次检查映射后的记录是否为空
		 * <AUTHOR>
		 * @created 2025-07-01 10:29:07
		 * @description 确保映射处理后仍有有效记录
		 */
		if (!mappedRecords || mappedRecords.length === 0) {
			return {
				success: false,
				fileName: file.name,
				error: {
					type: "FILE_ERROR",
					message: "Excel文件字段映射后没有有效的股东记录数据",
				},
			};
		}

		// 第二步：检查是否包含必要字段（在设置默认值之前）
		const missingFields = checkRequiredFields(
			mappedRecords[0],
			REQUIRED_FIELDS_T3,
		);

		if (missingFields.length > 0) {
			return {
				success: false,
				fileName: file.name,
				error: {
					type: "MISSING_FIELDS",
					message: `名册中缺少字段"${missingFields.join('", "')}"`,
					details: missingFields,
				},
			};
		}

		// 第三步：设置默认值并进行最终标准化
		const standardizedRecords = mappedRecords.map((record) => {
			const standardRecord = { ...record };

			// 确保必填字段有默认值（仅在字段存在但值为空时设置）
			if (standardRecord.YMTZHHM === undefined || standardRecord.YMTZHHM === null || standardRecord.YMTZHHM === "") {
				standardRecord.YMTZHHM = "未提供";
			}

			if (standardRecord.CYRMC === undefined || standardRecord.CYRMC === null || standardRecord.CYRMC === "") {
				standardRecord.CYRMC = "未提供";
			}

			if (standardRecord.ZJHM === undefined || standardRecord.ZJHM === null || standardRecord.ZJHM === "") {
				standardRecord.ZJHM = "未提供";
			}

			if (standardRecord.CYRLB === undefined || standardRecord.CYRLB === null || standardRecord.CYRLB === "") {
				standardRecord.CYRLB = "未提供";
			}

			// 计算总持股数量（如果不存在）
			if (
				!standardRecord.ZCYSL &&
				(standardRecord.PTZQZHCYSL || standardRecord.XYCYSL)
			) {
				const ptShares = Number(standardRecord.PTZQZHCYSL || 0);
				const xyShares = Number(standardRecord.XYCYSL || 0);
				standardRecord.ZCYSL = (ptShares + xyShares).toString();
			}

			return standardRecord;
		});

		/**
		 * 再次检查标准化后的记录是否为空
		 * <AUTHOR>
		 * @created 2025-07-01 10:29:07
		 * @description 确保标准化处理后仍有有效记录
		 */
		if (!standardizedRecords || standardizedRecords.length === 0) {
			return {
				success: false,
				fileName: file.name,
				error: {
					type: "FILE_ERROR",
					message: "Excel文件处理后没有有效的股东记录数据",
				},
			};
		}

		// 检查必填字段是否有空值
		const emptyFields = validateRequiredFieldsContent(
			standardizedRecords,
			REQUIRED_FIELDS_T3,
		);
		if (emptyFields.length > 0) {
			return {
				success: false,
				fileName: file.name,
				error: {
					type: "EMPTY_FIELDS",
					message: `名册中字段"${emptyFields.join('", "')}"缺少内容`,
					details: emptyFields,
				},
			};
		}

		// 检查是否包含参照表中的所有字段
		const missingReferenceFields = checkT3FieldsCompleteness(
			standardizedRecords[0],
		);
		if (missingReferenceFields.length > 0) {
			// 调试日志已移除 - 2025-06-24 19:17:24 hayden 问题已解决
		}

		/**
		 * 计算信用股东统计数据（全量数据）
		 *
		 * <AUTHOR>
		 * @created 2025-06-13 12:54:01
		 * @description 根据T3名册的XYCYSL字段统计信用总户数和信用总持股
		 * @update 2025-06-13 13:15:00 修正计算逻辑，在预处理之前使用standardizedRecords计算
		 */
		let marginAccounts = 0;
		let marginShares = 0;

		// 统计全量数据的信用股东信息（在预处理之前计算）
		standardizedRecords.forEach(record => {
			/**
			 * 处理XYCYSL字段的各种情况
			 *
			 * <AUTHOR>
			 * @created 2025-06-13 13:20:00
			 * @description 处理XYCYSL字段可能为空白、null、undefined、0等情况
			 * @update 2025-06-13 13:49:20 修改统计逻辑，统计全部股东（包括XYCYSL为0或空白的情况）
			 */
			const xycyslRaw = record.XYCYSL;
			let xycyslValue = 0;

			// 处理各种空值情况
			if (xycyslRaw !== null && xycyslRaw !== undefined && xycyslRaw !== '') {
				// 如果是字符串，先清理空白字符
				const cleanValue = typeof xycyslRaw === 'string' ? xycyslRaw.trim() : xycyslRaw;
				if (cleanValue !== '') {
					xycyslValue = Number(cleanValue);
					// 确保转换后的数值是有效的
					if (Number.isNaN(xycyslValue)) {
						xycyslValue = 0;
					}
				}
			}

			/**
			 * 修改统计逻辑：只统计XYCYSL大于0的股东
			 *
			 * <AUTHOR>
			 * @created 2025-06-13 13:49:20
			 * @description 根据需求，marginAccounts和marginShares需要排除XYCYSL为0的情况
			 * @update 2025-06-17 19:12:37 修改统计逻辑，排除XYCYSL为0的情况，只统计有信用持股的股东
			 */
			// 原逻辑：marginAccounts += 1; marginShares += xycyslValue; // 统计全部股东
			// 新逻辑：只统计XYCYSL大于0的股东
			if (xycyslValue > 0) {
				marginAccounts += 1; // 信用总户数（只统计XYCYSL > 0的股东）
				marginShares += xycyslValue; // 信用总持股（只累加XYCYSL > 0的值）
			}
		});

		// 提取公司信息
		const companyInfoFromSpecial = extractCompanyInfo(specialRecords);

		// 合并公司信息，绝对使用SJLX=802的信息
		const companyInfo = {
			// 绝对使用SJLX=802的信息，只有在SJLX=802没有提供信息时才使用其他来源
			companyName:
				companyBasicInfo.companyName ||
				companyInfoFromSpecial.companyName,
			companyCode:
				companyBasicInfo.companyCode ||
				companyInfoFromSpecial.companyCode,
			registerDate:
				companyBasicInfo.registerDate ||
				fileNameInfo.registerDate ||
				companyInfoFromSpecial.registerDate,
			totalShares:
				companyBasicInfo.totalShares ||
				companyInfoFromSpecial.totalShares,
			totalShareholders:
				companyBasicInfo.totalShareholders ||
				companyInfoFromSpecial.totalShareholders,
			totalInstitutions:
				companyBasicInfo.totalInstitutions ||
				companyInfoFromSpecial.totalInstitutions,
			institutionShares:
				companyBasicInfo.institutionShares ||
				companyInfoFromSpecial.institutionShares,
			largeSharesCount: companyInfoFromSpecial.largeSharesCount,
			largeShareholdersCount:
				companyInfoFromSpecial.largeShareholdersCount,
		};

		// 处理记录，清理字段值，但保留所有原始字段
		const processedRecords = standardizedRecords.map((record) => {
			const processedRecord: Record<string, any> = {};

			/**
			 * 数值字段列表 - 需要转换为字符串类型
			 *
			 * <AUTHOR>
			 * @created 2025-06-24 15:34:59
			 * @description 确保所有数值字段都转换为字符串类型，避免后端验证失败
			 * 包括t3名册的所有可能的数值字段
			 */
			const numericFields = [
				'ZCYSL',       // 总持股数量
				'PTZQZHCYSL',  // 普通账户持股数量
				'XYCYSL',      // 信用账户持股数量
				'CYBL',        // 持股比例
				'ZYDJZS',      // 质押冻结股数
				'GPNF',        // 股票年份
				'XH'           // 序号
			];

			// 处理每个字段
			for (const key in record) {
				if (Object.prototype.hasOwnProperty.call(record, key)) {
					// 如果是字符串类型，清理字符串并修复编码
					if (typeof record[key] === "string") {
						processedRecord[key] = cleanString(fixEncoding(record[key]));
					} else if (numericFields.includes(key) && record[key] !== undefined && record[key] !== null) {
						// 数值字段转换为字符串
						processedRecord[key] = String(record[key]);
					} else {
						processedRecord[key] = record[key];
					}
				}
			}

			/**
			 * 再次确保GDLB映射到CYRLB（防止在处理过程中丢失）
			 * @update 2025-06-24 19:05:56 hayden 修复字段映射在处理过程中丢失的问题
			 */
			if (processedRecord.GDLB && !processedRecord.CYRLB) {
				processedRecord.CYRLB = processedRecord.GDLB;
			}

			// 确保所有参照表字段都有值（即使是空值）
			for (const field of T3_ALL_FIELDS) {
				if (
					!Object.prototype.hasOwnProperty.call(
						processedRecord,
						field,
					)
				) {
					processedRecord[field] = "";
				}
			}

			return processedRecord;
		});

		// 预处理：合并重复记录
		const mergedRecords = preprocessT3Records(processedRecords);

		/**
		 * 按XYCYSL字段降序排序并取前200个股东
		 *
		 * <AUTHOR>
		 * @created 2025-06-13 12:54:01
		 * @description 根据需求，只上传XYCYSL字段值较大的前200个股东
		 */
		const sortedRecords = [...mergedRecords].sort((a, b) => {
			const xycyslA = Number(a.XYCYSL || 0);
			const xycyslB = Number(b.XYCYSL || 0);
			return xycyslB - xycyslA; // 降序排序
		});

		// 只保留前MAX_RECORDS_COUNT条记录
		const limitedRecords = sortedRecords.slice(0, MAX_RECORDS_COUNT);

		return {
			success: true,
			fileName: file.name,
			companyCode: companyInfo.companyCode,
			registerDate: companyInfo.registerDate,
			records: limitedRecords,
			recordCount: limitedRecords.length,
			companyInfo: {
				...companyInfo,
				/**
				 * 新增信用股东统计字段
				 *
				 * <AUTHOR>
				 * @created 2025-06-13 12:54:01
				 * @description 基于全量数据计算的信用股东统计信息
				 */
				marginAccounts, // 信用总户数（全量数据统计）
				marginShares: marginShares.toString(), // 信用总持股（全量数据统计）
			},
		};
  } catch (error) {
    console.error('解析Excel文件失败:', error);
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: error instanceof Error ? error.message : "解析Excel文件失败",
      },
    };
  }
}

/**
 * 预处理t3名册数据
 * 根据ZJHM(证件号码)和YMTZHHM(一码通账户号码)作为组合键进行记录匹配和合并
 * 
 * @param records t3名册记录
 * @returns 预处理后的记录
 * @update 2025-06-04 13:46:02 增强对所有字段的编码处理，确保中文正确显示
 */
export function preprocessT3Records(records: any[]): any[] {
  if (!records || records.length === 0) {
    return [];
  }
  
  // 使用Map存储合并后的记录，键为"ZJHM+YMTZHHM"
  const mergedRecordsMap = new Map<string, any>();
  
  // 遍历所有记录
  for (const record of records) {
    // 处理所有字段的编码问题
    const processedRecord: Record<string, any> = {};
    for (const key in record) {
      if (Object.prototype.hasOwnProperty.call(record, key)) {
        // 对所有字符串类型的字段应用fixEncoding
        if (typeof record[key] === 'string') {
          processedRecord[key] = fixEncoding(record[key]);
        } else {
          processedRecord[key] = record[key];
        }
      }
    }
    
    // 生成组合键
    const zjhm = processedRecord.ZJHM || '';
    const ymtzhhm = processedRecord.YMTZHHM || '';
    const key = `${zjhm}|${ymtzhhm}`;
    
    // 如果Map中已存在该键，合并记录
    if (mergedRecordsMap.has(key)) {
      const existingRecord = mergedRecordsMap.get(key);
      
      // 叠加普通持股数量
      const existingPtShares = Number(existingRecord.PTZQZHCYSL || 0);
      const newPtShares = Number(processedRecord.PTZQZHCYSL || 0);
      existingRecord.PTZQZHCYSL = (existingPtShares + newPtShares).toString();
      
      // 叠加信用持股数量
      const existingXyShares = Number(existingRecord.XYCYSL || 0);
      const newXyShares = Number(processedRecord.XYCYSL || 0);
      existingRecord.XYCYSL = (existingXyShares + newXyShares).toString();
      
      // 叠加总持股数量
      const existingTotalShares = Number(existingRecord.ZCYSL || 0);
      const newTotalShares = Number(processedRecord.ZCYSL || 0);
      existingRecord.ZCYSL = (existingTotalShares + newTotalShares).toString();
      
      // 其他字段保留先到先得的值
    } else {
      // 如果Map中不存在该键，添加记录
      mergedRecordsMap.set(key, { ...processedRecord });
    }
  }
  
  // 将Map转换为数组
  return Array.from(mergedRecordsMap.values());
}

/**
 * 合并多个t3名册的记录
 * 
 * @param recordsArray 多个t3名册记录数组
 * @returns 合并后的记录
 */
export function mergeT3Records(recordsArray: any[][]): any[] {
  // 将所有记录合并为一个数组
  const allRecords = recordsArray.flat();
  
  // 使用预处理函数合并记录
  return preprocessT3Records(allRecords);
} 