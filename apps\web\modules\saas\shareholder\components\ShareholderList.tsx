"use client";

import { useEffect, useState, useMemo } from "react";
import { useShareholders } from "../hooks/useShareholders";
import { useOrganization } from "@saas/organizations/hooks/useOrganization";
import { ShareholderFilter } from "@saas/shareholder/components/ShareholderFilter";
import { ShareholderTable } from "@saas/shareholder/components/ShareholderTable";
import { ImportShareholderRegistryDialog } from "@saas/shareholder/components/dialogs/ImportShareholderRegistryDialog";
import { toast } from "sonner";
import { ShareholderRegistrySkeleton } from "@saas/shareholder/components/ShareholderRegistrySkeleton";
import type { ColumnOption } from "@saas/shareholder/components/ColumnFilter";

interface ShareholderListProps {
  organizationSlug: string;
}

/**
 * 股东列表组件
 * 集成了股东数据的获取、显示、筛选和导入功能
 * 
 * @version 6.0.0 (2025-05-27) - 添加预设视图功能，支持快速切换默认视图和全量视图
 * @version 5.0.0 (2025-05-24) - 添加表格列筛选功能，支持自定义显示列
 * @version 4.0.0 (2025-05-21) - 使用更新后的ShareholderTable组件，统一UI风格和组件复用
 * @version 3.0.0 (2025-05-21) - 使用基于Ant Design的表格组件替代原有表格，支持更多功能扩展
 * @version 2.2.0 (2025-05-19) - 子组件现采用增强版useSystemScale hook进行统一样式适配
 * @version 2.1.0 (2025-05-19) - 更新子组件以支持系统缩放调整，优化不同缩放级别下的显示效果
 * @version 2.0.0 (2025-05-19) - 更新以适配移除移动端逻辑后的子组件
 */
export function ShareholderList({ organizationSlug }: ShareholderListProps) {
  // 获取组织信息
  const { data: organization, isLoading: isLoadingOrg } = useOrganization(organizationSlug);
  
  // 导入对话框状态
  const [isImportOpen, setIsImportOpen] = useState(false);
  
  // 获取股东列表数据
	const {
		shareholders,
		pagination,
		isLoading,
		error,
		page,
		limit,
		searchTerm,
		sortBy,
		sortOrder,
		registerDate,
		registerDates,
		isLoadingRegisterDates,
		setPage,
		setLimit,
		setSearchTerm,
		setSortBy,
		setSortOrder,
		setRegisterDate,
		refetch,
		refreshRegisterDates,
	} = useShareholders(organization?.id || "");

  // 表格列选项配置
  const columnOptions = useMemo<ColumnOption[]>(() => [
    { key: "rank", title: "持股排名", locked: true },
    { key: "name", title: "账户名称", locked: true },
    { key: "identifier", title: "证件号码" },
    { key: "percentage", title: "持股比例" },
    { key: "shares", title: "持股数量" },
    { key: "contactNumber", title: "手机号码" },
    { key: "type", title: "股东类型" },
    { key: "account", title: "一码通号码" },
    { key: "lockedShares", title: "锁定股份" },
    { key: "frozenShares", title: "冻结股份" },
    { key: "contactAddress", title: "联系地址" },
    { key: "zipCode", title: "邮政编码" },
    { key: "cashAccount", title: "现金账户" },
    { key: "sharesInCashAccount", title: "现金账户股份" },
    { key: "marginAccount", title: "融资账户" },
    { key: "sharesInMarginAccount", title: "融资账户股份" },
    { key: "relatedParty", title: "关联方标识" },
    { key: "clientCategory", title: "客户类别" },
    { key: "shareholderType", title: "股东类型" },
    { key: "marginCollateralAccountNumber", title: "汇总账户号码" },
    { key: "marginCollateralAccountName", title: "汇总账户名称" },
    { key: "natureOfShares", title: "股份性质" },
    { key: "rightsCategory", title: "权利类别" },
    { key: "shareTradingCategory", title: "股份交易类别" },
    { key: "remarks", title: "备注" },
  ], []);
  
  // 默认显示列
  const defaultColumns = useMemo(() => {
    return [
					"rank",
					"name",
					"identifier",
					"account",
					"tags",
					"percentage",
					"shares",
					"shareholderType",
				];
  }, []);
  
  // 全量显示列 - 所有可用字段
  const fullColumns = useMemo(() => {
    return columnOptions.map(col => col.key);
  }, [columnOptions]);

  // 表格列配置与可见列状态
  const [visibleColumns, setVisibleColumns] = useState<string[]>(defaultColumns);
  
  // 预设配置选项
  const presetOptions = useMemo(() => {
    return [
      { key: "default", title: "默认视图", columns: defaultColumns },
      { key: "full", title: "全量视图", columns: fullColumns }
    ];
  }, [defaultColumns, fullColumns]);
  
  // 处理预设配置切换
  const handlePresetChange = (presetKey: string) => {
    const preset = presetOptions.find(p => p.key === presetKey);
    if (preset) {
      setVisibleColumns(preset.columns);
    }
  };
  
  // 错误处理
  useEffect(() => {
    if (error) {
      toast.error(`获取股东数据失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }, [error]);
  
  // 处理搜索
  const handleSearch = (term?: string) => {
    setSearchTerm(term);
    setPage(1); // 重置到第一页
  };
  
  // 处理排序
  const handleSort = (column: string, order?: 'asc' | 'desc') => {
    // 验证参数有效性
    if (!column) {
      return;
    }
    
    // 所有列使用相同的排序逻辑，因为rank列的特殊处理已经在ant-shareholder-table组件中完成
    if (sortBy === column && !order) {
      // 如果是相同列但没有指定排序方向，切换排序方向
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else if (order) {
      // 如果指定了排序方向，直接使用
      setSortBy(column);
      setSortOrder(order);
    } else {
      // 新的排序列且没有指定排序方向
      // 对所有列使用统一的默认排序方向
      setSortBy(column);
      setSortOrder('desc');
    }
  };
  
  // 处理重置排序
  const handleResetSort = () => {
    // 重置为默认排序：按持股数量降序
    setSortBy("numberOfShares");
    setSortOrder("desc");
    // 同时重置搜索条件
    setSearchTerm(undefined);
    // 重置页码
    setPage(1);
  };
  
  // 处理导入对话框打开
  const handleImport = () => {
    setIsImportOpen(true);
  };
  
  // 处理导入对话框关闭
  const handleImportClose = (open: boolean) => {
    setIsImportOpen(open);
    if (!open) {
      // 刷新数据 - 先刷新期数日期列表，再刷新股东数据
      refreshRegisterDates(); // 先刷新期数日期列表
      // 延迟200ms后再刷新股东数据，确保期数日期列表已更新
      setTimeout(() => {
        refetch();
      }, 200);
    }
  };

  // 处理列筛选变化
  const handleVisibleColumnsChange = (columns: string[]) => {
    setVisibleColumns(columns);
  };
  
  // 处理筛选对话框
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false);
  
  // 加载状态或未选择报告日期
  if (isLoadingOrg) {
    return <ShareholderRegistrySkeleton />;
  }
  
  if (!organization?.id) {
    return <div className="text-center py-8">无法获取组织信息</div>;
  }
  
  return (
		<div className="space-y-4">
			{/* 过滤器组件 - 支持系统缩放响应 */}
			<ShareholderFilter
				registerDate={registerDate}
				registerDates={registerDates}
				isLoadingRegisterDates={isLoadingRegisterDates}
				onRegisterDateChange={setRegisterDate}
				searchTerm={searchTerm}
				onSearch={handleSearch}
				isLoading={isLoading}
				onImport={handleImport}
				// 列筛选相关属性
				columnOptions={columnOptions}
				visibleColumns={visibleColumns}
				onVisibleColumnsChange={handleVisibleColumnsChange}
				defaultColumns={defaultColumns}
				presetOptions={presetOptions}
				onPresetChange={handlePresetChange}
			/>

			{/* 使用更新后的ShareholderTable组件 */}
			<ShareholderTable
				shareholders={shareholders}
				pagination={pagination}
				isLoading={isLoading}
				page={page}
				limit={limit}
				sortBy={sortBy}
				sortOrder={sortOrder}
				onSort={handleSort}
				onPageChange={setPage}
				onLimitChange={setLimit}
				searchTerm={searchTerm}
				onResetSort={handleResetSort}
				visibleColumns={visibleColumns}
			/>

			{/* 导入对话框 */}
			<ImportShareholderRegistryDialog
				isOpen={isImportOpen}
				organizationId={organization?.id || ""}
				onOpenChange={handleImportClose}
			/>
		</div>
	);
} 