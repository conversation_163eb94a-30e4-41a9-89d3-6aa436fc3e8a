/**
 * 股东名册分析 - 结构趋势分析骨架屏组件
 * @file StructureTrendSkeleton.tsx
 * @description 用于展示结构趋势分析数据加载时的骨架屏，与 StructureTrend 组件布局完全一致
 * <AUTHOR>
 * @created 2025-06-16 17:26:26
 */

"use client";

import { TrendingUpIcon } from "lucide-react";
import { Skeleton } from "@ui/components/skeleton";

/**
 * 图表骨架屏组件
 * @param title 图表标题
 * @returns 图表骨架屏UI组件
 * <AUTHOR>
 * @created 2025-06-16 17:26:26
 * @updated 2025-06-16 17:36:24 - 修复 Hydration 错误，使用固定高度替代 Math.random()
 * @updated 2025-06-18 17:33:45 - 优化骨架屏，使其更像实际的组合图表（柱状图+线条）
 */
function ChartSkeleton({ title }: { title: string }): JSX.Element {
	// 使用固定的高度数组替代 Math.random()，避免 SSR 和客户端渲染不一致
	const fixedHeights = [45, 72, 38, 85, 56];

	return (
		<div className="rounded-lg bg-card p-3 pb-2 shadow-sm border border-border">
			<h4 className="mb-4 font-medium text-sm text-foreground/80">
				{title}
			</h4>
			<div className="h-40">
				{/* 图表内容骨架 */}
				<div className="h-full flex flex-col">
					{/* Y轴标签骨架 */}
					<div className="flex justify-between items-start mb-2">
						<div className="flex flex-col gap-2 w-12">
							<Skeleton className="h-2 w-8" />
							<Skeleton className="h-2 w-6" />
							<Skeleton className="h-2 w-10" />
							<Skeleton className="h-2 w-4" />
						</div>
					</div>

					{/* 图表区域 */}
					<div className="flex-1 flex flex-col justify-end relative">
						{/* 柱状图和X轴标签骨架 */}
						<div className="flex justify-between items-end mb-2 px-2">
							{Array.from({ length: 5 }).map((_, index) => (
								<div key={index} className="flex flex-col items-center">
									{/* 柱状图骨架 - 使用更接近实际的宽度 */}
									<Skeleton
										className="w-5 mb-1 rounded-t-sm"
										style={{
											height: `${fixedHeights[index]}px`
										}}
									/>
									{/* X轴标签骨架 */}
									<Skeleton className="h-3 w-12" />
								</div>
							))}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}

/**
 * 股东类别行骨架屏组件
 * @param categoryName 类别名称
 * @param showTooltip 是否显示提示图标
 * @returns 类别行骨架屏UI组件
 * <AUTHOR>
 * @created 2025-06-16 17:26:26
 */
function CategoryRowSkeleton({ 
	categoryName, 
	showTooltip = false 
}: { 
	categoryName: string; 
	showTooltip?: boolean; 
}): JSX.Element {
	return (
		<div className="mb-6">
			<div className="flex items-center gap-2 mb-4">
				<h3 className="text-base font-semibold text-foreground">{categoryName}</h3>
				{showTooltip && (
					<Skeleton className="h-4 w-4 rounded-full" />
				)}
			</div>
			<div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
				<ChartSkeleton title="户数" />
				<ChartSkeleton title="持股" />
			</div>
		</div>
	);
}

/**
 * 结构趋势分析骨架屏组件
 * @returns 结构趋势分析骨架屏UI组件
 * <AUTHOR>
 * @created 2025-06-16 17:26:26
 */
export function StructureTrendSkeleton(): JSX.Element {
	// 股东类别配置数据，与StructureTrend.tsx保持一致
	const categories = [
		{ name: "全部股东", showTooltip: false },
		{ name: "信用股东", showTooltip: true },
		{ name: "个人股东", showTooltip: false },
		{ name: "机构股东", showTooltip: false },
		{ name: "细分机构股东", showTooltip: false },
		{ name: "持股集中度", showTooltip: false },
	];

	return (
		<div className="w-full">
			<div className="flex items-center gap-2 mb-6">
				<TrendingUpIcon className="size-5 text-primary" />
				<h2 className="text-lg font-semibold">股东结构趋势分析</h2>
			</div>
			<div className="space-y-6">
				{categories.map((category, index) => (
					<CategoryRowSkeleton 
						key={index} 
						categoryName={category.name}
						showTooltip={category.showTooltip}
					/>
				))}
			</div>
		</div>
	);
}
