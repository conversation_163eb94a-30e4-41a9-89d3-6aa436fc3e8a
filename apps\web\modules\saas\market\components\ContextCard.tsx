import React, { useState } from "react";
import { cn } from "@ui/lib";
import type { FundCard } from "../lib/TestData";
import { InvestorDialog } from "./Dialog";
import { fetchFundDetail } from "../lib/html_dialog";
import { Star } from "lucide-react";

/**
 * ContextCard 组件 - 基金详情卡片和弹窗组件
 * <AUTHOR>
 * @modified 2025-07-03 15:01:58 - 集成 ShadowHtmlRenderer 组件使用 Shadow DOM 完全隔离样式，保持原始布局
 * @modified 2025-07-03 16:08:10 - 替换为 LargeDialog 大容器弹窗组件，支持更大的显示区域和更好的用户体验
 * @modified 2025-07-04 - 增加投资人收藏功能
 */


export default function ContextCard({ filtered }: { filtered: FundCard[] }) {
  // 添加收藏状态 - 为每个卡片维护独立的收藏状态
  const [favoriteCards, setFavoriteCards] = useState<Set<string>>(new Set());

  const [open, setOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState<FundCard | null>(null);
  const [html, setHtml] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // 处理收藏功能
  const handleToggleFavorite = (cardCode: string, e?: React.MouseEvent) => {
    e?.stopPropagation();
    setFavoriteCards(prev => {
      const newSet = new Set(prev);
      if (newSet.has(cardCode)) {
        newSet.delete(cardCode);
      } else {
        newSet.add(cardCode);
      }
      return newSet;
    });
  };

  const handleCardClick = async (card: FundCard) => {
    setSelectedCard(card);
    setOpen(true);
    setLoading(true);
    setError("");
    setHtml("");
    const result = await fetchFundDetail(card);
    if (result.html) {
      setHtml(result.html);
    } else {
      setError(result.error || "暂无内容");
    }
    setLoading(false);
  };

  const handleRefresh = async () => {
    if (!selectedCard) {return;}
    setLoading(true);
    setError("");
    setHtml("");
    const result = await fetchFundDetail(selectedCard);
    if (result.html) {
      setHtml(result.html);
    } else {
      setError(result.error || "暂无内容");
    }
    setLoading(false);
  };

  // 只渲染卡牌区，不再管理 search、弹窗等状态
		return (
			<>
				<div className="grid gap-6 grid-cols-[repeat(auto-fit,minmax(270px,1fr))]">
					{filtered.map((f) => (
						<button
							key={f.code}
							type="button"
							className="rounded-xl shadow bg-white dark:bg-zinc-900 p-6 flex flex-col transition hover:shadow-lg cursor-pointer appearance-none border-0 text-left relative h-[312.67px]"
							onClick={() => handleCardClick(f)}
							onKeyUp={(e) => {
								if (e.key === "Enter" || e.key === " ") {
									handleCardClick(f);
								}
							}}
						>
							{/* 右上角Star图标 */}
							<Star
								className={cn(
									"absolute right-6 top-6 w-7 h-7 cursor-pointer transform -translate-y-0.49",
									favoriteCards.has(f.code)
										? "fill-yellow-500 text-yellow-500 dark:fill-yellow-400 dark:text-yellow-400"
										: "fill-none text-yellow-500 dark:text-stone-400",
								)}
								onClick={(e) => handleToggleFavorite(f.code, e)}
							/>

							{/* 标题 */}
							<div className="font-bold text-2xl text-gray-900 dark:text-gray-100 mb-2">
								{f.name || "-"}
							</div>

							{/* 基金代码 */}
							<div className="text-xs text-gray-500 mb-2">
								基金代码：{f.code || "-"}
							</div>

							{/* 主要信息区纵向排列 */}
							<div className="flex flex-col gap-2 flex-1 mt-1">
								<div className="flex items-center gap-1">
									<span className="font-medium text-gray-700 text-sm">
										基金类型：
									</span>
									<span className="text-md text-blue-700">
										{f.type || "-"}
									</span>
								</div>
								<div className="flex items-center gap-1">
									<span className="font-medium text-gray-700 text-sm">
										成立时间：
									</span>
									<span className="text-sm text-gray-500">
										{f.date || "-"}
									</span>
								</div>
								<div className="flex items-center gap-1">
									<span className="font-medium text-gray-700 text-sm">
										总资产规模：
									</span>
									<span className="text-sm text-gray-500">
										{f.scale || "-"}
									</span>
								</div>
								<div className="flex items-center gap-1">
									<span className="font-medium text-gray-700 text-sm">
										基金净值：
									</span>
									<span
										className={cn(
											"font-medium text-md",
											f.yield?.startsWith("+")
												? "text-red-500"
												: f.yield?.startsWith("-")
													? "text-green-500"
													: "text-gray-900 dark:text-gray-100",
										)}
									>
										{f.yield || "-"}
									</span>
								</div>
								<div className="flex items-center gap-1">
									<span className="font-medium text-gray-700 text-sm">
										基金经理：
									</span>
									<span className="text-sm text-gray-400">
										{f.manager || "-"}
									</span>
								</div>
								<div className="flex items-center gap-1">
									<span className="font-medium text-gray-700 text-sm">
										基金公司：
									</span>
									<span className="text-sm text-gray-500">
										{f.company || "-"}
									</span>
								</div>
							</div>
							{/* 底部标签 */}
							<div className="mt-auto pt-2.5 pb-1.5 -ml-0.5">
								<span
									className={cn(
										"inline-block bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full",
										f.tag === "持仓本司"
											? "bg-green-100 text-green-600"
											: f.tag === "持仓对标"
												? "bg-orange-100 text-orange-600"
												: "",
									)}
								>
									{f.tag}
								</span>
							</div>
						</button>
					))}
				</div>
				{/* 弹窗 - 使用 InvestorDialog 组件 */}
				<InvestorDialog
					open={open}
					onOpenChange={setOpen}
					selectedCard={selectedCard}
					html={html}
					loading={loading}
					error={error}
					isFavorite={
						selectedCard
							? favoriteCards.has(selectedCard.code)
							: false
					}
					onToggleFavorite={() => {
						if (selectedCard) {
							handleToggleFavorite(selectedCard.code);
						}
					}}
					onRefresh={handleRefresh}
				/>
			</>
		);
}


