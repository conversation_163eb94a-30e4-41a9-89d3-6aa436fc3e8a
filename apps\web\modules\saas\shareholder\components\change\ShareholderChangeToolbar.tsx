"use client";

import { Input } from "@ui/components/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@ui/components/select";
import { Button } from "@ui/components/button";
import { RefreshCcw, Search as SearchIcon, X as XIcon } from "lucide-react";
import { cn } from "@ui/lib";
import { useState, useEffect, useCallback, useRef } from "react";
import { useKeyPress } from "@saas/shareholder/hooks/useKeyPress";
import { useSystemScale } from "@saas/shareholder/hooks/useSystemScale";

/**
 * 股东持股变化工具栏组件属性接口
 * 
 * @interface ShareholderChangeToolbarProps
 * @property {string} startDate - 开始日期
 * @property {string} endDate - 结束日期
 * @property {string} shareholderType - 股东类型
 * @property {string} searchTerm - 搜索关键词
 * @property {string[]} [availableDates] - 可用日期列表
 * @property {string[]} [shareholderTypes] - 可用股东类型列表
 * @property {(startDate: string) => void} onStartDateChange - 开始日期变更回调
 * @property {(endDate: string) => void} onEndDateChange - 结束日期变更回调
 * @property {(type: string) => void} onShareholderTypeChange - 股东类型变更回调
 * @property {(term: string) => void} onSearchChange - 搜索关键词变更回调
 * @property {() => void} onRefresh - 刷新按钮点击回调
 * @property {boolean} [isLoading] - 是否正在加载数据
 */
interface ShareholderChangeToolbarProps {
  startDate: string;
  endDate: string;
  shareholderType: string;
  searchTerm: string;
  availableDates?: string[];
  shareholderTypes?: string[];
  onStartDateChange: (startDate: string) => void;
  onEndDateChange: (endDate: string) => void;
  onShareholderTypeChange: (type: string) => void;
  onSearchChange: (term: string) => void;
  onRefresh: () => void;
  isLoading?: boolean;
}

/**
 * 股东持股变化工具栏组件
 * 提供日期范围选择、股东类型筛选、搜索和刷新功能
 * 
 * <AUTHOR>
 * @created 2024-06-28 16:23:45.621
 * @modified 2025年06月11日 18:15:43.455
 * @param {ShareholderChangeToolbarProps} props - 组件属性
 * @returns {JSX.Element} 股东持股变化工具栏组件
 * @time 2025年06月11日 18:15:43.455
 */
export function ShareholderChangeToolbar({
  startDate,
  endDate,
  shareholderType,
  searchTerm,
  availableDates = [],
  shareholderTypes = [],
  onStartDateChange,
  onEndDateChange,
  onShareholderTypeChange,
  onSearchChange,
  onRefresh,
  isLoading
}: ShareholderChangeToolbarProps) {
  // 使用useRef保存上一次的props值，用于比较避免无限循环
  const prevPropsRef = useRef<{
    startDate?: string;
    endDate?: string;
  }>({});
  
  // 本地状态，用于输入框内容管理
  const [inputValue, setInputValue] = useState(searchTerm || "");
  // 添加本地状态跟踪日期值，确保界面正确更新
  const [localStartDate, setLocalStartDate] = useState(startDate || "");
  const [localEndDate, setLocalEndDate] = useState(endDate || "");
  
  // 记录组件是否已经完成初始化
  const isInitializedRef = useRef(false);
  
  // 使用系统缩放hook获取样式配置
  const { scale, styles, formStyles } = useSystemScale();
  
  // 在组件首次渲染后记录初始化完成
  useEffect(() => {
    if (!isInitializedRef.current) {
      isInitializedRef.current = true;
    }
  }, [startDate, endDate]);
  
  // 监听回车键
  useKeyPress(
    "Enter",
    () => {
      onSearchChange(inputValue || "");
    },
    [inputValue, onSearchChange],
  );
  
  // 处理可用日期 - 修改于 2025-06-26 12:07:34.786，当没有数据时不添加默认日期
  const dateOptions: { label: string; value: string }[] = availableDates.length > 0
    ? availableDates.map(date => ({ label: date, value: date }))
    : []; // 移除默认值，完全依赖API提供的数据

  // 只有当有可用日期数据时，才确保当前选择的日期在选项中
  // 如果开始日期不在选项中且有可用日期数据，添加到选项列表
  if (availableDates.length > 0 && localStartDate && !dateOptions.some(option => option.value === localStartDate)) {
    dateOptions.push({ label: localStartDate, value: localStartDate });
  }

  // 如果结束日期不在选项中且有可用日期数据，添加到选项列表
  if (availableDates.length > 0 && localEndDate && !dateOptions.some(option => option.value === localEndDate)) {
    dateOptions.push({ label: localEndDate, value: localEndDate });
  }
  
  // 对dateOptions进行排序（对变量进行修改，不影响返回类型）
  dateOptions.sort((a, b): number => {
    const dateA = new Date(a.value).getTime();
    const dateB = new Date(b.value).getTime();
    // 检查日期是否有效，无效日期排在最后
    if (Number.isNaN(dateA) && Number.isNaN(dateB)) {
      return 0;
    }
    if (Number.isNaN(dateA)) {
      return 1;
    }
    if (Number.isNaN(dateB)) {
      return -1;
    }
    return dateA - dateB;
  });
  
  // 当外部searchTerm变化时，更新输入框值
  useEffect(() => {
    setInputValue(searchTerm || "");
  }, [searchTerm]);

  // 同步外部传入的startDate到本地状态
  // 修复：添加值比较，确保只在实际变化时更新本地状态
  useEffect(() => {
    // 检查是否是由于本组件内部的操作导致的props变化
    if (prevPropsRef.current.startDate === startDate) {
      return; // 如果值相同，则不处理，防止循环更新
    }
    
    if (startDate && startDate !== localStartDate) {
      setLocalStartDate(startDate);
      
      // 更新上一次的props值
      prevPropsRef.current.startDate = startDate;
    }
  }, [startDate, localStartDate]);

  // 同步外部传入的endDate到本地状态
  // 修复：添加值比较，确保只在实际变化时更新本地状态
  useEffect(() => {
    // 检查是否是由于本组件内部的操作导致的props变化
    if (prevPropsRef.current.endDate === endDate) {
      return; // 如果值相同，则不处理，防止循环更新
    }
    
    if (endDate && endDate !== localEndDate) {
      setLocalEndDate(endDate);
      
      // 更新上一次的props值
      prevPropsRef.current.endDate = endDate;
    }
  }, [endDate, localEndDate]);
  
  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    
    // 如果输入为空，立即清除搜索
    if (!value) {
      onSearchChange("");
    }
  };

  // 处理搜索图标点击
  const handleSearchIconClick = () => {
    onSearchChange(inputValue || "");
  };

  // 处理搜索清除
  const handleClearSearch = () => {
    setInputValue("");
    onSearchChange("");
  };
  
  // 处理开始日期变更（确保不大于结束日期）- 修改于 2025-06-26 12:07:34.786，当没有数据时不允许变更
  const handleStartDateChange = useCallback((date: string) => {
    // 确保日期有效且有可用数据
    if (!date || availableDates.length === 0) {
      return;
    }

    // 先更新本地状态，确保界面立即响应
    setLocalStartDate(date);

    // 更新追踪的上一次props值，防止useEffect中重复处理
    prevPropsRef.current.startDate = date;

    // 判断选择的开始日期是否大于当前结束日期
    if (new Date(date) > new Date(localEndDate)) {
      // 如果大于，则同时更新结束日期为开始日期
      setLocalEndDate(date);
      prevPropsRef.current.endDate = date;
      onEndDateChange(date);
    }

    // 触发父组件回调
    onStartDateChange(date);
  }, [localEndDate, onStartDateChange, onEndDateChange, availableDates.length]);
  
  // 处理结束日期变更（确保不小于开始日期）- 修改于 2025-06-26 12:07:34.786，当没有数据时不允许变更
  const handleEndDateChange = useCallback((date: string) => {
    // 确保日期有效且有可用数据
    if (!date || availableDates.length === 0) {
      return;
    }

    // 先更新本地状态，确保界面立即响应
    setLocalEndDate(date);

    // 更新追踪的上一次props值，防止useEffect中重复处理
    prevPropsRef.current.endDate = date;

    // 判断选择的结束日期是否小于当前开始日期
    if (new Date(date) < new Date(localStartDate)) {
      // 如果小于，则同时更新开始日期为结束日期
      setLocalStartDate(date);
      prevPropsRef.current.startDate = date;
      onStartDateChange(date);
    }

    // 触发父组件回调
    onEndDateChange(date);
  }, [localStartDate, onStartDateChange, onEndDateChange, availableDates.length]);

  return (
			<div className="flex flex-wrap items-center gap-4">
					{/* 开始日期选择 - 修改于 2025-06-26 12:07:34.786，当没有数据时禁用 */}
					<div className="flex flex-col gap-1">
						<Select
							value={availableDates.length > 0 ? localStartDate : ""}
							onValueChange={handleStartDateChange}
							disabled={availableDates.length === 0}
						>
							<SelectTrigger
								className={cn(
									formStyles?.selectWidth?.xs,
									formStyles?.selectWidth?.sm,
									"w-[120px]",
									"shrink-0",
									"font-mono",
									styles?.fontSize?.content,
									"h-9 rounded-md",
									scale > 1.25 && "min-h-[2rem] py-1",
									availableDates.length === 0
										? "opacity-50 cursor-not-allowed"
										: "",
								)}
							>
								<SelectValue placeholder={availableDates.length === 0 ? "暂无数据" : "开始日期"} />
							</SelectTrigger>
							<SelectContent>
								{dateOptions.length > 0 ? (
									dateOptions.map(
										(date): JSX.Element => (
											<SelectItem
												key={date.value}
												value={date.value}
												disabled={
													date.value === localEndDate
												}
												className={cn(
													"font-mono",
													styles?.fontSize?.content,
													scale > 1.25 && "py-1",
												)}
											>
												{date.label}
											</SelectItem>
										),
									)
								) : (
									<SelectItem
										value="no-data"
										disabled
										className={cn(
											"font-mono",
											styles?.fontSize?.content,
										)}
									>
										暂无可用日期
									</SelectItem>
								)}
							</SelectContent>
						</Select>
					</div>

					{/* 结束日期选择 - 修改于 2025-06-26 12:07:34.786，当没有数据时禁用 */}
					<div className="flex flex-col gap-1">
						<Select
							value={availableDates.length > 0 ? localEndDate : ""}
							onValueChange={handleEndDateChange}
							disabled={availableDates.length === 0}
						>
							<SelectTrigger
								className={cn(
									formStyles?.selectWidth?.xs,
									formStyles?.selectWidth?.sm,
									"w-[120px]",
									"shrink-0",
									"font-mono",
									styles?.fontSize?.content,
									"h-9 rounded-md",
									scale > 1.25 && "min-h-[2rem] py-1",
									availableDates.length === 0
										? "opacity-50 cursor-not-allowed"
										: "",
								)}
							>
								<SelectValue placeholder={availableDates.length === 0 ? "暂无数据" : "结束日期"} />
							</SelectTrigger>
							<SelectContent>
								{dateOptions.length > 0 ? (
									dateOptions.map(
										(date): JSX.Element => (
											<SelectItem
												key={date.value}
												value={date.value}
												disabled={
													date.value ===
													localStartDate
												}
												className={cn(
													"font-mono",
													styles?.fontSize?.content,
													scale > 1.25 && "py-1",
												)}
											>
												{date.label}
											</SelectItem>
										),
									)
								) : (
									<SelectItem
										value="no-data"
										disabled
										className={cn(
											"font-mono",
											styles?.fontSize?.content,
										)}
									>
										暂无可用日期
									</SelectItem>
								)}
							</SelectContent>
						</Select>
					</div>

					{/* 股东类型选择 */}
					<div className="flex flex-col gap-1">
						<Select
							value={shareholderType}
							onValueChange={onShareholderTypeChange}
						>
							<SelectTrigger
								className={cn(
									formStyles?.selectWidth?.md,
									"w-[160px]",
									"shrink-0",
									styles?.fontSize?.content,
									"h-9 rounded-md",
									scale > 1.25 && "min-h-[2rem] py-1",
								)}
							>
								<SelectValue placeholder="选择类型" />
							</SelectTrigger>
							<SelectContent className="min-w-[170px]">
								<SelectItem
									value="all"
									className={cn(
										"whitespace-nowrap",
										styles?.fontSize?.content,
										scale > 1.25 && "py-1",
									)}
								>
									全部股东
								</SelectItem>
								{shareholderTypes.length > 0 ? (
									shareholderTypes.map(
										(type): JSX.Element => (
											<SelectItem
												key={type}
												value={type}
												className={cn(
													"whitespace-nowrap",
													styles?.fontSize?.content,
													scale > 1.25 && "py-1",
												)}
											>
												{type}
											</SelectItem>
										),
									)
								) : (
									<>
										<SelectItem
											value="individual"
											className={cn(
												"whitespace-nowrap",
												styles?.fontSize?.content,
												scale > 1.25 && "py-1",
											)}
										>
											个人股东
										</SelectItem>
										<SelectItem
											value="institutional"
											className={cn(
												"whitespace-nowrap",
												styles?.fontSize?.content,
												scale > 1.25 && "py-1",
											)}
										>
											机构股东
										</SelectItem>
									</>
								)}
							</SelectContent>
						</Select>
					</div>

					{/* 搜索框 */}
					<div className="flex-1 min-w-[200px] max-w-[400px]">
						<div className="relative">
							<Input
								value={inputValue}
								onChange={handleSearchChange}
								placeholder="搜索股东名称"
								className={cn(
									"pr-10 w-full", // 确保右侧有足够空间放置图标
									styles?.fontSize?.content,
									"h-9 rounded-md", // 更方正的圆角
									scale > 1.25
										? styles?.elements?.inputHeight
										: "",
									"min-h-[2.25rem]", // 确保最小高度与其他元素一致
								)}
								disabled={isLoading}
							/>
							{/* 搜索按钮始终显示 */}
							{inputValue && (
								<button
									type="button"
									className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 flex items-center justify-center text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 rounded-full"
									onClick={handleSearchIconClick}
									disabled={isLoading}
									aria-label="搜索"
								>
									<SearchIcon
										className={
											formStyles?.iconSize || "h-4 w-4"
										}
									/>
								</button>
							)}

							{/* 清除按钮仅在有输入内容时显示 */}
							{inputValue && (
								<button
									type="button"
									className="absolute right-9 top-1/2 transform -translate-y-1/2 h-5 w-5 flex items-center justify-center text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 rounded-full"
									onClick={handleClearSearch}
									disabled={isLoading}
									aria-label="清除搜索"
								>
									<XIcon
										className={
											formStyles?.iconSize || "h-4 w-4"
										}
									/>
								</button>
							)}
						</div>
					</div>

					{/* 刷新按钮 */}
					<div className="flex flex-col gap-1">
						<Button
							variant="outline"
							size="icon"
							className={cn(
								"shrink-0",
								"h-9 w-9", // 确保高宽一致为9（36px）
								"rounded-md", // 与其他按钮保持一致的圆角
								"bg-background text-foreground border-border hover:bg-accent", // 使用默认白色背景
							)}
							onClick={onRefresh}
							disabled={isLoading}
						>
							<RefreshCcw className="size-4 m-0" />
						</Button>
					</div>
			</div>
		);
} 