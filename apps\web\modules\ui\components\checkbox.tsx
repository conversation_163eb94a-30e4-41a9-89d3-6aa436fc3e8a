import * as React from "react";
import * as RadixCheckbox from "@radix-ui/react-checkbox";
import { CheckIcon, MinusIcon } from "lucide-react";

export interface CheckboxProps extends React.ComponentPropsWithoutRef<typeof RadixCheckbox.Root> {
  indeterminate?: boolean;
}

export const Checkbox = React.forwardRef<React.ElementRef<typeof RadixCheckbox.Root>, CheckboxProps>(
  ({ className, checked, indeterminate, ...props }, ref) => (
    <RadixCheckbox.Root
      ref={ref}
      className={
        `peer h-5 w-5 shrink-0 rounded border border-input bg-background shadow focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 flex items-center justify-center ${className ?? ''}`
      }
      checked={checked}
      {...props}
    >
      <RadixCheckbox.Indicator className="flex items-center justify-center h-full w-full">
        {indeterminate ? (
          <MinusIcon className="h-4 w-4 text-primary" />
        ) : checked ? (
          <CheckIcon className="h-4 w-4 text-primary" />
        ) : null}
      </RadixCheckbox.Indicator>
    </RadixCheckbox.Root>
  )
);
Checkbox.displayName = "Checkbox"; 