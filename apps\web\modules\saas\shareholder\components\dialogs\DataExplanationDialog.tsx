import {
	Dialog,
	DialogContent,
	Di<PERSON>Header,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import { InfoIcon } from "lucide-react";
import { But<PERSON> } from "@ui/components/button";
/**
 * 数据说明弹窗组件
 * @description 展示持股变动分析的筛选条件说明
 * @returns 数据说明弹窗UI组件
 * <AUTHOR>
 * @created 2025-06-12 19:45:08
 */
export function DataExplanationDialog(): JSX.Element {
	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button
					variant="ghost"
					size="sm"
					className="h-6 w-6 p-0 hover:bg-muted/50"
					title="查看数据说明"
				>
					<InfoIcon className="size-4 text-muted-foreground hover:text-foreground" />
				</Button>
			</DialogTrigger>
			<DialogContent className="sm:max-w-[600px]">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<InfoIcon className="size-5 text-primary" />
						持股变动分析数据说明
					</DialogTitle>
				</DialogHeader>
				<div className="space-y-4 py-4">
					{/* 新进股东说明 */}
					<div className="space-y-2">
						<h4 className="font-medium text-sm text-foreground">
							新进股东筛选条件
						</h4>
						<div className="text-sm text-muted-foreground space-y-1">
							<p>
								• <strong>个人股东：</strong>
								在当期名册中首次出现的自然人股东
							</p>
							<p>
								• <strong>机构股东：</strong>
								在当期名册中首次出现的法人或其他机构股东
							</p>
							<p>• 筛选依据：股东证件号码在历史名册中未曾出现</p>
						</div>
					</div>

					{/* 退出股东说明 */}
					<div className="space-y-2">
						<h4 className="font-medium text-sm text-foreground">
							退出股东筛选条件
						</h4>
						<div className="text-sm text-muted-foreground space-y-1">
							<p>
								• <strong>个人股东：</strong>
								在上期名册中存在但当期名册中消失的自然人股东
							</p>
							<p>
								• <strong>机构股东：</strong>
								在上期名册中存在但当期名册中消失的法人或其他机构股东
							</p>
							<p>
								•
								筛选依据：股东证件号码在上期名册存在，当期名册中不存在
							</p>
						</div>
					</div>

					{/* 增持股东说明 */}
					<div className="space-y-2">
						<h4 className="font-medium text-sm text-foreground">
							增持股东筛选条件
						</h4>
						<div className="text-sm text-muted-foreground space-y-1">
							<p>
								• <strong>个人股东：</strong>
								当期持股数量相比上期有所增加的自然人股东
							</p>
							<p>
								• <strong>机构股东：</strong>
								当期持股数量相比上期有所增加的法人或其他机构股东
							</p>
							<p>
								•
								筛选依据：同一证件号码股东在两期名册中持股数量对比，当期
								&gt; 上期
							</p>
						</div>
					</div>

					{/* 减持股东说明 */}
					<div className="space-y-2">
						<h4 className="font-medium text-sm text-foreground">
							减持股东筛选条件
						</h4>
						<div className="text-sm text-muted-foreground space-y-1">
							<p>
								• <strong>个人股东：</strong>
								当期持股数量相比上期有所减少的自然人股东
							</p>
							<p>
								• <strong>机构股东：</strong>
								当期持股数量相比上期有所减少的法人或其他机构股东
							</p>
							<p>
								•
								筛选依据：同一证件号码股东在两期名册中持股数量对比，当期
								&lt; 上期
							</p>
						</div>
					</div>

					{/* 注意事项 */}
					<div className="mt-6 p-3 bg-muted/30 rounded-md">
						<h4 className="font-medium text-sm text-foreground mb-2">
							注意事项
						</h4>
						<div className="text-xs text-muted-foreground space-y-1">
							<p>
								• 分析基于股东证件号码进行匹配，确保数据准确性
							</p>
							<p>• 个人股东与机构股东根据证件类型自动区分</p>
							<p>• 持股变动计算精确到股，比例计算保留两位小数</p>
						</div>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
