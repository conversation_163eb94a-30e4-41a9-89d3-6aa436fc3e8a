import type { Config } from "./types";

export const config = {
	// 国际化
	i18n: {
		// 是否启用国际化（如果禁用，您仍然需要定义下面要使用的区域设置并将其设置为默认区域设置）
		enabled: false,
		// 在此处定义应用程序中应可用的所有区域设置
		// 您需要定义一个显示在语言选择器中的标签和一个应与此区域设置一起用于定价的货币
		locales: {
			// 英语区域设置配置
			en: {
				// 设置英语区域的默认货币为美元
				currency: "USD",
				// 设置语言选择器中显示的英语标签
				label: "English",
			},
			// 德语区域设置配置
			de: {
				// 设置德语区域的默认货币为美元
				currency: "USD",
				// 设置语言选择器中显示的德语标签
				label: "Deutsch",
			},
			// 中文区域设置配置
			zh: {
				// 设置中文区域的默认货币为人民币
				currency: "CNY",
				// 设置语言选择器中显示的中文标签
				label: "中文",
			},
		},
		// 如果未提供区域设置，则使用默认区域设置
		defaultLocale: "zh",
		// 如果未提供货币，则使用默认货币进行定价
		defaultCurrency: "CNY",
		// 用于确定区域设置的 cookie 名称
		localeCookieName: "NEXT_LOCALE",
	},
	// 组织
	organizations: {
		// 是否启用组织功能
		enable: true,
		// 是否为组织启用计费（您可以在下面为用户启用它）
		enableBilling: false,
		// 是否对用户隐藏组织（用于多租户应用程序）
		hideOrganization: false,
		// 用户是否应该能够创建新组织？否则只有管理员用户可以创建它们
		enableUsersToCreateOrganizations: false,
		// 是否要求用户必须属于某个组织。这将在登录后将用户重定向到组织页面
		requireOrganization: true,
		// 是否允许组织管理者和所有者邀请成员
		enableMemberInvitations: false,
		// 定义禁止的组织 slug。确保添加所有在 /app/... 之后定义为路由的路径以避免路由问题
		forbiddenOrganizationSlugs: [
			// 禁止使用 "new-organization" 作为组织 slug，因为这是创建新组织的路由
			"new-organization",
			// 禁止使用 "admin" 作为组织 slug，因为这是管理员路由
			"admin",
			// 禁止使用 "settings" 作为组织 slug，因为这是设置页面路由
			"settings",
			// 禁止使用 "ai-demo" 作为组织 slug，因为这是 AI 演示页面路由
			"ai-demo",
			// 禁止使用 "organization-invitation" 作为组织 slug，因为这是组织邀请页面路由
			"organization-invitation",
		],
	},
	// 用户
	users: {
		// 是否为用户启用计费（您可以在上面为组织启用它）
		enableBilling: false,
		// 您是否希望用户在注册后通过入职表单（可在 OnboardingForm.tsx 中定义）
		enableOnboarding: false,
	},
	// 认证
	auth: {
		// 用户是否应该能够创建帐户（否则只能由管理员创建用户）
		enableSignup: false,
		// 用户是否应该能够通过魔术链接登录
		enableMagicLink: false,
		// 用户是否应该能够通过社交提供商登录
		enableSocialLogin: false,
		// 用户是否应该能够通过通行密钥登录
		enablePasskeys: false,
		// 用户是否应该能够通过密码登录
		enablePasswordLogin: true,
		// 用户登录后应重定向到的位置
		redirectAfterSignIn: "/app",
		// 用户注销后应重定向到的位置
		redirectAfterLogout: "/",
		// 会话应该有效的时长（这里设置为 30 天，单位是秒：60秒 * 60分 * 24小时 * 30天）
		sessionCookieMaxAge: 60 * 60 * 24 * 30,
	},
	// 邮件
	mails: {
		// 邮件的发件人地址
		from: "<EMAIL>",
	},
	// 前端
	ui: {
		// 应用中应可用的主题
		enabledThemes: ["light", "dark"],
		// 默认主题
		defaultTheme: "light",
		// 应用程序的 SaaS 部分
		saas: {
			// 是否启用 SaaS 部分（否则所有路由将重定向到营销页面）
			enabled: true,
			// 是否应使用侧边栏布局
			useSidebarLayout: true,
		},
		// 应用程序的营销部分
		marketing: {
			// 是否启用营销功能（否则所有路由将重定向到 SaaS 部分）
			enabled: true,
		},
	},
	// 存储
	storage: {
		// 定义不同类型文件的存储桶名称
		bucketNames: {
			// 头像存储桶名称，如果环境变量未设置则默认使用 "avatars"
			avatars: process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME ?? "avatars",
		},
	},
	// 联系表单配置
	contactForm: {
		// 是否启用联系表单
		enabled: true,
		// 联系表单消息应发送到的电子邮件
		to: "<EMAIL>",
		// 电子邮件的主题
		subject: "Contact form message",
	},
	// 支付
	payments: {
		// 定义结账时应可用的产品
		plans: {
			// 基础版（免费）计划配置
			basic: {
				// 标记这是一个免费计划
				isFree: true,
			},
			// 专业版计划配置
			pro: {
				// 定义专业版计划的价格选项数组
				prices: [
					{
						// 设置为循环付费类型
						type: "recurring",
						// 从环境变量获取月付产品 ID
						productId: process.env
							.NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY as string,
						// 设置付费间隔为每月
						interval: "month",
						// 设置月付金额为 10万元
						amount: 10000,
						// 设置货币单位为人民币
						currency: "CNY",
						// 启用基于座位的定价
						seatBased: true,
						// 设置默认席位数
						defaultSeats: 3,
						// 设置 7 天的试用期
						trialPeriodDays: 7,
					},
					{
						// 设置为循环付费类型
						type: "recurring",
						// 从环境变量获取年付产品 ID
						productId: process.env
							.NEXT_PUBLIC_PRICE_ID_PRO_YEARLY as string,
						// 设置付费间隔为每年
						interval: "year",
						// 设置年付金额为 10万元
						amount: 100000,
						// 设置货币单位为人民币
						currency: "CNY",
						// 启用基于座位的定价
						seatBased: true,
						// 设置默认席位数
						defaultSeats: 3,
						// 设置 7 天的试用期
						trialPeriodDays: 7,
					},
				],
			},
			// 高级版计划配置
			advanced: {
				// 标记这是推荐的计划
				recommended: true,
				// 定义高级版计划的价格选项数组
				prices: [
					{
						// 设置为循环付费类型
						type: "recurring",
						// 从环境变量获取月付产品 ID
						productId: process.env
							.NEXT_PUBLIC_PRICE_ID_ADVANCED_MONTHLY as string,
						// 设置付费间隔为每月
						interval: "month",
						// 设置月付金额为 20万元
						amount: 20000,
						// 设置货币单位为人民币
						currency: "CNY",
						// 启用基于座位的定价
						seatBased: true,
						// 设置默认席位数
						defaultSeats: 5,
						// 设置 7 天的试用期
						trialPeriodDays: 7,
					},
					{
						// 设置为循环付费类型
						type: "recurring",
						// 从环境变量获取年付产品 ID
						productId: process.env
							.NEXT_PUBLIC_PRICE_ID_ADVANCED_YEARLY as string,
						// 设置付费间隔为每年
						interval: "year",
						// 设置年付金额为 20万元
						amount: 200000,
						// 设置货币单位为人民币
						currency: "CNY",
						// 启用基于座位的定价
						seatBased: true,
						// 设置默认席位数
						defaultSeats: 5,
						// 设置 7 天的试用期
						trialPeriodDays: 7,
					},
				],
			},
			// 旗舰版计划配置
			flagship: {
				// 标记这是企业版计划
				isEnterprise: true,
			},
		},
	},
} as const satisfies Config;

export type { Config };
