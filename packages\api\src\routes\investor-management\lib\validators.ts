/**
 * 投资人管理模块验证器
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建验证器
 * @updated 2025-07-09 20:29:13 hayden 添加companyFilterId可选参数，支持收藏标签关联公司筛选配置
 * @updated 2025-07-09 20:33:23 hayden 修改companyFilterId为必需参数，确保所有标签都关联公司筛选配置
 * @description 投资人管理模块的所有Zod验证器定义，包括公司筛选配置、投资人标签和联系人相关验证
 */
import { z } from "zod";

// 公司筛选配置相关验证器
export const CreateCompanyFilterSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  companyCode: z.string().min(1, "本司公司代码不能为空"),
  benchmarkCompanyCodes: z.string().optional(),
});

export const GetCompanyFilterSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
});

// 投资人标签相关验证器（基于标签系统的收藏功能）
export const CreateInvestorTagSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  investorCode: z.string().min(1, "投资人代码不能为空"),
  tagName: z.string().min(1, "标签名称不能为空"),
  tagCategory: z.enum(["system", "user"], {
    errorMap: () => ({ message: "标签分类必须是 system、user" })
  }),
  tagMetadata: z.record(z.any()).optional(),
  companyFilterId: z.string().min(1, "公司筛选配置ID不能为空"), // 2025-07-09 20:33:23 hayden 修改为必需参数，收藏标签必须携带companyFilterId
});

export const DeleteInvestorTagSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  id: z.string().min(1, "标签记录ID不能为空"),
});

export const ListInvestorTagsSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  investorCode: z.string().optional(),
  tagName: z.string().optional(),
  tagCategory: z.enum(["system", "user"]).optional(),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
});

export const SyncInvestorTagsSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
});

// 投资人联系人相关验证器
export const CreateInvestorContactSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  name: z.string().min(1, "联系人姓名不能为空"),
  phoneNumber: z.string().optional(),
  email: z.string().email("邮箱格式不正确").optional().or(z.literal("")),
  address: z.string().optional(),
  remarks: z.string().max(300, "备注信息不能超过300字符").optional(),
});

export const UpdateInvestorContactSchema = z.object({
  contactId: z.string().min(1, "联系人ID不能为空"),
  organizationId: z.string().min(1, "组织ID不能为空"),
  name: z.string().min(1, "联系人姓名不能为空").optional(),
  phoneNumber: z.string().optional(),
  email: z.string().email("邮箱格式不正确").optional().or(z.literal("")),
  address: z.string().optional(),
  remarks: z.string().max(300, "备注信息不能超过300字符").optional(),
});

export const DeleteInvestorContactSchema = z.object({
  contactId: z.string().min(1, "联系人ID不能为空"),
  organizationId: z.string().min(1, "组织ID不能为空"),
});

export const ListInvestorContactsSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  name: z.string().optional(),
  phoneNumber: z.string().optional(),
  email: z.string().optional(),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
});
