import { useState, useCallback } from "react";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import type { ShareholderRegistryItem } from "@saas/shareholder/lib/types";
import { useShareholderRegistry } from "@saas/shareholder/hooks/useShareholders";
import { shareholderRegistryApi } from "@saas/shareholder/lib/registry-api";

/**
 * 股东名册管理自定义Hook
 * @param organizationId 组织ID
 * @param organization 组织信息对象
 * @returns 股东名册管理相关状态和方法
 * 
 * @version 1.1.0 (2025-06-01) - 添加组织信息刷新机制，确保删除名册后公司代码和简称更新
 */
export function useShareholderRegistryManage(organizationId: string, organization: any) {
  // 获取 React Query 的 queryClient 用于刷新数据
  const queryClient = useQueryClient();
  
  // 导入对话框状态
  const [isImportOpen, setIsImportOpen] = useState(false);
  
  // 单个删除对话框状态
  const [deleteTarget, setDeleteTarget] = useState<ShareholderRegistryItem | null>(null);
  
  // 批量删除状态
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [isBatchDeleteOpen, setIsBatchDeleteOpen] = useState(false);
  // 批量删除加载状态
  const [isBatchDeleting, setIsBatchDeleting] = useState(false);
  
  // 使用共享的股东名册钩子获取数据
  const {
		registries,
		registriesPagination,
		isLoadingRegistries,
		page,
		limit,
		setPage,
		setLimit,
		setCompanyCode,
		deleteRegistry,
		isDeleting,
		refetchRegistries,
  } = useShareholderRegistry(organizationId);

  // 刷新组织信息的函数
  const refreshOrganizationData = useCallback(() => {
    if (organizationId) {
      // 使用组织ID刷新组织信息
      queryClient.invalidateQueries({
        queryKey: ["organization", organization?.slug]
      });
    }
  }, [organizationId, organization?.slug, queryClient]);

  // 处理导入
  const handleImport = useCallback(() => {
    setIsImportOpen(true);
  }, []);

  // 处理导入完成
  const handleImportClose = useCallback((refresh = true) => {
    setIsImportOpen(false);
    if (refresh) {
      refetchRegistries();
      // 刷新股东变化分析相关缓存 - 添加于 2025-06-26 11:53:14.842
      queryClient.invalidateQueries({
        queryKey: ["shareholder-changes", organizationId]
      });
      queryClient.invalidateQueries({
        queryKey: ["shareholder-register-dates", organizationId]
      });
      // 不再直接调用refreshOrganizationData，依靠组件中的useEffect处理
    }
  }, [refetchRegistries, queryClient, organizationId]);

  // 处理批量删除
  const handleBatchDelete = useCallback(() => {
    if (selectedItems.length > 0) {
      setIsBatchDeleteOpen(true);
    } else {
      toast.error("请选择要删除的项目", {
        description: "您需要先选择至少一个名册才能进行批量删除"
      });
    }
  }, [selectedItems]);

  // 确认批量删除 - 使用新的批量删除API
  const confirmBatchDelete = useCallback(async () => {
    if (!selectedItems.length) {
      toast.error("请选择要删除的项目", {
        description: "您需要先选择至少一个名册才能进行批量删除"
      });
      return;
    }
    
    // 设置加载状态
    setIsBatchDeleting(true);
    
    try {
      const result = await shareholderRegistryApi.batchDeleteShareholderRegistry(selectedItems);
      // 检查删除结果
      if (result?.success) {
        // 解析返回的数据 - 处理data字段是字符串的情况
        let parsedData: any;
        if (result.data && typeof result.data === 'string') {
          try {
            parsedData = JSON.parse(result.data);
          } catch (e) {
            console.error('解析data字段失败:', e);
            parsedData = {};
          }
        } else {
          parsedData = result;
        }
        
        const successCount = parsedData?.success?.count || 0;
        const failedCount = parsedData?.failed?.count || 0;
        
        if (successCount > 0) {
          if (failedCount > 0) {
            // 部分成功
            toast.warning("部分删除成功", {
              description: `已成功删除 ${successCount} 个股东名册，${failedCount} 个删除失败`
            });
          } else {
            // 全部成功
            toast.success("批量删除成功", {
              description: `已成功删除 ${successCount} 个股东名册`
            });
          }
        } else if (failedCount > 0) {
          // 全部失败
          toast.error("批量删除失败", {
            description: "所有股东名册删除均失败，请稍后重试"
          });
        } else {
          // 没有明确的成功或失败计数，但API返回成功
          toast.success("操作完成", {
            description: "批量删除操作已完成"
          });
        }
        
        // 重置状态
        setSelectedItems([]);
        setIsBatchDeleteOpen(false);
        
        // 刷新数据列表
        refetchRegistries();

        // 刷新股东变化分析相关缓存 - 添加于 2025-06-26 11:53:14.842
        queryClient.invalidateQueries({
          queryKey: ["shareholder-changes", organizationId]
        });
        queryClient.invalidateQueries({
          queryKey: ["shareholder-register-dates", organizationId]
        });

        // 检查是否删除了所有名册，如果是，刷新组织信息
        const isAllDeleted = selectedItems.length === registries.length;
        if (isAllDeleted) {
          // 延迟500ms再次刷新，确保期数信息更新
          setTimeout(() => {
            refetchRegistries();
            // 刷新组织信息
            refreshOrganizationData();
          }, 500);
        }
      } else {
        // API返回失败
        console.error('API返回错误:', result);
        toast.error("批量删除失败", {
          description: result?.error?.message || "删除股东名册失败，请稍后重试"
        });
      }
    } catch (error) {
      console.error('批量删除出错:', error);
      toast.error("批量删除出错", {
        description: error instanceof Error ? error.message : "未知错误，请稍后重试"
      });
    } finally {
      // 无论成功失败，都取消加载状态
      setIsBatchDeleting(false);
    }
  }, [selectedItems, refetchRegistries, registries.length, refreshOrganizationData]);

  // 确认删除单个名册
  const confirmSingleDelete = useCallback(async (registry: ShareholderRegistryItem) => {
    try {
      // 调用API删除
      const result = await deleteRegistry({ 
        registryId: registry.id
      });
      
      // 检查删除结果
      if (result?.success) {
        toast.success("删除成功", {
          description: `代码:${registry.companyCode} 股东名册已成功删除`
        });
        
        // 重置状态
        setDeleteTarget(null);
        
        // 刷新数据列表
        refetchRegistries();

        // 刷新股东变化分析相关缓存 - 添加于 2025-06-26 11:53:14.842
        queryClient.invalidateQueries({
          queryKey: ["shareholder-changes", organizationId]
        });
        queryClient.invalidateQueries({
          queryKey: ["shareholder-register-dates", organizationId]
        });

        // 检查是否删除了最后一个名册，如果是，刷新组织信息
        if (registries.length === 1) {
          // 延迟500ms再次刷新，确保组织信息更新
          setTimeout(() => {
            // 不再直接调用refreshOrganizationData，依靠组件中的useEffect处理
          }, 500);
        }
      }
      // 注意：如果失败，deleteRegistry内部已经通过toast显示错误信息，不需要再次显示
    } catch {
      // 不在此处抛出错误，deleteRegistry内部已经处理了错误提示
    }
  }, [deleteRegistry, refetchRegistries, registries.length]);

  // 处理选择
  const handleSelectItem = useCallback((id: string, selected: boolean) => {
    if (selected) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(itemId => itemId !== id));
    }
  }, []);

  // 处理全选
  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      // 全选当前页
      setSelectedItems(registries.map(reg => reg.id));
    } else {
      // 取消全选
      setSelectedItems([]);
    }
  }, [registries]);

  // 直接删除单个名册（无需二次确认弹窗）
  const directDeleteRegistry = useCallback(async (registry: ShareholderRegistryItem) => {
    try {
      // 调用API删除
      const result = await deleteRegistry({ 
        registryId: registry.id
      });
      
      // 检查删除结果
      if (result?.success) {
        toast.success("删除成功", {
          description: `代码:${registry.companyCode} 股东名册已成功删除`
        });
        
        // 刷新数据列表
        refetchRegistries();

        // 刷新股东变化分析相关缓存 - 添加于 2025-06-26 11:53:14.842
        queryClient.invalidateQueries({
          queryKey: ["shareholder-changes", organizationId]
        });
        queryClient.invalidateQueries({
          queryKey: ["shareholder-register-dates", organizationId]
        });
      }
      // 注意：如果失败，deleteRegistry内部已经通过toast显示错误信息，不需要再次显示
    } catch {
      // 不在此处抛出错误，deleteRegistry内部已经处理了错误提示
    }
  }, [deleteRegistry, refetchRegistries, registries.length, queryClient, organizationId]);
  
  // 直接批量删除（无需二次确认弹窗）- 使用新的批量删除API
  const batchDeleteRegistries = useCallback(async () => {
    if (!selectedItems.length) {
      toast.error("请选择要删除的项目", {
        description: "您需要先选择至少一个名册才能进行批量删除"
      });
      return;
    }
    
    // 设置加载状态
    setIsBatchDeleting(true);
    
    try {
      // 调用批量删除API
      const result = await shareholderRegistryApi.batchDeleteShareholderRegistry(selectedItems);
      // 检查删除结果
      if (result?.success) {
        // 解析返回的数据 - 处理data字段是字符串的情况
        let parsedData: any;
        if (result.data && typeof result.data === 'string') {
          try {
            parsedData = JSON.parse(result.data);
          } catch (e) {
            parsedData = {};
          }
        } else {
          parsedData = result;
        }
        
        const successCount = parsedData?.success?.count || 0;
        const failedCount = parsedData?.failed?.count || 0;

        if (successCount > 0) {
          if (failedCount > 0) {
            // 部分成功
            toast.warning("部分删除成功", {
              description: `已成功删除 ${successCount} 个股东名册，${failedCount} 个删除失败`
            });
          } else {
            // 全部成功
            toast.success("批量删除成功", {
              description: `已成功删除 ${successCount} 个股东名册`
            });
          }
        } else if (failedCount > 0) {
          // 全部失败
          toast.error("批量删除失败", {
            description: "所有股东名册删除均失败，请稍后重试"
          });
        } else {
          // 没有明确的成功或失败计数，但API返回成功
          toast.success("操作完成", {
            description: "批量删除操作已完成"
          });
        }
        
        // 重置状态
        setSelectedItems([]);
        
        // 刷新数据列表
        refetchRegistries();

        // 刷新股东变化分析相关缓存 - 添加于 2025-06-26 11:53:14.842
        queryClient.invalidateQueries({
          queryKey: ["shareholder-changes", organizationId]
        });
        queryClient.invalidateQueries({
          queryKey: ["shareholder-register-dates", organizationId]
        });

        // 检查是否删除了所有名册，如果是，延迟再次刷新以确保期数更新
        const isAllDeleted = selectedItems.length === registries.length;
        if (isAllDeleted) {
          // 延迟500ms再次刷新，确保期数信息更新
          setTimeout(() => {
            refetchRegistries();
            // 不再直接调用refreshOrganizationData，依靠组件中的useEffect处理
          }, 500);
        }
      } else {
        // API返回失败
        console.error('API返回错误:', result);
        toast.error("批量删除失败", {
          description: result?.error?.message || "删除股东名册失败，请稍后重试"
        });
      }
    } catch (error) {
      console.error('批量删除出错:', error);
      toast.error("批量删除出错", {
        description: error instanceof Error ? error.message : "未知错误，请稍后重试"
      });
    } finally {
      // 无论成功失败，都取消加载状态
      setIsBatchDeleting(false);
    }
  }, [selectedItems, refetchRegistries, registries.length]);


  // 从组织metadata中获取公司代码和名称
  let boundCompanyCode = "-";
  let boundCompanyName = "-";

  if (organization?.metadata) {
      // 尝试解析metadata字段
      const metadata = typeof organization.metadata === 'string' 
        ? JSON.parse(organization.metadata) 
        : organization.metadata; 
      // 提取绑定的公司代码和名称
      boundCompanyCode = metadata?.boundCompanyCode || "-";
      boundCompanyName = metadata?.boundCompanyName || "-";
  }

  // 当前公司代码和名称现在从组织metadata获取
  const currentCompanyCode = boundCompanyCode;
  const currentCompanyName = boundCompanyName;
  
  return {
    // 状态
    isImportOpen,
    deleteTarget,
    selectedItems,
    isBatchDeleteOpen,
    isDeleting,
    isBatchDeleting,
    isLoadingRegistries,
    
    // 数据
    registries,
    registriesPagination,
    currentCompanyCode,
    currentCompanyName,
    page,
    limit,
    
    // 操作方法
    setPage,
    setLimit,
    setCompanyCode,
    setDeleteTarget,
    setIsBatchDeleteOpen,
    setSelectedItems,
    
    // 业务方法
    handleImport,
    handleImportClose,
    confirmBatchDelete,
    confirmSingleDelete,
    handleSelectItem,
    handleSelectAll,
    handleBatchDelete,
    directDeleteRegistry,
    batchDeleteRegistries,
    refetchRegistries,
    refreshOrganizationData, // 导出刷新组织信息的方法
  };
} 