/**
 * DBF文件解析和验证共享工具
 * 提供01和05名册解析器共享的类型定义和工具函数
 *
 * 主要功能:
 * 1. 定义共享的数据类型和接口
 * 2. 提供通用的DBF文件解析函数
 * 3. 提供通用的验证和工具函数
 * 
 * @update 2025年06月03日 - 确保所有解析器保留原始字段名，不转换为数据库字段名。
 * 后端已实现字段映射机制，会根据名册类型自动将原始字段名映射到数据库字段。
 */

/**
 * 股东名册解析结果接口
 * 定义了解析DBF文件后返回的数据结构
 *
 * @property success - 解析是否成功
 * @property fileName - 原始文件名
 * @property companyCode - 公司代码
 * @property registerDate - 报告日期
 * @property records - 解析出的记录数组
 * @property recordCount - 记录总数
 * @property companyInfo - 公司相关信息
 * @property error - 错误信息(如果解析失败)
 */
export interface ShareholderRegistryParseResult {
	success: boolean;
	fileName: string;
	companyCode?: string;
	registerDate?: string;
	records?: any[];
	recordCount?: number;
	companyInfo?: {
		companyName: string;
		totalShares: string;
		totalShareholders: number;
		totalInstitutions: number;
		largeSharesCount: string;
		largeShareholdersCount: number;
		institutionShares: string;
		/**
		 * 新增信用股东统计字段
		 *
		 * <AUTHOR>
		 * @created 2025-06-13 12:54:01
		 * @description 支持05名册和T3名册的信用股东统计数据
		 */
		marginAccounts?: number; // 信用总户数（全量数据统计）
		marginShares?: string; // 信用总持股（全量数据统计）
	};
	error?: {
		type:
			| "FILE_ERROR"
			| "COMPANY_MISMATCH"
			| "DATE_MISMATCH"
			| "MISSING_FIELDS"
			| "EMPTY_FIELDS";
		message: string;
		details?: string[];
	};
}

/**
 * DBF文件相关数据结构定义
 */

/**
 * DBF字段定义
 * @property name - 字段名称
 * @property type - 字段类型(string/numeric/date等)
 * @property size - 字段长度
 * @property decimal - 小数位数(针对数值类型)
 */
export interface DbfField {
	name: string;
	type: string;
	size: number;
	decimal: number;
}

/**
 * DBF记录数据
 * 使用索引签名允许动态的字段名
 */
export interface DbfRecord {
	[key: string]: any;
}

/**
 * DBF文件完整数据结构
 * @property fields - 字段定义列表
 * @property records - 记录数据列表
 * @property version - DBF文件版本号
 * @property lastUpdated - 最后更新时间
 * @property recordCount - 记录总数
 */
export interface DbfData {
	fields: DbfField[];
	records: DbfRecord[];
	version: number;
	lastUpdated: Date;
	recordCount: number;
}

/**
 * 解析DBF文件头信息
 * DBF文件头包含文件的基本信息,如版本、记录数等
 *
 * @param buffer 文件二进制数据
 * @returns 文件头信息对象
 *
 * 返回信息包括:
 * - version: DBF文件版本号
 * - recordCount: 记录总数
 * - headerLength: 文件头长度
 * - recordLength: 每条记录长度
 */
export function parseDbfHeader(buffer: ArrayBuffer): {
	version: number;
	recordCount: number;
	headerLength: number;
	recordLength: number;
} {
	const view = new DataView(buffer);

	// 读取版本号 (第0个字节)
	const version = view.getUint8(0);

	// 读取记录数量 (第4-7个字节)
	const recordCount = view.getUint32(4, true);

	// 读取头部长度 (第8-9个字节)
	const headerLength = view.getUint16(8, true);

	// 读取记录长度 (第10-11个字节)
	const recordLength = view.getUint16(10, true);

	return {
		version,
		recordCount,
		headerLength,
		recordLength,
	};
}

/**
 * 从DBF文件解析字段信息
 * 解析文件中定义的所有字段的详细信息
 *
 * @param buffer 文件二进制数据
 * @returns 字段定义列表
 *
 * 字段信息包括:
 * - name: 字段名称
 * - type: 字段类型(string/numeric/date等)
 * - size: 字段长度
 * - decimal: 小数位数
 */
export function parseDbfFields(buffer: ArrayBuffer): DbfField[] {
	const view = new DataView(buffer);
	const fields: DbfField[] = [];

	// 头部长度是32字节
	let offset = 32;

	// 读取字段信息，直到遇到0x0D（字段描述符终止符）
	while (offset < buffer.byteLength && view.getUint8(offset) !== 0x0d) {
		// 字段名称 (11字节)
		let fieldName = "";
		for (let i = 0; i < 11; i++) {
			const charCode = view.getUint8(offset + i);
			if (charCode !== 0) {
				fieldName += String.fromCharCode(charCode);
			}
		}
		fieldName = fieldName.trim();

		// 字段类型 (1字节)
		const fieldType = String.fromCharCode(view.getUint8(offset + 11));

		// 字段长度 (1字节)
		const fieldLength = view.getUint8(offset + 16);

		// 小数位数 (1字节)
		const decimalCount = view.getUint8(offset + 17);

		fields.push({
			name: fieldName,
			type:
				fieldType === "C"
					? "string"
					: fieldType === "N"
						? "numeric"
						: fieldType === "F"
							? "numeric"
							: fieldType === "L"
								? "logical"
								: fieldType === "D"
									? "date"
									: "string",
			size: fieldLength,
			decimal: decimalCount,
		});

		// 每个字段描述符是32字节
		offset += 32;
	}

	return fields;
}

/**
 * 检查日期是否有效
 *
 * @param year 年份
 * @param month 月份（0-11）
 * @param day 日（1-31）
 * @returns 日期是否有效
 */
export function isValidDate(year: number, month: number, day: number): boolean {
	// 创建日期对象
	const date = new Date(year, month, day);

	// 检查日期是否有效（如果无效，年月日会被自动调整）
	return (
		date.getFullYear() === year &&
		date.getMonth() === month &&
		date.getDate() === day
	);
}

/**
 * 检查字符串是否为有效的日期格式 (YYYY-MM-DD)
 *
 * @param dateStr 待检查的日期字符串
 * @returns 日期是否有效
 */
export function isValidDateString(dateStr: string): boolean {
	// 检查是否符合YYYY-MM-DD格式
	const regex = /^\d{4}-\d{2}-\d{2}$/;
	if (!regex.test(dateStr)) {
		return false;
	}

	// 拆分日期字符串
	const parts = dateStr.split("-");
	const year = Number(parts[0]);
	const month = Number(parts[1]) - 1; // 月份从0开始
	const day = Number(parts[2]);

	// 检查日期是否有效
	return isValidDate(year, month, day);
}

/**
 * 验证必填字段的内容是否为空
 * 检查所有股东记录中必填字段的值是否有效
 *
 * @param records 记录数组
 * @returns 存在空值的字段标签列表
 */
export function validateRequiredFieldsContent(records: any[]): string[] {
	if (!records || records.length === 0) {
		return [];
	}

	// 过滤掉特殊记录，只检查实际股东记录
	const shareholderRecords = records.filter(
		(rec: any) =>
			rec.XH !== 0 &&
			rec.XH !== -1 &&
			rec.XH !== -2 &&
			typeof rec.XH === "number" &&
			rec.XH > 0,
	);

	if (shareholderRecords.length === 0) {
		return [];
	}

	// 从第一条记录中确定必填字段
	// 01名册和05名册的必填字段不同，但都包含以下字段
	const requiredFields = [
		{ name: "YMTH", label: "一码通账户号码" },
		{ name: "CYRLBMS", label: "持有人类别" },
	];

	// 检查01名册特有字段
	if ("ZQZHMC" in shareholderRecords[0]) {
		requiredFields.push({ name: "ZQZHMC", label: "证券账户名称" });
	}
	if ("ZJDM" in shareholderRecords[0]) {
		requiredFields.push({ name: "ZJDM", label: "证件号码" });
	}

	// 检查05名册特有字段
	if ("XYZHMC" in shareholderRecords[0]) {
		requiredFields.push({ name: "XYZHMC", label: "信用证券账户名称" });
	}
	if ("XYZHZJDM" in shareholderRecords[0]) {
		requiredFields.push({ name: "XYZHZJDM", label: "信用证券账户证件号码" });
	}

	const emptyFields = new Set<string>();

	// 检查每条记录的必填字段是否有内容
	for (const record of shareholderRecords) {
		for (const field of requiredFields) {
			// 如果字段存在但值为空或undefined
			if (
				field.name in record &&
				(record[field.name] === undefined ||
					record[field.name] === null ||
					record[field.name] === "")
			) {
				emptyFields.add(field.label);
			}
		}
	}

	return Array.from(emptyFields);
}

/**
 * 检查文件类型是否为支持的格式
 * 支持的格式包括: .dbf, .zip, .xls, .xlsx
 *
 * @param fileName 文件名
 * @returns 是否为支持的文件格式
 */
export function isSupportedFileType(fileName: string): boolean {
	const lowercaseName = fileName.toLowerCase();
	return (
		lowercaseName.endsWith(".dbf") ||
		lowercaseName.endsWith(".zip") ||
		lowercaseName.endsWith(".xls") ||
		lowercaseName.endsWith(".xlsx")
	);
}

/**
 * 检查文件类型是否为当前可处理的格式
 * 目前系统只支持处理DBF文件格式
 * 其他格式(.zip/.xls/.xlsx)虽然支持上传但暂不支持处理
 *
 * @param fileName 文件名
 * @returns 是否为当前可处理的文件格式
 */
export function isProcessableFileType(fileName: string): boolean {
	return fileName.toLowerCase().endsWith(".dbf");
}

/**
 * 判断DBF文件是01名册还是05名册
 * 根据文件名严格匹配判断名册类型
 *
 * @param fileName 文件名
 * @returns 名册类型 '01' | '05' | 'unknown'
 * <AUTHOR>
 * @update 2025-06-23 16:01:13 - 添加严格文件名匹配逻辑
 */
export function detectRegistryType(fileName: string): '01' | '05' | 'unknown' {
  // 导入配置项
  const {
    FILE_NAME_PATTERNS,
    STRICT_FILENAME_VALIDATION
  } = require('../config');

  if (STRICT_FILENAME_VALIDATION) {
    // 严格模式：使用正则表达式精确匹配
    if (FILE_NAME_PATTERNS.DBF_C01.test(fileName)) {
      return '01';
    }
    if (FILE_NAME_PATTERNS.DBF_C05.test(fileName)) {
      return '05';
    }
    return 'unknown';
  }

  // 兼容模式：保留原有的宽松匹配逻辑
  const lowerFileName = fileName.toLowerCase();

  // 首先尝试从文件名判断
  if (lowerFileName.includes('01') || lowerFileName.includes('dqmc01')) {
    return '01';
  }

  if (lowerFileName.includes('05') || lowerFileName.includes('dqmc05')) {
    return '05';
  }

  // 如果文件名无法判断，返回未知类型
  return 'unknown';
}

/**
 * 验证DBF文件名格式
 * 在严格模式下使用正则表达式验证文件名格式
 *
 * @param fileName 文件名
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 */
export function validateDBFFileName(fileName: string): {
  isValid: boolean;
  registryType?: string;
  error?: string;
  suggestion?: string;
} {
  // 导入配置项
  const {
    validateDBFFileName: configValidateDBFFileName
  } = require('../config');

  return configValidateDBFFileName(fileName);
}