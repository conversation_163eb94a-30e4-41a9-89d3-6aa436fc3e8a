"use client";

import { authClient } from "@repo/auth/client";
import { isOrganizationAdmin } from "@repo/auth/lib/helper";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { sessionQuery<PERSON>ey } from "@saas/auth/lib/api";
import {
	activeOrganizationQueryKey,
	useActiveOrganizationQuery,
} from "@saas/organizations/lib/api";
import { purchasesQueryKey } from "@saas/payments/lib/api";
import { useRouter } from "@shared/hooks/router";
import { apiClient } from "@shared/lib/api-client";
import { useQueryClient } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import nProgress from "nprogress";
import { type ReactNode, useEffect, useState } from "react";
import { ActiveOrganizationContext } from "../lib/active-organization-context";

export function ActiveOrganizationProvider({
	children,
}: {
	children: ReactNode;
}) {
	const router = useRouter();
	const queryClient = useQueryClient();
	const { session, user } = useSession();
	const params = useParams();

	const activeOrganizationSlug = params.organizationSlug as string;

	const { data: activeOrganization } = useActiveOrganizationQuery(
		activeOrganizationSlug,
		{
			enabled: !!activeOrganizationSlug,
		},
	);

	const refetchActiveOrganization = async () => {
		await queryClient.refetchQueries({
			queryKey: activeOrganizationQueryKey(activeOrganizationSlug),
		});
	};

	/**
	 * 设置活跃组织，并更新状态和路由
	 * @param organizationSlug - 组织的slug或null（用于切换到个人视图）
	 */
	const setActiveOrganization = async (organizationSlug: string | null) => {
		nProgress.start();

		/* 原代码:
		 * const { data: newActiveOrganization } =
		 *   await authClient.organization.setActive(
		 *     organizationSlug
		 *       ? {
		 *           organizationSlug,
		 *         }
		 *       : {
		 *           organizationId: null,
		 *         },
		 *   );
		 *
		 * if (!newActiveOrganization) {
		 *   nProgress.done();
		 *   return;
		 * }
		 * 
		 * await queryClient.setQueryData(
		 *   activeOrganizationQueryKey(newActiveOrganization.slug),
		 *   newActiveOrganization,
		 * );
		 *
		 * if (config.organizations.enableBilling) {
		 *   await queryClient.prefetchQuery({
		 *     queryKey: purchasesQueryKey(newActiveOrganization.id),
		 *     queryFn: async () => {
		 *       const response = await apiClient.payments.purchases.$get({
		 *         query: {
		 *           organizationId: newActiveOrganization.id,
		 *         },
		 *       });
		 *
		 *       if (!response.ok) {
		 *         throw new Error("Failed to fetch purchases");
		 *       }
		 *
		 *       return response.json();
		 *     },
		 *   });
		 * }
		 *
		 * await queryClient.setQueryData(sessionQueryKey, (data: any) => {
		 *   return {
		 *     ...data,
		 *     session: {
		 *       ...data?.session,
		 *       activeOrganizationId: newActiveOrganization.id,
		 *     },
		 *   };
		 * });
		 *
		 * router.push(`/app/${newActiveOrganization.slug}`);
		 * 修改原因: 当用户从组织视图切换到个人视图时，当前实现没有将活跃组织设置为null并保存到服务器，导致状态不一致。
		 * 修改时间: 2025-04-29
		 * 修改人: LLM
		 * 关联需求: 修复个人视图切换中活跃组织状态的实施计划
		 */
		
		// 特殊处理个人视图切换
		if (organizationSlug === null) {
			await authClient.organization.setActive({
				organizationId: null,
			});
			
			// 更新本地会话状态
			await queryClient.setQueryData(sessionQueryKey, (data: any) => {
				return {
					...data,
					session: {
						...data?.session,
						activeOrganizationId: null,
					},
				};
			});
			
			// 跳转到个人视图
			router.push("/app");
			nProgress.done();
			return;
		}
		
		// 原有逻辑处理组织切换
		const { data: newActiveOrganization } =
			await authClient.organization.setActive({
				organizationSlug,
			});

		if (!newActiveOrganization) {
			nProgress.done();
			return;
		}

		await queryClient.setQueryData(
			activeOrganizationQueryKey(newActiveOrganization.slug),
			newActiveOrganization,
		);

		if (config.organizations.enableBilling) {
			await queryClient.prefetchQuery({
				queryKey: purchasesQueryKey(newActiveOrganization.id),
				queryFn: async () => {
					const response = await apiClient.payments.purchases.$get({
						query: {
							organizationId: newActiveOrganization.id,
						},
					});

					if (!response.ok) {
						throw new Error("Failed to fetch purchases");
					}

					return response.json();
				},
			});
		}

		await queryClient.setQueryData(sessionQueryKey, (data: any) => {
			return {
				...data,
				session: {
					...data?.session,
					activeOrganizationId: newActiveOrganization.id,
				},
			};
		});

		router.push(`/app/${newActiveOrganization.slug}`);
		nProgress.done();
	};

	const [loaded, setLoaded] = useState(activeOrganization !== undefined);

	useEffect(() => {
		if (!loaded && activeOrganization !== undefined) {
			setLoaded(true);
		}
	}, [activeOrganization]);

	const activeOrganizationUserRole = activeOrganization?.members.find(
		(member) => member.userId === session?.userId,
	)?.role;

	return (
		<ActiveOrganizationContext.Provider
			value={{
				loaded,
				activeOrganization: activeOrganization ?? null,
				activeOrganizationUserRole: activeOrganizationUserRole ?? null,
				isOrganizationAdmin:
					!!activeOrganization &&
					!!user &&
					isOrganizationAdmin(activeOrganization, user),
				setActiveOrganization,
				refetchActiveOrganization,
			}}
		>
			{children}
		</ActiveOrganizationContext.Provider>
	);
}
