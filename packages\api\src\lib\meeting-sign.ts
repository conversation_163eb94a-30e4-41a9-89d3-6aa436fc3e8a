// lib/tc3.ts
import { createHmac } from 'node:crypto';

export function generateSignature(
  httpMethod: string,
  requestUri: string,
  requestBody: string,
  timestamp: number,
  nonce: number,
  secretId: string,
  secretKey: string
): string {
  // 步骤1: 组织 headerString
    const headerString = `X-TC-Key=${secretId}&X-TC-Nonce=${nonce}&X-TC-Timestamp=${timestamp}`;

    

    // 步骤2: 组织签名串
    const stringToSign = `${httpMethod}\n${headerString}\n${requestUri}\n${requestBody}`;

    
    // console.log("签名字符串", { stringToSign });

    // 步骤3: 计算签名 - HMAC-SHA256
    const hmac = createHmac("sha256", secretKey);
    hmac.update(stringToSign);
    const hash = hmac.digest();

    // 步骤4: 将结果转换为16进制字符串
    const hexHash = hash.toString("hex").toLowerCase();

    // 步骤5: 对16进制字符串进行Base64编码
    const signature = Buffer.from(hexHash).toString("base64");

    // console.log("生成签名", { signature });

    return signature;
}
