# ContactsDialog 功能测试说明

## 已完成的功能集成

### 1. 数据层 (contacts_data.ts)
✅ 定义了完整的联系人数据类型
✅ 实现了增删改查四个API函数
✅ 使用了加密和签名机制
✅ 完善的错误处理

### 2. Hook层 (useContacts.ts)
✅ 修复了类型导入问题，使用 `type` 关键字
✅ 提供了查询Hook：`useContacts`
✅ 提供了变更Hook：`useCreateContact`, `useUpdateContact`, `useDeleteContact`
✅ 配置了5分钟缓存策略

### 3. 组件层 (ContactsDialog.tsx)
✅ 集成了真实的API调用
✅ 使用 `useActiveOrganization` 获取组织ID
✅ 实现了以下功能：

#### 新增联系人
- 点击+按钮创建新联系人卡片
- 自动进入编辑状态
- 点击保存按钮触发 `POST /api/investor-management/contacts/create`
- 成功后刷新列表显示新数据

#### 编辑现有联系人
- 点击编辑按钮进入编辑状态
- 点击保存按钮触发 `POST /api/investor-management/contacts/update`
- 成功后退出编辑状态

#### 删除联系人
- 新建未保存的联系人：直接从本地状态删除，不调用API
- 已存在的联系人：调用 `POST /api/investor-management/contacts/delete`

#### 搜索功能
- 输入搜索关键词会本地过滤显示结果
- 服务端搜索通过 `keyword` 参数实现

### 4. 导航集成 (Dialog.tsx)
✅ 点击"联系人"标签时才渲染 ContactsDialog 组件
✅ 触发 `POST /api/investor-management/contacts/list` 查询接口

## API接口映射

| 功能 | 接口路径 | 触发时机 |
|------|----------|----------|
| 查询联系人列表 | `POST /api/investor-management/contacts/list` | 点击联系人标签 |
| 创建联系人 | `POST /api/investor-management/contacts/create` | 新增卡片点击保存 |
| 更新联系人 | `POST /api/investor-management/contacts/update` | 现有卡片点击保存 |
| 删除联系人 | `POST /api/investor-management/contacts/delete` | 现有卡片点击删除 |

## 数据字段映射

| 前端字段 | 后端字段 | 说明 |
|----------|----------|------|
| contactId | contactId | 联系人ID |
| name | name | 姓名（必填） |
| phoneNumber | phoneNumber | 电话号码 |
| email | email | 邮箱 |
| address | address | 地址 |
| remarks | remarks | 备注 |
| organizationId | organizationId | 组织ID（必填） |

## 错误处理

✅ 表单验证：姓名为必填字段
✅ 并发编辑控制：同时只能编辑一个联系人
✅ 网络错误处理：显示错误消息
✅ 加载状态：显示加载中提示

## 用户体验优化

✅ 5分钟缓存策略，减少不必要的API调用
✅ 乐观更新：操作成功后立即更新UI
✅ 错误提示：操作失败时显示具体错误信息
✅ 加载状态：按钮禁用防止重复提交

## 下一步测试建议

1. 启动开发服务器测试UI交互
2. 确认后端API接口是否已实现
3. 测试加密解密功能是否正常
4. 验证组织权限控制
