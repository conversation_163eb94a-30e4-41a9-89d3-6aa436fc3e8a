/**
 * 投资人管理主路由
 * 聚合所有投资人管理相关的子路由
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建主路由，排除tags/sync.ts接口
 * @updated 2025-07-09 20:53:57 hayden 添加company-filter/get.ts接口，支持前端获取保存的公司代码
 * @description 投资人管理模块主路由，包括公司筛选配置、投资人标签和联系人管理功能
 */
import { Hono } from "hono";
import { companyFilterUpsertRouter } from "./company-filter/upsert";
import { companyFilterGetRouter } from "./company-filter/get";
import { tagsCreateRouter } from "./tags/create";
import { tagsDeleteRouter } from "./tags/delete";
import { contactsCreateRouter } from "./contacts/create";
import { contactsUpdateRouter } from "./contacts/update";
import { contactsDeleteRouter } from "./contacts/delete";
import { contactsListRouter } from "./contacts/list";

/**
 * 投资人管理路由器
 * 
 * 注意：根据要求排除以下接口：
 * - tags/sync.ts: 同步系统自动标签（由n8n处理）
 */
export const investorManagementRouter = new Hono()
  .basePath("/investor-management")
  .route("/company-filter", companyFilterUpsertRouter)
  .route("/company-filter", companyFilterGetRouter)
  .route("/tags", tagsCreateRouter)
  .route("/tags", tagsDeleteRouter)
  .route("/contacts", contactsCreateRouter)
  .route("/contacts", contactsUpdateRouter)
  .route("/contacts", contactsDeleteRouter)
  .route("/contacts", contactsListRouter);
