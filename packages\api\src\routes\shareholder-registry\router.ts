import { Hono } from "hono";
import { uploadRouter } from "./upload";
import { listRouter } from "./list";
import { registerDatesRouter } from "./register-dates";
import { shareholdersRouter } from "./shareholders";
import { deleteRouter } from "./delete";
import { shareholdingChangesRouter } from "./shareholding-changes";
import { shareholderTypesRouter } from "./shareholder-types";
import { classificationRulesRouter } from "./classification-rules";
import { classificationUpdateTimeRouter } from "./classification-update-time";

/**
 * 股东名册主路由
 * 聚合所有股东名册相关的子路由
 *
 * @update 2025-06-10 添加股东持股变化分析和股东类型查询接口
 * @update 2025-06-25 添加股东分类规则相关接口
 * <AUTHOR>
 * @time 2025-06-25 10:13:42
 */
export const shareholderRegistryRouter = new Hono()
  .basePath("/shareholder-registry")
  .route("/", uploadRouter)
  .route("/", listRouter)
  .route("/", registerDatesRouter)
  .route("/", shareholdersRouter)
  .route("/", deleteRouter)
  .route("/", shareholdingChangesRouter)
  .route("/", shareholderTypesRouter)
  .route("/", classificationRulesRouter)
  .route("/", classificationUpdateTimeRouter);