/**
 * 查询投资人联系人列表路由
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建联系人列表查询接口
 * @updated 2025-07-09 19:06:43 hayden 添加手机号码和邮箱的搜索功能，支持模糊搜索
 * @description 查询投资人联系人列表，支持分页、姓名/手机号码/邮箱搜索和排序功能
 */
import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { ListInvestorContactsSchema } from "../lib/validators";

export const contactsListRouter = new Hono().post(
  "/list",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");

      // 参数验证
      const validationResult = ListInvestorContactsSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { organizationId, name, phoneNumber, email, page, limit } = validationResult.data;

      // 构建查询条件
      const where = {
        organizationId,
        ...(name ? { name: { contains: name } } : {}),
        ...(phoneNumber ? { phoneNumber: { contains: phoneNumber } } : {}),
        ...(email ? { email: { contains: email } } : {}),
      };

      // 查询数据
      const [total, contacts] = await Promise.all([
        db.investorContact.count({ where }),
        db.investorContact.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip: (page - 1) * limit,
          take: limit,
        })
      ]);

      c.set("response", {
        code: 200,
        message: "查询成功",
        data: {
          contacts: contacts.map(contact => ({
            contactId: contact.contactId,
            name: contact.name,
            phoneNumber: contact.phoneNumber,
            email: contact.email,
            address: contact.address,
            remarks: contact.remarks,
            createdAt: contact.createdAt.toISOString(),
            updatedAt: contact.updatedAt ? contact.updatedAt.toISOString() : contact.createdAt.toISOString(),
            createdBy: contact.createdBy,
            updatedBy: contact.updatedBy || null,
          })),
          pagination: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
          }
        }
      });
      return;

    } catch (error) {
      if (error instanceof HTTPException) {
        c.set("response", {
          code: error.status,
          message: error.message,
          data: null
        });
        return;
      }
      
      console.error("投资人管理API错误:", error);
      c.set("response", {
        code: 500,
        message: "服务器内部错误",
        data: null
      });
      return;
    }
  }
);
