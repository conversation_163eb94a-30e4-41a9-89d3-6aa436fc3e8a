import { Dialog, DialogContent, DialogTitle } from "@ui/components/dialog";
import { FileIcon, UploadIcon, FileTextIcon, CheckIcon, InfoIcon, DatabaseIcon } from "lucide-react";

interface ShareholderRegistryHelpDialogProps {
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
}

/**
 * 股东名册说明对话框组件
 * 展示名册上传说明和要求
 *
 * <AUTHOR>
 * @update 2025-05-19 更新导入流程说明，新增校验流程的描述
 * @update 2025-06-23 16:53:58 修复 Tailwind CSS 类名重复问题，移除重复的 mt-0.5 类名
 */
export function ShareholderRegistryHelpDialog({
	isOpen,
	onOpenChange,
}: ShareholderRegistryHelpDialogProps) {
	return (
		<Dialog open={isOpen} onOpenChange={onOpenChange}>
			<DialogContent
				className="sm:max-w-[520px] border-0 shadow-xl p-0 rounded-xl"
			>
				{/* 顶部标题区域 */}
				<div
					className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/20 relative px-6 py-5"
				>
					<DialogTitle
						className="font-semibold text-gray-800 dark:text-gray-100 text-lg"
					>
						<div className="flex items-center">
							<InfoIcon
								className="mr-2 text-red-500 h-5 w-5"
							/>
							上传文件要求
						</div>
					</DialogTitle>

					{/* 关闭按钮 - 使用 Dialog 内置的关闭机制，无需额外实现 */}
				</div>

				{/* 内容区域 */}
				<div
					className="overflow-y-auto max-h-[calc(85vh-65px)]"
				>
					<div
						className="space-y-4 p-6 text-sm"
					>
						<div className="flex">
							<div
								className="shrink-0 mr-3 mt-0.5 text-red-500 bg-red-50 rounded-full p-1.5 dark:bg-red-950/30"
							>
								<FileIcon
									className="h-4 w-4"
								/>
							</div>
							<div>
								<h3
									className="font-medium text-gray-800 dark:text-gray-200 text-base mb-1.5"
								>
									支持的文件格式
								</h3>
								<p className="text-gray-600 dark:text-gray-400">
									支持DBF文件（.dbf）、Excel文件（.xlsx, .xls）和ZIP压缩文件（.zip）
								</p>
							</div>
						</div>

						<div className="flex">
							<div
								className="shrink-0 mr-3 mt-0.5 text-red-500 bg-red-50 rounded-full p-1.5 dark:bg-red-950/30"
							>
								<UploadIcon
									className="h-4 w-4"
								/>
							</div>
							<div>
								<h3
									className="font-medium text-gray-800 dark:text-gray-200 text-base mb-1.5"
								>
									文件大小限制
								</h3>
								<p className="text-gray-600 dark:text-gray-400">
									单个文件不超过100MB
								</p>
							</div>
						</div>

						<div className="flex">
							<div
								className="shrink-0 mr-3 mt-0.5 text-red-500 bg-red-50 rounded-full p-1.5 dark:bg-red-950/30"
							>
								<FileTextIcon
									className="h-4 w-4"
								/>
							</div>
							<div>
								<h3
									className="font-medium text-gray-800 dark:text-gray-200 text-base mb-1.5"
								>
									表格格式要求
								</h3>
								<p className="text-gray-600 dark:text-gray-400">
									文件需要包含以下必填字段：
								</p>
								<ul
									className="list-none mt-1.5 text-gray-600 dark:text-gray-400 space-y-1 ml-0"
								>
									{[
										"一码通账户号码（YMTH）",
										"证券账户名称（ZQZHMC）",
										"证件号码（ZJDM）",
										"持有人类别（CYRLBMS）",
									].map((item, index) => (
										<li
											key={index}
											className="flex items-start"
										>
											<CheckIcon
												className="text-red-500 mr-1.5 shrink-0 h-3.5 w-3.5 mt-0.5"
											/>
											<span>{item}</span>
										</li>
									))}
								</ul>
							</div>
						</div>

						<div className="flex">
							<div
								className="shrink-0 mr-3 mt-0.5 text-red-500 bg-red-50 rounded-full p-1.5 dark:bg-red-950/30"
							>
								<DatabaseIcon
									className="h-4 w-4"
								/>
							</div>
							<div>
								<h3
									className="font-medium text-gray-800 dark:text-gray-200 text-base mb-1.5"
								>
									导入流程说明
								</h3>
								<p className="text-gray-600 dark:text-gray-400 mb-1.5">
									系统将按照以下步骤处理文件：
								</p>
								<ol className="list-decimal ml-4 text-gray-600 dark:text-gray-400 space-y-1">
									<li>校验文件格式和必填字段</li>
									<li>验证数据完整性和有效性</li>
									<li>解析股东信息并导入系统</li>
								</ol>
								<p className="text-gray-600 dark:text-gray-400 mt-1.5">
									多文件导入时将按顺序逐个处理，若某个文件校验失败，可选择跳过该文件继续处理其他文件。
								</p>
							</div>
						</div>

						<div className="flex">
							<div
								className="shrink-0 mr-3 mt-0.5 text-red-500 bg-red-50 rounded-full p-1.5 dark:bg-red-950/30"
							>
								<InfoIcon
									className="h-4 w-4"
								/>
							</div>
							<div>
								<h3
									className="font-medium text-gray-800 dark:text-gray-200 text-base mb-1.5"
								>
									数据要求
								</h3>
								<p className="text-gray-600 dark:text-gray-400">
									数据需保证有效性和完整性，公司代码和报告日期需与文件名匹配。
								</p>
							</div>
						</div>
					</div>

					<div
						className="border-t bg-gray-50 dark:bg-gray-900/20 px-6 py-4 text-xs text-gray-500 dark:text-gray-400"
					>
						<p>
							点击开始导入后，系统将逐个验证文件并导入有效数据
						</p>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
} 