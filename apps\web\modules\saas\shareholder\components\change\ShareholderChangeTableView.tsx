"use client";

import { useMemo, useState, useCallback, useEffect, useRef } from "react";
import type {
	ShareholderChangeItem,
	PaginationInfo,
} from "@saas/shareholder/lib/types";
import {
	AntShareholderTable,
	type ShareholderColumn,
	type ShareholderItem,
} from "@saas/shareholder/components/ant-shareholder-table";
import { renderTruncatedWithTooltip } from "@saas/shareholder/lib/ui-utils";
import { TablePagination } from "@saas/shareholder/components";
import { EmptyState } from "@saas/shareholder/components/EmptyState";
import { useSystemScale } from "@saas/shareholder/hooks/useSystemScale";
import { cn } from "@ui/lib";

/**
 * ShareholderChangeTableView组件属性接口
 *
 * @interface ShareholderChangeTableViewProps
 * @property {ShareholderChangeItem[]} data - 股东持股变化数据
 * @property {boolean} loading - 加载状态
 * @property {string[]} [availableDates] - 可用的报告日期列表（备选方案，优先使用数据中的实际日期）
 * @property {(sortKey: string, sortOrder: 'asc' | 'desc', sortType?: 'rank' | 'date' | string) => void} onSort - 排序回调，支持具体日期格式
 * @property {PaginationInfo} [pagination] - 分页信息
 * @property {number} page - 当前页码
 * @property {(page: number) => void} onPageChange - 页码变更回调
 * @time 2025-06-11 15:34:28.994
 * @modified 2025-06-13 14:50:32.386 - 添加滚动分页相关属性
 * @modified 2025-06-13 15:47:37.867 - 更新availableDates说明，现在优先使用数据中的实际日期
 * @modified 2025-06-17 17:09:31.041 - 更新onSort回调，支持具体日期格式的sortType参数
 */
export interface ShareholderChangeTableViewProps {
	data: ShareholderChangeItem[];
	loading: boolean;
	availableDates?: string[]; // 修改为可选参数
	onSort: (
		sortKey: string,
		sortOrder: "asc" | "desc",
		sortType?: "rank" | "date" | string,
	) => void; // 修改于 2025-06-17 17:09:31.041，支持具体日期格式
	searchTerm?: string; // 添加搜索关键词属性 - 添加于 2025-06-26 11:10:16.447
	// 添加滚动分页相关属性 - 添加于 2025-06-13 14:50:32.386
	pagination?: PaginationInfo;
	page: number;
	onPageChange: (page: number) => void;
}

/**
 * 股东持股变化表格视图组件
 * 展示股东持股变化数据，支持滚动分页功能，点击排序时重置分页
 * 使用AntShareholderTable组件替代原有表格组件
 * 支持底部数据加载状态提示，显示加载进度和完成状态
 * 表头日期列根据后端返回的实际数据动态生成，确保表头与数据完全匹配
 * 日期列按降序排列，最新日期显示在前面
 *
 * <AUTHOR>
 * @created 2024-07-01 15:30:45.621
 * @modified 2025-06-13 14:50:32.386 - 添加滚动分页逻辑
 * @modified 2025-06-13 15:27:25.936 - 添加底部数据加载状态提示
 * @modified 2025-06-13 15:47:37.867 - 修复表头日期列动态渲染，从实际数据中提取日期而非依赖缓存
 * @modified 2025-06-16 - 调整日期列排序，最新日期排在前面
 * @modified 2025-06-16 - 修复排序时滚动位置重置问题，添加排序状态追踪
 * @modified 2025-06-16 - 统一使用AntShareholderTable的加载状态，移除自定义加载状态逻辑
 * @modified 2025-06-26 11:10:16.447 - 使用EmptyState组件替换原有空状态实现，不显示图标和搜索关键字
 * @modified 2025-06-26 11:21:42.400 - 修改表格头部样式，移除text-center类，与ShareholderTable.tsx保持一致
 * @modified 2025-06-26 11:26:19.491 - 添加useSystemScale hook，使用动态字体大小配置，与ShareholderTable.tsx保持字体一致性
 * @param {ShareholderChangeTableViewProps} props - 组件属性
 * @returns {JSX.Element} 股东持股变化表格视图组件
 * @time 2025-06-26 11:26:19.491
 */
export function ShareholderChangeTableView({
	data,
	loading,
	availableDates = [], // 添加可用日期列表，默认为空数组
	onSort,
	searchTerm,
	// 添加滚动分页相关参数 - 添加于 2025-06-13 14:50:32.386
	pagination,
	page,
	onPageChange,
}: ShareholderChangeTableViewProps): JSX.Element {
	// 使用系统缩放hook获取样式配置 - 添加于 2025-06-26 11:26:19.491，与ShareholderTable.tsx保持一致
	const { styles } = useSystemScale();

	// 滚动分页相关状态 - 修改于 2025-06-16，移除自定义加载状态，使用AntShareholderTable的加载状态
	const [accumulatedData, setAccumulatedData] = useState<
		ShareholderChangeItem[]
	>([]);
	const scrollRef = useRef({
		left: 0,
		top: 0,
	});
	const tableScrollRef = useRef<HTMLDivElement>(null); // 表格滚动容器引用
	// 添加排序状态追踪 - 添加于 2025-06-16，用于准确判断排序操作并重置滚动位置
	const [sortState, setSortState] = useState<{
		key: string;
		order: string;
	} | null>(null);

	// 定义加载状态的配置 - 添加于 2025-06-16，与其他表格组件保持一致
	const loadingConfig = useMemo(() => {
		return {
			spinning: loading,
			size: "large" as const,
		};
	}, [loading]);

	// 当滚动加载后续页数据时，进行累积 - 修改于 2025-06-13 15:14:18.803
	// 修复：只处理后续页数据累积，第一页数据重置在上面的useEffect中处理
	useEffect(() => {
		// console.log("ShareholderChangeTableView - 数据变化:", { page, dataLength: data.length, loading });

		// 只处理后续页数据的累积（页码大于1）
		if (page > 1) {
			// 后续页数据，进行累积（去重）
			setAccumulatedData((prevData) => {
				const existingIds = new Set(
					prevData.map(
						(item) => item.shareholderId || item.name || "",
					),
				);
				const newItems = data.filter(
					(item) =>
						!existingIds.has(item.shareholderId || item.name || ""),
				);
				return [...prevData, ...newItems];
			});
		}
	}, [data, page]);

	// 当排序变化时，重置滚动位置 - 修改于 2025-06-16
	// 修复：使用排序状态追踪来准确判断排序操作，避免滚动加载时意外重置滚动位置
	useEffect(() => {
		// 只有当排序状态变化时才重置滚动位置
		if (sortState) {
			// 重置滚动位置到顶部（仅在排序变化时）
			if (tableScrollRef.current) {
				tableScrollRef.current.scrollTo({ top: 0, behavior: "smooth" });
			}

			// 重置滚动记录
			scrollRef.current = { left: 0, top: 0 };
		}
	}, [sortState]); // 修复：只依赖于排序状态变化

	// 当排序变化且页码为1时，重置累积数据 - 添加于 2025-06-13 15:14:18.803
	useEffect(() => {
		// 只有当页码为1时才重置累积数据（表示是排序或筛选变化，而非滚动加载）
		if (page === 1) {
			setAccumulatedData(data);
		}
	}, [data, page, availableDates.length]); // 依赖于数据、页码和排序变化

	/**
	 * 格式化数字显示，为正数添加+号，负数保持-号
	 * @param numValue - 要格式化的数值
	 * @returns 格式化后的字符串
	 * <AUTHOR>
	 * @date 2025-06-18 14:34:22
	 * @modified 2025-06-18 14:34:22 - 修改formatNumber函数，为正数添加+号显示
	 */
	const formatNumber = (numValue: number | string) => {
		let value = numValue;
		if (typeof value === "string") {
			value = Number.parseFloat(value);
		}

		if (Number.isNaN(value)) {
			return "0";
		}

		// 原代码：直接返回千位分隔符格式
		// return (value as number).toLocaleString("zh-CN");

		// 修改：为正数添加+号，负数保持-号，0不添加符号
		const formattedValue = (value as number).toLocaleString("zh-CN");

		if (value > 0) {
			return `+${formattedValue}`;
		}

		return formattedValue; // 负数和0保持原样
	};

	// 处理表格滚动事件 - 修改于 2025-06-16，移除自定义加载状态，使用AntShareholderTable的加载状态
	const handleTableScroll = useCallback(
		(event: React.UIEvent<HTMLDivElement>) => {
			const { scrollTop, clientHeight, scrollHeight, scrollLeft } =
				event.target as HTMLDivElement;

			// 记录滚动容器引用
			if (!tableScrollRef.current && event.target) {
				tableScrollRef.current = event.target as HTMLDivElement;
			}

			// 计算是否还有更多数据可以加载
			const totalPages = pagination?.totalPages || 1;
			const hasMoreData = page < totalPages;

			// 垂直滚动 & 到底了 & 有更多数据 & 不在加载中
			if (
				Math.abs(scrollTop - scrollRef.current.top) > 0 &&
				scrollTop + clientHeight >= scrollHeight - 50 && // 提前50px触发加载
				hasMoreData &&
				!loading // 修改于 2025-06-16：直接使用loading状态，移除自定义isScrollLoading
			) {
				// 调用页面变更函数，加载下一页
				onPageChange(page + 1);
			}

			// 记录当前滚动信息
			scrollRef.current = {
				left: scrollLeft,
				top: scrollTop,
			};
		},
		[pagination?.totalPages, page, loading, onPageChange],
	);

	const scrollConfig = useMemo(() => {
		// 智能垂直滚动：只有当数据超过10行时才启用垂直滚动，避免少量数据时出现滚动条
		const shouldEnableYScroll = accumulatedData.length > 10;

		return {
			x: "max-content", // 启用水平滚动
			y: shouldEnableYScroll ? "calc(95vh - 300px)" : undefined, // 根据数据量决定是否启用垂直滚动
			scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
		};
	}, [accumulatedData.length]);

	// 从实际数据中提取可用日期列表，确保表头与数据完全匹配 - 修改于 2025-06-13 15:47:37.867
	// 原有逻辑：const sortedDates = [...availableDates].sort();
	// 修改于 2025-06-16 - 调整日期排序，最新日期排在前面
	const sortedDates = useMemo(() => {
		// 如果没有数据，使用传入的availableDates作为备选
		if (accumulatedData.length === 0) {
			return [...availableDates].sort((a, b) => {
				// 按日期降序排列，最新日期在前
				const dateA = new Date(a).getTime();
				const dateB = new Date(b).getTime();
				// 检查日期是否有效，无效日期排在最后
				if (Number.isNaN(dateA) && Number.isNaN(dateB)) {
					return 0;
				}
				if (Number.isNaN(dateA)) {
					return 1;
				}
				if (Number.isNaN(dateB)) {
					return -1;
				}
				return dateB - dateA; // 降序排列，最新日期在前
			});
		}

		// 从实际数据中提取所有存在的日期
		const actualDates = new Set<string>();
		accumulatedData.forEach((item) => {
			if (item.periodChanges && Array.isArray(item.periodChanges)) {
				item.periodChanges.forEach((periodChange) => {
					if (periodChange.date) {
						actualDates.add(periodChange.date);
					}
				});
			}
		});

		// 如果从数据中提取到了日期，使用实际日期；否则使用传入的availableDates
		const datesArray =
			actualDates.size > 0
				? Array.from(actualDates)
				: [...availableDates];

		// 按日期降序排列，最新日期在前
		return datesArray.sort((a, b) => {
			const dateA = new Date(a).getTime();
			const dateB = new Date(b).getTime();
			// 检查日期是否有效，无效日期排在最后
			if (Number.isNaN(dateA) && Number.isNaN(dateB)) {
				return 0;
			}
			if (Number.isNaN(dateA)) {
				return 1;
			}
			if (Number.isNaN(dateB)) {
				return -1;
			}
			return dateB - dateA; // 降序排列，最新日期在前
		});
	}, [accumulatedData, availableDates]);

	// 将ShareholderChangeItem转换为AntShareholderTable需要的ShareholderItem格式 - 修改于 2025-06-13 14:50:32.386，使用累积数据
	const tableData = useMemo(() => {
		return accumulatedData.map(
			(item, index): ShareholderItem => ({
				id: `row-${index}`,
				name: item.shareholderName || item.name || "",
				identifier: item.shareholderId || "",
				type: item.shareholderType || "",
				unifiedAccountNumber: item.unifiedAccountNumber || "",
				netChange: item.netChange,
				// 添加rank字段，使用索引+1作为排名
				rank: item.rank,
				// 添加shares属性，使用netChange作为shares值
				shares: item.netChange,
				// 添加每个日期的变动数据作为动态字段
				...item.periodChanges?.reduce(
					(acc, periodChange) => {
						acc[`change_${periodChange.date}`] =
							periodChange.change;
						return acc;
					},
					{} as Record<string, any>,
				),
				// 保存原始数据以供需要
				originalData: item,
			}),
		);
	}, [accumulatedData]);

	// 定义表格列配置
	const columns = useMemo((): ShareholderColumn[] => {
		// 基础列配置
		const baseColumns: ShareholderColumn[] = [
			{
				key: "rank",
				title: "排名",
				width: 60,
				sortable: true,
				className: "text-center", // 添加居中对齐样式
			},
			{
				key: "name",
				title: "股东名称",
				width: 180,
				className: "text-center", // 添加居中对齐样式
				render: (value: any) =>
					renderTruncatedWithTooltip(String(value), 25),
			},
			{
				key: "type",
				title: "股东类型",
				width: 200,
				className: "text-center", // 添加居中对齐样式
			},
			{
				key: "identifier",
				title: "证件号码",
				width: 180,
				className: "text-center font-mono", // 添加居中对齐样式，保留等宽字体
			},
			{
				key: "unifiedAccountNumber",
				title: "一码通",
				width: 120,
				className: "text-center font-mono", // 添加居中对齐样式，保留等宽字体
			},
			{
				key: "netChange",
				title: "范围内净变动",
				width: 140,
				sortable: true,
				className: "text-center", // 添加居中对齐样式
				render: (value, _record) => {
					const change = Number(value) || 0;
					return (
						<span>
							{formatNumber(change)}
						</span>
					);
				},
			},
		];

		// 为每个日期添加一个列
		const dateColumns = sortedDates.map(
			(date): ShareholderColumn => ({
				key: `change_${date}`,
				title: date,
				width: 120,
				sortable: true,
				className: "text-center", // 修改为居中对齐
				render: (value, _record) => {
					const change = Number(value) || 0;
					if (change === 0) {
						return "0";
					}

					return (
						<span>
							{formatNumber(change)}
						</span>
					);
				},
			}),
		);

		return [...baseColumns, ...dateColumns];
	}, [sortedDates]);

	// 处理表格排序 - 修改于 2025-06-17 17:09:31.041，更新sortType参数支持具体日期格式
	const handleSort = (sortKey: string, sortOrder: "asc" | "desc") => {
		// 更新排序状态，触发滚动位置重置 - 添加于 2025-06-16
		setSortState({ key: sortKey, order: sortOrder });

		// 重置分页到第一页 - 添加于 2025-06-13 14:50:32.386
		onPageChange(1);

		// 如果是日期列的排序，需要提取出实际的日期
		if (sortKey.startsWith("change_")) {
			const date = sortKey.replace("change_", "");
			// 调用父组件的排序方法，传入具体日期作为sortType，支持按指定期数排序
			onSort(`date_${date}`, sortOrder, date); // 修改：直接传入具体日期作为sortType
		} else {
			// 对于其他列（如rank、netChange等），使用rank排序类型
			onSort(sortKey, sortOrder, "rank");
		}
	};

	// 自定义空内容 - 修改于 2025-06-26 11:10:16.447，使用EmptyState组件替换原有实现
	const emptyContent = (
		<EmptyState
			text={loading ? "数据加载中..." : "请上传2份以上股东名册"}
			type={
				loadingConfig.spinning
					? "loading"
					: searchTerm
						? "search"
						: "empty"
			}
		/>
	);

	return (
		<div
			className={cn(
				"rounded-md",
				"h-[calc(94vh-200px)]",
				"flex",
				"flex-col",
			)}
		>
			<div className="flex-1 overflow-hidden">
				<AntShareholderTable
					data={tableData}
					columns={columns}
					loading={loadingConfig} // 修改于 2025-06-16：使用统一的loadingConfig，与其他表格组件保持一致
					onSort={handleSort}
					emptyContent={emptyContent}
					className="w-full"
					headerClassName="sticky top-0 z-10 font-medium"
					cellClassName={`text-center ${styles.fontSize.content}`}
					onRowClick={() => {}}
					onScroll={handleTableScroll}
					scroll={scrollConfig}
				/>
			</div>
			<div className="flex-shrink-0">
				<TablePagination
					pagination={
						pagination || {
							total: 0,
							page: 1,
							limit: 10,
							totalPages: 1,
						}
					}
					page={page}
				/>
			</div>
		</div>
	);
}
