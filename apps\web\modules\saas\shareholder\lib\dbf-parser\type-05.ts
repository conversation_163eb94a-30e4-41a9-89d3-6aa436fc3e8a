/**
 * 05名册DBF文件解析器
 * 专门用于处理05类型股东名册的DBF文件解析
 * 
 * 主要功能:
 * 1. 解析05名册特有的字段结构
 * 2. 提取05名册中的公司信息和股东信息
 * 3. 处理05名册特有的数据格式和规则
 * 
 * 05名册特点:
 * - 文件名格式为DQMC05_公司代码_日期.DBF
 * - 包含信用账户股东信息
 * - 包含汇总账户号码、汇总账户名称、股份性质等特有字段
 * 
 * @update 2025年06月03日 - 确保解析结果保留原始字段名(如YMTH、XYZHMC等)，
 * 而不是转换为数据库字段名。后端已实现字段映射机制，会自动将原始字段名映射到
 * 数据库字段。
 */

import { decode } from "iconv-lite";
import type {
	DbfData,
	DbfField,
	DbfRecord,
	ShareholderRegistryParseResult,
} from "./common";
import {
	parseDbfHeader,
	parseDbfFields,
	isValidDateString,
	isValidDate,
	validateRequiredFieldsContent,
} from "./common";
import { MAX_RECORDS_COUNT } from "../config";

// 05名册必填字段列表
const REQUIRED_FIELDS_05 = [
	{ name: "YMTH", label: "一码通账户号码" },
	{ name: "XYZHMC", label: "信用证券账户名称" },
	{ name: "XYZHZJDM", label: "信用证券账户证件号码" },
	{ name: "CYRLBMS", label: "持有人类别" },
];

/**
 * 检测文本是否包含乱码字符
 * 用于识别中文字段的编码问题
 *
 * @param text 要检查的文本
 * @returns 是否包含乱码
 */
function containsGarbledChars(text: string): boolean {
	if (!text) {
		return false;
	}

	// 检查常见乱码字符
	return (
		/[\ufffd\u0020-\u9fff\ue000-\uffff]/.test(text) ||
		text.length === 0 ||
		text.includes("\ufffd") ||
		// 检查异常的字符重复模式
		/([\u4e00-\u9fa5])\1{3,}/.test(text) ||
		// 检测特定字符（如"炽"）的异常重复，这可能表明编码错误
		/炽{2,}/.test(text)
	);
}

/**
 * 解析05名册DBF记录数据
 * 根据字段定义解析每条记录的具体数据
 *
 * @param buffer 文件二进制数据
 * @param headerInfo 文件头信息
 * @param fields 字段定义列表
 * @param encoding 字符编码(默认GBK)
 * @returns 解析后的记录列表
 */
function parseDbfRecords05(
	buffer: ArrayBuffer,
	headerInfo: {
		recordCount: number;
		headerLength: number;
		recordLength: number;
	},
	fields: DbfField[],
	encoding: string,
): DbfRecord[] {
	const records: DbfRecord[] = [];
	const view = new DataView(buffer);
	const { recordCount, headerLength, recordLength } = headerInfo;

	// 记录起始位置是头部信息结束的位置
	let offset = headerLength;

	for (let i = 0; i < recordCount; i++) {
		// 跳过记录标记字节 (1字节)
		offset += 1;

		const record: DbfRecord = {};

		// 读取每个字段的值
		for (const field of fields) {
			let value: string | number | boolean | Date | null;
			const fieldBuffer = new Uint8Array(
				buffer.slice(offset, offset + field.size),
			);

			// 根据字段类型处理数据
			if (field.type === "string") {
				// 使用指定编码解析字符串
				try {
					// 创建一个临时的原始字符串
					let rawString = "";
					for (let j = 0; j < fieldBuffer.length; j++) {
						rawString += String.fromCharCode(fieldBuffer[j]);
					}

					// 使用iconv-lite解码
					value = decode(
						Buffer.from(rawString, "binary"),
						encoding,
					).trim();

					// 特殊处理中文字段，特别是XYZHMC字段
					if (
						(field.name === "XYZHMC" ||
							field.name.includes("MC") ||
							field.name === "TXDZ" ||
							field.name === "HZZHMC") &&
						containsGarbledChars(value)
					) {
						// 如果有乱码，尝试不同的编码
						for (const altEncoding of [
							"gbk",
							"gb2312",
							"gb18030",
							"cp936",
						]) {
							if (altEncoding !== encoding) {
								const alternativeValue = decode(
									Buffer.from(rawString, "binary"),
									altEncoding,
								).trim();
								if (
									!containsGarbledChars(alternativeValue) &&
									alternativeValue.length > 0
								) {
									value = alternativeValue;
									break;
								}
							}
						}
					}
				} catch (e) {
					value = "";
				}
			} else if (field.type === "numeric") {
				// 转换为数字
				const numStr = String.fromCharCode
					.apply(null, Array.from(fieldBuffer))
					.trim();
				value = numStr === "" ? null : Number.parseFloat(numStr);
			} else if (field.type === "logical") {
				// 布尔值
				const boolChar = String.fromCharCode(
					fieldBuffer[0],
				).toUpperCase();
				value = boolChar === "T" || boolChar === "Y";
			} else if (field.type === "date") {
				// 日期 (YYYYMMDD格式)
				const dateStr = String.fromCharCode.apply(
					null,
					Array.from(fieldBuffer),
				);
				if (dateStr.trim() === "") {
					value = null;
				} else {
					const year = Number.parseInt(dateStr.substring(0, 4), 10);
					const month =
						Number.parseInt(dateStr.substring(4, 6), 10) - 1; // 月份从0开始
					const day = Number.parseInt(dateStr.substring(6, 8), 10);
					value = new Date(year, month, day);
				}
			} else {
				// 其他类型作为字符串处理
				value = String.fromCharCode
					.apply(null, Array.from(fieldBuffer))
					.trim();
			}

			record[field.name] = value;
			offset += field.size;
		}

		records.push(record);
	}

	return records;
}

/**
 * 解析05名册DBF文件
 * 完整解析05名册DBF文件的实现
 *
 * @param file 上传的DBF文件
 * @param encoding 字符编码，默认为gbk
 * @returns 解析后的完整数据
 */
export async function parseRawDbf05File(file: File, encoding = "gbk"): Promise<DbfData> {
	return new Promise((resolve, reject) => {
		const reader = new FileReader();

		reader.onload = async (event) => {
			try {
				if (!event.target || !event.target.result) {
					throw new Error("读取文件失败: FileReader无结果");
				}

				const arrayBuffer = event.target.result as ArrayBuffer;

				// 基本检查
				if (arrayBuffer.byteLength < 32) {
					throw new Error(
						`文件过小，不是有效的DBF文件 (${arrayBuffer.byteLength} 字节)`,
					);
				}

				try {
					// 使用DBF解析逻辑
					const headerInfo = parseDbfHeader(arrayBuffer);
					const fields = parseDbfFields(arrayBuffer);
					const records = parseDbfRecords05(
						arrayBuffer,
						headerInfo,
						fields,
						encoding,
					);

					// 构建结果
					const result: DbfData = {
						fields,
						records,
						version: headerInfo.version,
						lastUpdated: new Date(),
						recordCount: records.length,
					};

					resolve(result);
				} catch (error) {
					if (error instanceof Error) {
						reject(new Error(`解析DBF文件错误: ${error.message}`));
					} else {
						reject(new Error("解析DBF文件时发生未知错误"));
					}
				}
			} catch (error) {
				if (error instanceof Error) {
					reject(new Error(`读取文件错误: ${error.message}`));
				} else {
					reject(new Error("读取文件时发生未知错误"));
				}
			}
		};

		reader.onerror = (error) => {
			reject(
				new Error(
					`文件读取错误: ${error instanceof Error ? error.message : String(error)}`,
				),
			);
		};

		// 以ArrayBuffer形式读取文件
		try {
			reader.readAsArrayBuffer(file);
		} catch (readError) {
			reject(
				new Error(
					`文件读取调用错误: ${readError instanceof Error ? readError.message : String(readError)}`,
				),
			);
		}
	});
}

/**
 * 验证05名册记录中是否包含所有必填字段
 * 检查记录对象是否包含REQUIRED_FIELDS_05中定义的所有必填字段
 *
 * @param record 记录对象
 * @returns 缺失的字段列表
 */
export function validateRequiredFields05(
	record: any,
): Array<{ name: string; label: string }> {
	if (!record) {
		return REQUIRED_FIELDS_05;
	}

	// 原始验证逻辑
	return REQUIRED_FIELDS_05.filter((field) => !(field.name in record));
}

/**
 * 使用05名册DBF解析方法处理文件
 * 这是对外暴露的主要解析函数
 *
 * @param file DBF文件对象
 * @returns 解析结果
 */
export async function parseDBFFile05(
	file: File,
): Promise<ShareholderRegistryParseResult> {
	try {
		// 从文件名解析公司代码和报告日期
		const {
			companyCode: fileNameCompanyCode,
			registerDate: fileNameRegisterDate,
		} = parseFileName05(file.name);

		// 使用05名册DBF解析方法
		const dbfData = await parseRawDbf05File(file, "gbk");

		if (!dbfData.records || dbfData.records.length === 0) {
			return {
				success: false,
				fileName: file.name,
				error: {
					type: "FILE_ERROR",
					message: "DBF文件格式无效或不包含数据",
				},
			};
		}

		const records = dbfData.records;

		/**
		 * 确保所有数值类型数据转换为字符串格式
		 *
		 * <AUTHOR>
		 * @created 2025-06-24 17:17:04
		 * @description 修复05名册上传时数值字段类型验证失败的问题，确保所有数值字段都转换为字符串类型
		 */
		const numericFields = [
			'CGSL',      // 信用账户持股数量
			'DJGS',      // 冻结股数
			'XH'         // 序号
		];

		records.forEach(record => {
			// 系统化转换所有数值字段为字符串类型
			numericFields.forEach(field => {
				if (record[field] !== undefined && record[field] !== null) {
					record[field] = String(record[field]);
				}
			});
		});

		// 验证必填字段是否存在
		const missingFields = validateRequiredFields05(records[0]);
		
		if (missingFields.length > 0) {
			return {
				success: false,
				fileName: file.name,
				error: {
					type: "MISSING_FIELDS",
					message: `名册中缺少字段"${missingFields.map((f) => f.label).join('", "')}"`,
					details: missingFields.map((f) => f.label),
				},
			};
		}

		// 查找特殊记录（XH为0的记录）
		const specialRecords = records.filter(
			(rec: any) => rec.XH === "0" || rec.XH === 0,
		);

		// 从特殊记录中提取公司代码和日期
		let companyCode: string | undefined;
		let registerDate: string | undefined;
		let companyName: string | undefined;

		// 收集所有XH=0记录的DHHM、TXDZ和YMTH字段值，用于校验
		const allDates: string[] = [];
		const allCompanyCodes: string[] = [];
		const invalidDateRecords: Array<{ xh: number; date: string }> = [];

		specialRecords.forEach((rec: any) => {
			// 收集所有特殊记录的DHHM（日期）和YMTH（公司代码）字段
			if (rec.DHHM) {
				// 验证日期格式是否有效
				if (isValidDateString(rec.DHHM)) {
					allDates.push(rec.DHHM);
				} else {
					invalidDateRecords.push({ xh: rec.XH, date: rec.DHHM });
				}
			}

			// 收集TXDZ字段作为期数时间
			if (rec.TXDZ) {
				// 验证日期格式是否有效
				if (isValidDateString(rec.TXDZ)) {
					allDates.push(rec.TXDZ);
				} else {
					invalidDateRecords.push({ xh: rec.XH, date: rec.TXDZ });
				}
			}

			if (rec.YMTH) {
				allCompanyCodes.push(rec.YMTH);
			}

			if (rec.XH === "0" || rec.XH === 0) {
				// 从XH=0记录中提取公司代码和期数
				companyCode = rec.YMTH; // 公司代码存储在YMTH字段中
				companyName = ""; // 公司名称存储在TXDZ字段中
				registerDate = rec.DHHM; // 期数日期存储在DHHM字段中
				registerDate = rec.TXDZ;

			}
		});

		// 检查是否有无效日期记录
		if (invalidDateRecords.length > 0) {
			const details = invalidDateRecords
				.map((record) => `XH=${record.xh}, 日期=${record.date}`)
				.join("; ");

			return {
				success: false,
				fileName: file.name,
				error: {
					type: "DATE_MISMATCH",
					message: `文件中包含无效日期: ${details}`,
					details: [details],
				},
			};
		}

		// 验证所有收集到的日期是否一致
		if (allDates.length > 1) {
			const uniqueDates = Array.from(new Set(allDates));
			if (uniqueDates.length > 1) {
				return {
					success: false,
					fileName: file.name,
					error: {
						type: "DATE_MISMATCH",
						message: `文件中特殊记录的日期不一致: ${uniqueDates.join(", ")}`,
					},
				};
			}
		}

		// 验证所有收集到的公司代码是否一致
		if (allCompanyCodes.length > 1) {
			const uniqueCompanyCodes = Array.from(new Set(allCompanyCodes));
			if (uniqueCompanyCodes.length > 1) {
				return {
					success: false,
					fileName: file.name,
					error: {
						type: "COMPANY_MISMATCH",
						message: `文件中特殊记录的公司代码不一致: ${uniqueCompanyCodes.join(", ")}`,
					},
				};
			}
		}

		// 验证报告日期的有效性
		if (registerDate && !isValidDateString(registerDate)) {
			return {
				success: false,
				fileName: file.name,
				companyCode,
				registerDate,
				error: {
					type: "DATE_MISMATCH",
					message: `文件中的报告日期(${registerDate})无效`,
				},
			};
		}

		// 验证公司代码是否匹配（文件名中的公司代码与DBF中的YMTH字段比较）
		if (
			fileNameCompanyCode &&
			companyCode &&
			// 修复：在比较前对公司代码进行格式化处理，去除空格和不可见字符
			String(fileNameCompanyCode).trim() !== String(companyCode).trim()
		) {
			return {
				success: false,
				fileName: file.name,
				companyCode,
				registerDate,
				error: {
					type: "COMPANY_MISMATCH",
					message: `文件名中的公司代码(${fileNameCompanyCode})与文件内容中的公司代码(${companyCode})不匹配`,
				},
			};
		}

		// 验证日期是否匹配（文件名中的报告日期与DBF中的DHHM或TXDZ字段比较）
		// 注意：DHHM和TXDZ格式为YYYY-MM-DD，与文件名转换后的格式一致
		if (
			fileNameRegisterDate &&
			registerDate &&
			fileNameRegisterDate !== registerDate
		) {
			// 检查是否有任何特殊记录中的TXDZ字段与文件名日期匹配
			const hasTXDZMatch = specialRecords.some(
				(rec: any) => rec.TXDZ === fileNameRegisterDate
			);

			// 如果没有任何字段匹配文件名日期，则报错
			if (!hasTXDZMatch) {
				return {
					success: false,
					fileName: file.name,
					companyCode,
					registerDate,
					error: {
						type: "DATE_MISMATCH",
						message: `文件名中的报告日期(${fileNameRegisterDate})与文件内容中的报告日期(${registerDate})不匹配`,
					},
				};
			}
		}

		// 验证必填字段内容不为空
		const emptyFields = validateRequiredFieldsContent(records);
		if (emptyFields.length > 0) {
			return {
				success: false,
				fileName: file.name,
				error: {
					type: "EMPTY_FIELDS",
					message: `名册中字段"${emptyFields.join('", "')}"缺少内容`,
					details: emptyFields,
				},
			};
		}

		// 过滤掉特殊记录，只保留实际股东记录
		const shareholderRecords = records.filter(
			(rec: any) =>
				rec.XH !== "0" &&
				rec.XH !== 0 &&
				(typeof rec.XH === "string" ? Number(rec.XH) > 0 : rec.XH > 0),
		);

		// 再次确保所有股东记录中的数值字段都是字符串类型
		shareholderRecords.forEach(record => {
			// 确保所有必要的数值字段都转换为字符串
			["CGSL", "DJGS"].forEach(field => {
				if (record[field] !== undefined && record[field] !== null) {
					record[field] = String(record[field]);
				}
			});
		});

		/**
		 * 计算信用股东统计数据（全量数据）
		 *
		 * <AUTHOR>
		 * @created 2025-06-13 12:54:01
		 * @description 根据05名册的CGSL字段统计信用总户数和信用总持股
		 */
		let marginAccounts = 0;
		let marginShares = 0;

		// 统计全量数据的信用股东信息
		shareholderRecords.forEach(record => {
			/**
			 * 处理CGSL字段的各种情况
			 *
			 * <AUTHOR>
			 * @created 2025-06-13 13:49:20
			 * @description 处理CGSL字段可能为空白、null、undefined、0等情况
			 */
			const cgslValue = Number(record.CGSL || 0);

			/**
			 * 修改统计逻辑：统计全部股东，不排除CGSL为0或空白的情况
			 *
			 * <AUTHOR>
			 * @created 2025-06-13 13:49:20
			 * @description 根据需求，marginAccounts和marginShares需要统计全部股东，包括CGSL为0或空白的情况
			 */
			if (cgslValue > 0) { marginAccounts += 1; marginShares += cgslValue; }
		});

		/**
		 * 按CGSL字段降序排序并取前200个股东
		 *
		 * <AUTHOR>
		 * @created 2025-06-13 12:54:01
		 * @description 根据需求，只上传CGSL字段值较大的前200个股东
		 */
		const sortedRecords = [...shareholderRecords].sort((a, b) => {
			const cgslA = Number(a.CGSL || 0);
			const cgslB = Number(b.CGSL || 0);
			return cgslB - cgslA; // 降序排序
		});

		// 获取前200个股东记录
		const limitedRecords = sortedRecords.slice(0, MAX_RECORDS_COUNT);

		// 组装结果
		return {
			success: true,
			fileName: file.name,
			companyCode: companyCode || fileNameCompanyCode,
			registerDate: registerDate || fileNameRegisterDate,
			records: limitedRecords, // 使用排序后的前200个记录
			recordCount: limitedRecords.length,
			companyInfo: {
				companyName: companyName || "",
				totalShares: "0", // 05名册没有总股数信息
				totalShareholders: 0, // 05名册没有总户数信息
				totalInstitutions: 0, // 05名册没有机构户数信息
				largeSharesCount: "0", // 05名册没有大股东持股数信息
				largeShareholdersCount: 0, // 05名册没有大股东户数信息
				institutionShares: "0", // 05名册没有机构持股数信息
				/**
				 * 新增信用股东统计字段
				 *
				 * <AUTHOR>
				 * @created 2025-06-13 12:54:01
				 * @description 基于全量数据计算的信用股东统计信息
				 */
				marginAccounts, // 信用总户数（全量数据统计）
				marginShares: marginShares.toString(), // 信用总持股（全量数据统计）
			},
		};
	} catch (error) {
		return {
			success: false,
			fileName: file.name,
			error: {
				type: "FILE_ERROR",
				message:
					error instanceof Error ? error.message : "解析DBF文件失败",
			},
		};
	}
}

/**
 * 解析05名册文件名
 * 从文件名中提取公司代码和报告日期信息
 *
 * @param fileName 文件名，标准格式为DQMC05_001339_20240930.DBF
 * @returns 解析结果对象，包含公司代码和报告日期
 */
export function parseFileName05(fileName: string): {
	companyCode?: string;
	registerDate?: string;
} {
	try {
		// 文件名示例: DQMC05_001339_20240930.DBF
		const nameParts = fileName.split("_");

		if (nameParts.length >= 3) {
			// 提取公司代码，通常是文件名中的第二部分
			const companyCode = nameParts[1]; // 例如：001339

			// 提取日期部分，通常是文件名中的第三部分（去掉扩展名）
			let dateStr = nameParts[2]; // 例如：20240930.DBF
			// 移除可能的文件扩展名
			dateStr = dateStr.split(".")[0]; // 例如：20240930

			// 尝试从不同格式中解析日期
			let year: string;
			let month: string;
			let day: string;

			// 标准8位格式 YYYYMMDD
			if (dateStr.length === 8) {
				year = dateStr.substring(0, 4); // 2024
				month = dateStr.substring(4, 6); // 09
				day = dateStr.substring(6, 8); // 30
			}
			// 处理异常格式，例如9位或其他格式
			else if (dateStr.length > 8) {
				// 尝试仍然提取前8位作为YYYYMMDD
				const firstEight = dateStr.substring(0, 8);
				year = firstEight.substring(0, 4); // 2024
				month = firstEight.substring(4, 6); // 09
				day = firstEight.substring(6, 8); // 30
			} else {
				return { companyCode };
			}

			// 验证提取的日期是否有效
			if (!isValidDate(Number(year), Number(month) - 1, Number(day))) {
				// 日期无效
				return { companyCode };
			}

			// 转换为YYYY-MM-DD格式，与DBF文件中DHHM字段的格式一致
			const registerDate = `${year}-${month}-${day}`;

			return { companyCode, registerDate };
		}

		// 文件名格式不符合预期
		return {};
	} catch (error) {
		// 解析文件名失败
		return {};
	}
} 