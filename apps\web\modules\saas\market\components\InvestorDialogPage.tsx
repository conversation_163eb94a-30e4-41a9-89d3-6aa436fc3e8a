// "use client";
// import { useState, useEffect, useRef } from "react";
// import { MOCK_FUNDS } from "../lib/TestData";
// // import ListDetail from "./ListDetail";
// import { Button } from "@ui/components/button";
// import { Avatar, AvatarImage } from "@ui/components/avatar";
// import { Textarea } from "@ui/components/textarea";
// import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@ui/components/dropdown-menu";
// import { MoreVertical } from "lucide-react";
// import { Tooltip, TooltipTrigger, TooltipContent } from "@ui/components/tooltip";
// import { PanelRightClose, PanelRightOpen } from "lucide-react";
// import { TooltipProvider } from "@ui/components/tooltip";
// import { cn } from "@ui/lib";
// import { usePathname } from "next/navigation";
// // import type { FundCard } from "./ContextCard";

// //对话框 投资人界面



// interface Comment {
//   id: string;
//   author: string; // 当前用户
//   avatar: string;
//   content: string;
//   createdAt: number;
//   isVisible?: boolean; // 隐蔽
// }

// // 格式化相对时间
// function formatRelativeTime(date: number): string {
//   const now = Date.now();
//   const diff = Math.floor((now - date) / 1000);
//   if (diff < 60) { return "现在"; }
//   if (diff < 3600) { return `${Math.floor(diff / 60)}分钟前`; }
//   const d = new Date(date);
//   return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`;
// }

// // 全局评论变量
// let globalComments: Comment[] = []; // 后续存到后端 API 中  现在刷新会消失

// export default function InvestorDialogPage({ slug }: { slug: string }) {
//   const [investors, setInvestors] = useState([...MOCK_FUNDS]);
//   const investor = investors.find(
//     c => `${c.name}-${c.code}` === slug // 根据slug查找投资人数据
//   );
//   const [comments, setComments] = useState<Comment[]>(() => globalComments.map(c => ({ ...c, isVisible: true })));
//   const [inputValue, setInputValue] = useState("");
//   const [showDetail, setShowDetail] = useState(true);
//   const [isCardVisible, setIsCardVisible] = useState(true);
//   const prevCommentsRef = useRef<Comment[]>(comments);
//   const pathname = usePathname();
//   const match = pathname.match(/^\/app\/([^/]+)\/market/);
//   const organizationSlug = match ? match[1] : "";

//   useEffect(() => {
//     globalComments = comments;
//   }, [comments]);

//   function handleSend() {
//     if (!inputValue.trim()) { return; } // 如果输入为空，则不发送
//     const newComment = {
//       id: Math.random().toString(36).slice(2),
//       author: organizationSlug,
//       avatar: MOCK_FUNDS[0].managerAvatar,
//       content: inputValue,
//       createdAt: Date.now(),
//       isVisible: false,
//     };
//     setComments([
//       ...comments,
//       newComment
//     ]);
//     setInputValue("");
//   }

//   useEffect(() => {
//     const prev = prevCommentsRef.current;
//     if (
//       comments.length > prev.length &&
//       comments[comments.length - 1].isVisible === false
//     ) {
//       const timer = setTimeout(() => {
//         setComments(prevList => prevList.map((c, i) =>
//           i === prevList.length - 1 ? { ...c, isVisible: true } : c
//         ));
//       }, 10);
//       prevCommentsRef.current = comments;
//       return () => clearTimeout(timer);
//     }
//     prevCommentsRef.current = comments;
//   }, [comments]); // 监听comments变化，如果变化，则更新 prevCommentsRef

//   function handleDelete(commentId: string) {
//     setComments(prev =>
//       prev.map(c =>
//         c.id === commentId ? { ...c, isVisible: false } : c
//       )
//     );
//     setTimeout(() => {
//       setComments(prev => prev.filter(c => c.id !== commentId));
//     }, 300);
//   }

//   function handleToggleCard() {
//     if (showDetail) {
//       setShowDetail(false);
//       setTimeout(() => setIsCardVisible(false), 300);
//     } else {
//       setIsCardVisible(true);
//       setTimeout(() => setShowDetail(true), 10);
//     }
//   }

//   if (!investor) {
//     return <div className="flex items-center justify-center h-96 text-muted-foreground">未找到投资人信息</div>;
//   }

//   return (
// 			<TooltipProvider>
// 				<div className={cn("flex", showDetail && "gap-8", "relative")}>
// 					{/* 右上角显示/隐藏卡片按钮，始终可见 */}
// 					<div className="absolute right-2 top-2 z-10">
// 						<Tooltip>
// 							<TooltipTrigger asChild>
// 								<Button
// 									variant="ghost"
// 									size="icon"
// 									onClick={handleToggleCard}
// 									aria-label={
// 										showDetail
// 											? "隐藏投资人信息"
// 											: "显示投资人信息"
// 									}
// 								>
// 									{showDetail ? (
// 										<PanelRightClose size={20} />
// 									) : (
// 										<PanelRightOpen size={20} />
// 									)}
// 								</Button>
// 							</TooltipTrigger>
// 							<TooltipContent>
// 								{showDetail
// 									? "隐藏投资人信息"
// 									: "显示投资人信息"}
// 							</TooltipContent>
// 						</Tooltip>
// 					</div>
// 					{/* 左侧评论区 */}
// 					<div className="flex-1 flex flex-col bg-white dark:bg-zinc-900 rounded-xl shadow p-0 border border-gray-200 dark:border-zinc-700 transition-all duration-300">
// 						<div className="px-6 pt-6 pb-2 text-lg font-semibold">
// 							Activity
// 						</div>
// 						<div className="flex-1 overflow-y-auto px-6 pb-4">
// 							{comments.length === 0 && (
// 								<div className="flex flex-col items-center justify-center gap-2 py-8">
// 									<Avatar className="w-10 h-10 rounded-full">
// 										<AvatarImage
// 											src={MOCK_FUNDS[0].managerAvatar}
// 											alt="No comments"
// 											className="rounded-full"
// 										/>
// 									</Avatar>
// 									<div className="text-muted-foreground text-sm">
// 										No comments
// 									</div>
// 								</div>
// 							)}
// 							{comments.map((comment, idx) => (
// 								<div
// 									key={comment.id}
// 									data-state={
// 										comment.isVisible === false
// 											? "closed"
// 											: "open"
// 									}
// 									className={cn(
// 										"flex items-start gap-3 mb-6 relative transition-all duration-300",
// 										"data-[state=open]:slide-in-from-top data-[state=open]:max-h-40 data-[state=open]:opacity-100 data-[state=open]:translate-y-0",
// 										"data-[state=closed]:max-h-0 data-[state=closed]:opacity-0 data-[state=closed]:-translate-y-2 overflow-hidden",
// 									)}
// 								>
// 									{/* 时间线竖线（首条不显示） */}
// 									{idx > 0 && (
// 										<div className="absolute left-4 top-8 bottom-0 w-px bg-gray-200 z-0" />
// 									)}
// 									{/* 头像 */}
// 									<Avatar className="w-8 h-8 shrink-0 z-10 rounded-full">
// 										<AvatarImage
// 											src={comment.avatar}
// 											alt={comment.author}
// 											className="rounded-full"
// 										/>
// 									</Avatar>
// 									{/* 气泡卡片 */}
// 									<div className="bg-white dark:bg-zinc-900 rounded-lg shadow px-4 py-2 min-w-[180px] max-w-[320px] border border-gray-200 dark:border-zinc-700 relative">
// 										<div className="flex items-center gap-2 mb-3">
// 											<span className="font-medium text-sm">
// 												{comment.author}
// 											</span>
// 											<span className="text-xs text-muted-foreground">
// 												{formatRelativeTime(
// 													comment.createdAt,
// 												)}
// 											</span>
// 										</div>
// 										<div className="text-sm">
// 											{comment.content}
// 										</div>
// 										{/* 三点菜单 */}
// 										<DropdownMenu>
// 											<DropdownMenuTrigger asChild>
// 												<Button
// 													variant="ghost"
// 													size="icon"
// 													className="absolute right-2 top-2"
// 												>
// 													<MoreVertical size={16} />
// 												</Button>
// 											</DropdownMenuTrigger>
// 											<DropdownMenuContent align="end">
// 												<DropdownMenuItem
// 													onClick={() =>
// 														handleDelete(comment.id)
// 													}
// 													className="text-destructive"
// 												>
// 													删除
// 												</DropdownMenuItem>
// 											</DropdownMenuContent>
// 										</DropdownMenu>
// 									</div>
// 								</div>
// 							))}
// 						</div>
// 						<div className=" dark:border-zinc-700 px-6 py-4 bg-muted dark:bg-zinc-800">
// 							<div className="flex items-end gap-2 bg-white dark:bg-zinc-900 rounded-lg shadow px-4 py-2">
// 								<Textarea
// 									value={inputValue}
// 									onChange={(e) =>
// 										setInputValue(e.target.value)
// 									}
// 									placeholder="Write your comment..."
// 									className="border-0 shadow-none flex-1 rounded-lg resize-none min-h-[96px] max-h-56 focus:outline-none focus:ring-0 focus:border-transparent focus-visible:ring-0 bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"
// 								/>
// 								<Button
// 									onClick={handleSend}
// 									disabled={!inputValue.trim()}
// 									size="md"
// 									className="bg-primary dark:bg-primary text-white dark:text-white"
// 								>
// 									Comment
// 								</Button>
// 							</div>
// 						</div>
// 					</div>
// 					{/* 右侧信息卡片-动态渲染 */}
// 					{isCardVisible && (
// 						<div
// 							data-state={showDetail ? "open" : "closed"}
// 							className={cn(
// 								"w-[340px] shrink-0 relative transition-all duration-300",
// 								"data-[state=open]:slide-in-from-right data-[state=closed]:slide-out-to-right",
// 								"data-[state=open]:animate-in data-[state=closed]:animate-out",
// 							)}
// 						>
// 							{/* <ListDetail
// 								// investor={investor as unknown as Investor}
// 								onDelete={handleDeleteInvestor}
// 							/> */}
// 						</div>
// 					)}
// 				</div>
// 			</TooltipProvider>
// 		);
// }