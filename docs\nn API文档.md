# NN API 文档

**基础URL:** `http://192.168.138.244:6789/webhook`

**文档更新时间:** 2025-06-27 15:49:08

---

## 模块一：公司信息概览

### 1. 公司信息概览接口

**接口描述:** 获取最新一期公司股东信息概览及较上期变化数据

**请求信息:**
- **URL:** `/company-info`
- **HTTP方法:** `POST`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 组织ID，长度5-50个字符 |

#### 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |
| data | object | 数据内容 |

##### data 对象字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| companyName | string | 公司全称 |
| companyCode | string | 公司股票代码 |
| registerDate | string | 最新一期登记日期，数据基于此日期 |
| oldestRegisterDate | string | 该公司最早有数据的登记日期 |
| totalShares | number | 最新一期公司总股本（单位：股） |
| totalShareholders | number | 最新一期公司全部股东数量 |
| totalInstitutions | number | 最新一期机构投资者数量 |
| institutionShares | number | 最新一期机构投资者合计持股数（单位：股） |
| individualShareholders | number | 最新一期个人股东数量（=总股东数-机构投资者数） |
| prevTotalShares | number | 上一期公司总股本（单位：股） |
| prevTotalShareholders | number | 上一期公司全部股东数量 |
| prevTotalInstitutions | number | 上一期机构投资者数量 |
| prevInstitutionShares | number | 上一期机构投资者合计持股数（单位：股） |
| prevIndividualShareholders | number | 上一期个人股东数量 |
| shareholdersChange | number | 股东总人数变化（最新一期 - 上一期） |
| institutionsChange | number | 机构投资者数量变化（最新一期 - 上一期） |
| individualShareholdersChange | number | 个人股东数量变化（最新一期 - 上一期） |
| totalSharesChangePercent | number | 总股本变动百分比（%），正数为增长，负数为减少 |
| institutionSharesChangePercent | number | 机构持股变动百分比（%），正数为增长，负数为减少 |
| creditAccountCount | number | 最新一期信用账户数量 |
| totalMarginShares | number | 最新一期信用账户对应的总持股数（单位：股） |
| prevCreditAccountCount | number | 上一期信用账户数量 |
| prevTotalMarginShares | number | 上一期信用账户对应的总持股数（单位：股） |
| creditAccountCountChange | number | 信用账户数量变化（最新一期 - 上一期） |
| totalMarginSharesChangePercent | number | 信用账户持股数量变化百分比（%），正数为增长，负数为减少 |
| top10ShareholdingRatio | number | 最新一期前十大股东合计持股比例（%） |
| top20ShareholdingRatio | number | 最新一期前二十大股东合计持股比例（%） |
| avgSharesPerHolder | number | 最新一期户均持股数（股） |
| prevTop10ShareholdingRatio | number | 上一期前十大股东合计持股比例（%） |
| prevTop20ShareholdingRatio | number | 上一期前二十大股东合计持股比例（%） |
| prevAvgSharesPerHolder | number | 上一期户均持股数（股） |
| top10RatioChange | number | 前十大股东合计持股比例变化（最新一期 - 上一期，单位：%） |
| top20RatioChange | number | 前二十大股东合计持股比例变化（最新一期 - 上一期，单位：%） |
| avgSharesPerHolderChange | number | 户均持股数变化（最新一期 - 上一期，单位：股） |
| top1_shareholding_amount | number | 最新一期第一大股东持股数量（股） |
| top1_shareholding_ratio | number | 最新一期第一大股东持股比例（%） |
| top10_shareholding_amount | number | 最新一期前十大股东合计持股数量（股） |
| top10_shareholding_ratio | number | 最新一期前十大股东合计持股比例（%） |
| top100_shareholding_amount | number | 最新一期前一百大股东合计持股数量（股） |
| top100_shareholding_ratio | number | 最新一期前一百大股东合计持股比例（%） |
| shareholderTypes | array | 股东类型分布数据 |

##### shareholderTypes 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| shareholderType | string | 股东类型名称 |
| typeCount | string | 该类型股东数量 |
| typeShares | string | 该类型股东持股总数（股） |
| typePercentage | string | 该类型股东数量占总股东数比例（%） |
| sharesPercentage | string | 该类型股东持股占总股本比例（%） |

#### 错误码

| HTTP状态码 | 错误码 | 说明 |
|------------|--------|------|
| 400 | MISSING_ORGANIZATION_ID | 缺少必需参数：organizationId |
| 400 | INVALID_ORGANIZATION_ID | 无效的organizationId：参数必须是非空字符串 |
| 400 | ORGANIZATION_ID_LENGTH_INVALID | 无效的organizationId长度：参数长度必须在5-50个字符之间 |
| 500 | NO_DATA_FOUND | 未找到指定组织的数据，请检查organizationId是否正确 |
| 500 | INTERNAL_ERROR | 服务内部错误，请稍后重试 |


### 2. 总户数趋势接口

**接口描述:** 获取公司股东总户数历史趋势数据

**请求信息:**
- **URL:** `/company-analysis-trending`
- **HTTP方法:** `GET`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 组织ID，长度5-50个字符 |

#### 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |
| data | object | 数据内容 |

##### data 对象字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| trendData | array | 历史趋势数据 |

##### trendData 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| registerDate | string | 登记日期（ISO 日期时间字符串） |
| totalShareholders | number | 股东总人数 |

#### 错误码

| HTTP状态码 | 错误码 | 说明 |
|------------|--------|------|
| 400 | MISSING_ORGANIZATION_ID | 缺少必需参数：organizationId |
| 400 | INVALID_ORGANIZATION_ID | 无效的organizationId：参数必须是非空字符串 |
| 400 | ORGANIZATION_ID_LENGTH_INVALID | 无效的organizationId长度：参数长度必须在5-50个字符之间 |
| 500 | NO_TREND_DATA_FOUND | 未找到指定组织的趋势数据，请检查organizationId是否正确 |
| 500 | INTERNAL_ERROR | 服务内部错误，请稍后重试 |

#### 示例请求

```http
GET /company-analysis?id=your_organization_id
GET /company-analysis-trending?id=your_organization_id
```

#### 示例响应

**成功响应:**
```json
{
  "success": true,
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    // 对应的数据字段...
  }
}
```

**错误响应:**
```json
{
  "code": "MISSING_ORGANIZATION_ID",
  "message": "缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "type": "VALIDATION_ERROR"
}
```

---

## 模块二：股东结构历史趋势

### 股东结构历史趋势接口

**接口描述:** 获取某段时间范围公司股东信息概览

**请求信息:**
- **URL:** `/shareholders-trend-analysis`
- **HTTP方法:** `POST`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 组织ID，长度5-50个字符 |

#### 请求体示例

```json
{
  "id": "ORG123456"
}
```

#### 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |
| data | object | 数据内容 |

##### data 对象字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| trendData | array | 每期股东结构数据列表 |

##### trendData 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| registerDate | string | 登记日期（ISO 日期时间字符串） |
| companyName | string | 公司名称 |
| companyCode | string | 公司股票代码 |
| total_shares | number | 总股本（股） |
| total_shareholders | number | 股东总人数 |
| institutional_shareholders | number | 机构投资者数量 |
| institutional_shares | number | 机构投资者合计持股数（股） |
| institutional_shares_ratio | number | 机构投资者持股占总股本比例（%） |
| individual_shareholders | number | 个人股东数量（=总股东数-机构投资者数） |
| individual_shares | number | 个人股东合计持股数（股） |
| individual_shares_ratio | number | 个人股东持股占总股本比例（%） |
| credit_shareholders | number | 信用账户股东数量 |
| credit_shares | number | 信用账户持股总数（股） |
| credit_shares_ratio | number | 信用账户持股占总股本比例（%） |
| top10_shareholding_amount | number | 前十大股东合计持股数量（股） |
| top10_shareholding_amount_ratio | number | 前十大股东合计持股数量占总股本比例（%） |
| top20_shareholding_amount | number | 前二十大股东合计持股数量（股） |
| top20_shareholding_amount_ratio | number | 前二十大股东合计持股数量占总股本比例（%） |
| typeData | array | 股东类型分布数据 |

##### typeData 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| type | string | 股东类型名称 |
| count | number | 该类型股东数量 |
| shares | number | 该类型股东持股总数（股） |
| typeData | array | 股东类型分布数据 |

##### typeData 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| type | string | 股东类型名称 |
| count | number | 该类型股东数量 |
| shares | number | 该类型股东持股总数（股） |

#### 响应示例

```json
[
    {
        "success": true,
        "data": {
            "trendData": [
                {
                    "registerDate": "2024-03-29T00:00:00.000Z",
                    "companyName": "一品红",
                    "companyCode": "300723",
                    "total_shares": "*********.00",
                    "total_shareholders": 13159,
                    "institutional_shareholders": 1090,
                    "institutional_shares": "*********.00",
                    "institutional_shares_ratio": "65.51",
                    "individual_shareholders": 12069,
                    "individual_shares": "*********.00",
                    "individual_shares_ratio": "34.49",
                    "credit_shareholders": "1081",
                    "credit_shares": "17542222.00",
                    "credit_shares_ratio": "3.86",
                    "top10_shareholding_amount": "*********.00",
                    "top20_shareholding_amount": "*********.00",
                    "top10_shareholding_amount_ratio": "71.02",
                    "top20_shareholding_amount_ratio": "75.65",
                    "typeData": [
                    {
                      "type": "基本养老保险基金",
                      "count": 2,
                      "shares": 4336700
                    },
                    {
                      "type": "知名机构",
                      "count": 2,
                      "shares": 226000
                    },]
                }
            ]
        },
        "timestamp": "2025-06-16T02:33:09.188Z"
    }
]
```

#### 错误码

| HTTP状态码 | 错误码 | 说明 |
|------------|--------|------|
| 400 | MISSING_ORGANIZATION_ID | 缺少必需参数：organizationId |
| 400 | ORGANIZATION_ID_LENGTH_INVALID | organizationId长度必须在5-50个字符之间 |
| 400 | MISSING_START_DATE | 缺少必需参数：startDate |
| 500 | NO_DATA_FOUND | 未找到指定组织的数据，请检查organizationId是否正确 |
| 500 | INTERNAL_ERROR | 服务内部错误，请稍后重试 |

---

## 模块三：股东变动分析

### 1. 增持股东接口

#### 1.1 增持自然人股东接口

**接口描述:** 获取公司股东结构趋势（增持自然人股东明细）

**请求信息:**
- **URL:** `/increase-individual-shareholders-trend`
- **HTTP方法:** `POST`

##### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 组织ID，长度5-50个字符 |
| page | number | 是 | 页码，正整数 |
| pageSize | number | 是 | 每页条数，正整数 |
| order | string | 否 | 降序或升序排列(Desc or Asc) |
| order_base | string | 否 | 排序依据的字段名，具体字段名参考响应参数 |

##### 请求示例

```json
{
  "id": "1234567890",
  "page": 1,
  "pageSize": 10
}
```

##### 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |
| data | object | 数据内容 |

###### data 对象字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| increaseIndividuals | array | 增持自然人股东明细 |
| total | number | 符合条件的数据总条数 |

###### increaseIndividuals 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| name | string | 股东名称 |
| unified_account_number | string | 一码通 |
| current_numberofshares | number | 当前期持股数量 |
| increased_shares | number | 较上期增持股数 |
| increased_ratio_percent | number | 较上期增持比例（%） |
| current_shareholdingRatio | number | 当前期持股比例（%） |
| increased_date | string | 增持发生日期 |

##### 响应示例

```json
{
  "success": true,
  "timestamp": "2024-06-16T12:00:00.000Z",
  "data": {
    "increaseIndividuals": [
      {
        "name": "李四",
        "unified_account_number": "A654321",
        "current_numberofshares": 9000,
        "increased_shares": 1500,
        "increased_ratio_percent": 20.0,
        "current_shareholdingRatio": 2.0,
        "increased_date": "2024-06-01"
      }
    ],
    "total": 15
  }
}
```

##### 分页特殊说明

当分页查询已展示完所有内容，但仍继续请求下一页时，接口会返回如下结构，表示无更多数据：

```json
[
  {
    "success": true,
    "data": {
      "increaseIndividuals": [
        {
          "success": true
        }
      ]
    },
    "timestamp": "2025-06-16T08:59:42.466Z"
  }
]
```

其中 increaseIndividuals 数组仅包含一个 `{ "success": true }` 对象，表示数据已全部返回完毕。

#### 1.2 增持机构股东接口

**接口描述:** 获取公司股东结构趋势（增持机构股东明细）

**请求信息:**
- **URL:** `/increase-institution-shareholders-trend`
- **HTTP方法:** `POST`

##### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 组织ID，长度5-50个字符 |
| page | number | 是 | 页码，正整数 |
| pageSize | number | 是 | 每页条数，正整数 |
| order | string | 否 | 降序或升序排列(Desc or Asc) |
| order_base | string | 否 | 排序依据的字段名，具体字段名参考响应参数 |

##### 请求示例

```json
{
  "id": "1234567890",
  "page": 1,
  "pageSize": 10
}
```

##### 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |
| data | object | 数据内容 |

###### data 对象字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| increaseInstitutions | array | 增持机构股东明细 |
| total | number | 符合条件的数据总条数 |

###### increaseInstitutions 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| rank | string | 排名 |
| name | string | 股东名称 |
| unified_account_number | string | 一码通 |
| current_numberofshares | number | 当前期持股数量 |
| increased_shares | number | 较上期增持股数 |
| increased_ratio_percent | number | 较上期增持比例（%） |
| current_shareholdingRatio | number | 当前期持股比例（%） |
| increased_date | string | 增持发生日期 |

##### 响应示例

```json
{
  "success": true,
  "timestamp": "2024-06-16T12:00:00.000Z",
  "data": {
    "increaseInstitutions": [
      {
        "name": "某基金公司",
        "unified_account_number": "B123789",
        "current_numberofshares": 420000,
        "increased_shares": 20000,
        "increased_ratio_percent": 5.0,
        "current_shareholdingRatio": 8.5,
        "increased_date": "2024-06-01"
      }
    ],
    "total": 15
  }
}
```

##### 分页特殊说明

当分页查询已展示完所有内容，但仍继续请求下一页时，接口会返回如下结构，表示无更多数据：

```json
[
  {
    "success": true,
    "data": {
      "increaseInstitutions": [
        {
          "success": true
        }
      ]
    },
    "timestamp": "2025-06-16T08:59:42.466Z"
  }
]
```

其中 increaseInstitutions 数组仅包含一个 `{ "success": true }` 对象，表示数据已全部返回完毕。


### 2. 减持股东接口

#### 2.1 减持自然人股东接口

**接口描述:** 获取公司股东结构趋势（减持自然人股东明细）

**请求信息:**
- **URL:** `/decrease-individual-shareholders-trend`
- **HTTP方法:** `POST`

##### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 组织ID，长度5-50个字符 |
| page | number | 是 | 页码，正整数 |
| pageSize | number | 是 | 每页条数，正整数 |
| order | string | 否 | 降序或升序排列(Desc or Asc) |
| order_base | string | 否 | 排序依据的字段名，具体字段名参考响应参数 |

##### 请求示例

```json
{
  "id": "1234567890",
  "page": 1,
  "pageSize": 10
}
```

##### 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |
| data | object | 数据内容 |

###### data 对象字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| decreaseIndividuals | array | 减持自然人股东明细 |
| total | number | 符合条件的数据总条数 |

###### decreaseIndividuals 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| rank | string | 排名 |
| name | string | 股东名称 |
| unified_account_number | string | 一码通 |
| current_numberofshares | number | 当前期持股数量 |
| decreased_shares | number | 较上期减持股数 |
| decreased_ratio_percent | number | 较上期减持比例（%） |
| decreased_date | string | 减持发生日期 |

##### 响应示例

```json
{
  "success": true,
  "timestamp": "2024-06-16T12:00:00.000Z",
  "data": {
    "decreaseIndividuals": [
      {
        "name": "李四",
        "unifiedAccountNumber": "A654321",
        "current_numberOfShares": 8000,
        "decreased_shares": 1500,
        "decreased_ratio_percent": 15.8,
        "decreased_date": "2024-06-01"
      }
    ],
    "total": 15
  }
}
```

##### 分页特殊说明

当分页查询已展示完所有内容，但仍继续请求下一页时，接口会返回如下结构，表示无更多数据：

```json
[
  {
    "success": true,
    "data": {
      "decreaseIndividuals": [
        {
          "success": true
        }
      ]
    },
    "timestamp": "2025-06-16T08:59:42.466Z"
  }
]
```

其中 decreaseIndividuals 数组仅包含一个 `{ "success": true }` 对象，表示数据已全部返回完毕。

#### 2.2 减持机构股东接口

**接口描述:** 获取公司股东结构趋势（减持机构股东明细）

**请求信息:**
- **URL:** `/decrease-institution-shareholders-trend`
- **HTTP方法:** `POST`

##### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 组织ID，长度5-50个字符 |
| page | number | 是 | 页码，正整数 |
| pageSize | number | 是 | 每页条数，正整数 |
| order | string | 否 | 降序或升序排列(Desc or Asc) |
| order_base | string | 否 | 排序依据的字段名，具体字段名参考响应参数 |

##### 请求示例

```json
{
  "id": "1234567890",
  "page": 1,
  "pageSize": 10
}
```

##### 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |
| data | object | 数据内容 |

###### data 对象字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| decreaseInstitutions | array | 减持机构股东明细 |
| total | number | 符合条件的数据总条数 |

###### decreaseInstitutions 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| rank | string | 排名 |
| name | string | 股东名称 |
| unified_account_number | string | 一码通 |
| current_numberofshares | number | 当前期持股数量 |
| decreased_shares | number | 较上期减持股数 |
| decreased_ratio_percent | number | 较上期减持比例（%） |
| decreased_date | string | 减持发生日期 |

##### 响应示例

```json
{
  "success": true,
  "timestamp": "2024-06-16T12:00:00.000Z",
  "data": {
    "decreaseInstitutions": [
      {
        "name": "某基金公司",
        "unified_account_number": "B123789",
        "current_numberofshares": 400000,
        "decreased_shares": 20000,
        "decreased_ratio_percent": 4.8,
        "decreased_date": "2024-06-01"
      }
    ],
    "total": 15
  }
}
```

##### 分页特殊说明

当分页查询已展示完所有内容，但仍继续请求下一页时，接口会返回如下结构，表示无更多数据：

```json
[
  {
    "success": true,
    "data": {
      "decreaseInstitutions": [
        {
          "success": true
        }
      ]
    },
    "timestamp": "2025-06-16T08:59:42.466Z"
  }
]
```

其中 decreaseInstitutions 数组仅包含一个 `{ "success": true }` 对象，表示数据已全部返回完毕。

### 3. 新进股东接口

#### 3.1 新进自然人股东接口

**接口描述:** 获取公司股东结构趋势（新进自然人股东明细）

**请求信息:**
- **URL:** `/new-individual-shareholders-trend`
- **HTTP方法:** `POST`

##### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 组织ID，长度5-50个字符 |
| page | number | 是 | 页码，正整数 |
| pageSize | number | 是 | 每页条数，正整数 |
| order | string | 否 | 降序或升序排列(Desc or Asc) |
| order_base | string | 否 | 排序依据的字段名，具体字段名参考响应参数 |

##### 请求示例

```json
{
  "id": "1234567890",
  "page": 1,
  "pageSize": 10
}
```

##### 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |
| data | object | 数据内容 |

###### data 对象字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| newIndividuals | array | 新进自然人股东明细 |
| total | number | 符合条件的数据总条数 |

###### newIndividuals 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| name | string | 股东名称 |
| unified_account_number | string | 一码通 |
| number_of_shares | number | 当前期持股数量 |
| shareholder_ratio | number | 当前期持股比例（%） |
| register_date | string | 新进发生日期 |

##### 响应示例

```json
{
  "success": true,
  "timestamp": "2024-06-16T12:00:00.000Z",
  "data": {
    "newIndividuals": [
      {
        "name": "王五",
        "unified_account_number": "A987654",
        "number_of_shares": 5000,
        "shareholder_ratio": 1.2,
        "register_date": "2024-06-01"
      }
    ],
    "total": 15
  }
}
```

##### 分页特殊说明

当分页查询已展示完所有内容，但仍继续请求下一页时，接口会返回如下结构，表示无更多数据：

```json
[
  {
    "success": true,
    "data": {
      "newIndividuals": [
        {
          "success": true
        }
      ]
    },
    "timestamp": "2025-06-16T08:59:42.466Z"
  }
]
```

其中 newIndividuals 数组仅包含一个 `{ "success": true }` 对象，表示数据已全部返回完毕。

#### 3.2 新进机构股东接口

**接口描述:** 获取公司股东结构趋势（新进机构股东明细）

**请求信息:**
- **URL:** `/new-institution-shareholders-trend`
- **HTTP方法:** `POST`

##### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 组织ID，长度5-50个字符 |
| page | number | 是 | 页码，正整数 |
| pageSize | number | 是 | 每页条数，正整数 |
| order | string | 否 | 降序或升序排列(Desc or Asc) |
| order_base | string | 否 | 排序依据的字段名，具体字段名参考响应参数 |

##### 请求示例

```json
{
  "id": "1234567890",
  "page": 1,
  "pageSize": 10
}
```

##### 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |
| data | object | 数据内容 |

###### data 对象字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| newInstitutions | array | 新进机构股东明细 |
| total | number | 符合条件的数据总条数 |

###### newInstitutions 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| name | string | 股东名称 |
| unified_account_number | string | 一码通 |
| number_of_shares | number | 当前期持股数量 |
| shareholder_ratio | number | 当前期持股比例（%） |
| register_date | string | 新进发生日期 |

##### 响应示例

```json
{
  "success": true,
  "timestamp": "2024-06-16T12:00:00.000Z",
  "data": {
    "newInstitutions": [
      {
        "name": "某保险公司",
        "unified_account_number": "B456123",
        "number_of_shares": 300000,
        "shareholder_ratio": 6.5,
        "register_date": "2024-06-01"
      }
    ],
    "total": 15
  }
}
```

##### 分页特殊说明

当分页查询已展示完所有内容，但仍继续请求下一页时，接口会返回如下结构，表示无更多数据：

```json
[
  {
    "success": true,
    "data": {
      "newInstitutions": [
        {
          "success": true
        }
      ]
    },
    "timestamp": "2025-06-16T08:59:42.466Z"
  }
]
```

其中 newInstitutions 数组仅包含一个 `{ "success": true }` 对象，表示数据已全部返回完毕。



### 4. 退出股东接口

#### 4.1 退出自然人股东接口

**接口描述:** 获取公司股东结构趋势（退出自然人股东明细）

**请求信息:**
- **URL:** `/exit-individual-shareholders-trend`
- **HTTP方法:** `POST`

##### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 组织ID，长度5-50个字符 |
| page | number | 是 | 页码，正整数 |
| pageSize | number | 是 | 每页条数，正整数 |
| order | string | 否 | 降序或升序排列(Desc or Asc) |
| order_base | string | 否 | 排序依据的字段名，具体字段名参考响应参数 |

##### 请求示例

```json
{
  "id": "1234567890",
  "page": 1,
  "pageSize": 10
}
```

##### 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |
| data | object | 数据内容 |

###### data 对象字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| exitIndividuals | array | 退出自然人股东明细 |
| total | number | 符合条件的数据总条数 |

###### exitIndividuals 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| rank | number | 排名 |  
| unified_account_number | string | 一码通 |
| exit_date | string | 退出发生日期 |
| prev_numberOfShares | number | 上期持股数量 |
| prev_shareholdingRatio | number | 上期持股比例（%） |

##### 响应示例

```json
{
  "success": true,
  "timestamp": "2024-06-16T12:00:00.000Z",
  "data": {
    "exitIndividuals": [
      {
        "name": "赵六",
        "unifiedAccountNumber": "A112233",
        "exit_date": "2024-06-01",
        "prev_numberOfShares": 3000,
        "prev_shareholdingRatio": 0.8
      }
    ],
      "total": 15
  }
}
```

##### 分页特殊说明

当分页查询已展示完所有内容，但仍继续请求下一页时，接口会返回如下结构，表示无更多数据：

```json
[
  {
    "success": true,
    "data": {
      "exitIndividuals": [
        {
          "success": true
        }
      ]
    },
    "timestamp": "2025-06-16T08:59:42.466Z"
  }
]
```

其中 exitIndividuals 数组仅包含一个 `{ "success": true }` 对象，表示数据已全部返回完毕。

#### 4.2 退出机构股东接口

**接口描述:** 获取公司股东结构趋势（退出机构股东明细）

**请求信息:**
- **URL:** `/exit-institution-shareholders-trend`
- **HTTP方法:** `POST`

##### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 组织ID，长度5-50个字符 |
| page | number | 是 | 页码，正整数 |
| pageSize | number | 是 | 每页条数，正整数 |

##### 请求示例

```json
{
  "id": "1234567890",
  "page": 1,
  "pageSize": 10
}
```

##### 响应参数

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |
| data | object | 数据内容 |

###### data 对象字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| exitInstitutions | array | 退出机构股东明细 |

###### exitInstitutions 数组元素字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| rank | number | 排名 |
| unified_account_number | string | 一码通 |
| exit_date | string | 退出发生日期 |
| prev_numberOfShares | number | 上期持股数量 |
| prev_shareholdingRatio | number | 上期持股比例（%） |

##### 响应示例

```json
{
  "success": true,
  "timestamp": "2024-06-16T12:00:00.000Z",
  "data": {
    "exitInstitutions": [
      {
        "name": "某信托公司",
        "unifiedAccountNumber": "B998877",
        "exit_date": "2024-06-01",
        "prev_numberOfShares": 100000,
        "prev_shareholdingRatio": 2.5
      }
    ],
      "total": 15
  }
}
```

##### 分页特殊说明

当分页查询已展示完所有内容，但仍继续请求下一页时，接口会返回如下结构，表示无更多数据：

```json
[
  {
    "success": true,
    "data": {
      "exitInstitutions": [
        {
          "success": true
        }
      ]
    },
    "timestamp": "2025-06-16T08:59:42.466Z"
  }
]
```

其中 exitInstitutions 数组仅包含一个 `{ "success": true }` 对象，表示数据已全部返回完毕。

## 通用错误码

所有股东变动分析接口都使用相同的错误处理机制：

| HTTP状态码 | 错误码 | 说明 |
|------------|--------|------|
| 400 | MISSING_ORGANIZATION_ID | 缺少必需参数：organizationId |
| 400 | INVALID_ORGANIZATION_ID | 无效的organizationId：参数必须是非空字符串 |
| 400 | ORGANIZATION_ID_LENGTH_INVALID | 无效的organizationId长度：参数长度必须在5-50个字符之间 |
| 500 | NO_TREND_DATA_FOUND | 未找到指定组织的数据，请检查organizationId是否正确 |
| 500 | INTERNAL_ERROR | 服务内部错误，请稍后重试 |

### 错误响应示例

```json
{
  "error": {
    "code": "MISSING_ORGANIZATION_ID",
    "message": "缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。",
    "timestamp": "2024-06-16T12:00:00.000Z",
    "type": "VALIDATION_ERROR"
  }
}
```

## 功能说明

这八个接口组合提供了完整的股东变动分析功能：

- **增持分析**: 识别对公司信心增强的股东（分别支持自然人和机构股东）
- **减持分析**: 监控股东减持行为和趋势（分别支持自然人和机构股东）
- **新进分析**: 跟踪新投资者的进入情况（分别支持自然人和机构股东）
- **退出分析**: 分析股东退出的原因和影响（分别支持自然人和机构股东）

---

**文档作者:** hayden
**最后更新:** 2025-06-27 15:49:08