"use client";

import React from 'react';
import { Progress } from "@ui/components/progress";

interface ImportProgressProps {
  currentFileIndex: number;
  totalFiles: number;
  progress: number;
  status: "validating" | "importing";
}

/**
 * 股东名册导入进度指示器组件
 * 显示当前处理的文件索引和总体进度
 * 
 * @param {Object} props - 组件属性
 * @param {number} props.currentFileIndex - 当前处理的文件索引
 * @param {number} props.totalFiles - 文件总数
 * @param {number} props.progress - 当前文件处理进度
 * @param {string} props.status - 当前状态
 * 
 * @update 2025-06-04 16:15:02 从ShareholderRegistryImportProgress组件移植
 * @version 2.1.0 (2025-05-26) - 更新进度计算逻辑，显示整体进度而不仅是当前文件进度
 * @version 2.0.0 (2025-05-19) - 移除移动端适配逻辑，只保留桌面端布局
 * <AUTHOR> 2025-05-16
 */
export function ImportProgress({
  currentFileIndex,
  totalFiles,
  progress,
  status,
}: ImportProgressProps) {
  // 计算整体进度：已完成的文件贡献100%，当前文件贡献按实际进度，未处理文件贡献0%
  const calculateOverallProgress = (): number => {
    if (totalFiles === 0) {
      return 0;
    }
    
    // 每个文件的权重
    const fileWeight = 100 / totalFiles;
    
    // 已完成的文件进度
    const completedProgress = currentFileIndex * fileWeight;
    
    // 当前文件的进度贡献
    const currentFileProgress = status === "validating" ? 0 : (progress * fileWeight / 100);
    
    // 总体进度
    return Math.round(completedProgress + currentFileProgress);
  };

  // 计算整体进度
  const overallProgress = calculateOverallProgress();

  return (
    <div className="mt-3">
      <div className="mb-1 flex justify-between">
        <span className="text-muted-foreground text-sm">
          {currentFileIndex + 1}/{totalFiles}
        </span>
        <span className="text-muted-foreground text-sm">
          {status === "validating" ? "校验中" : "导入中"} ({overallProgress}%)
        </span>
      </div>
      <Progress 
        value={overallProgress} 
        className="h-2 w-full"
      />
    </div>
  );
} 