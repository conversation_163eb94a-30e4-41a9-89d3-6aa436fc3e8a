import { Card } from "@ui/components/card";
import { Avatar, AvatarImage } from "@ui/components/avatar";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@ui/components/dropdown-menu";
import { Button } from "@ui/components/button";
import { MoreVertical } from "lucide-react";

interface Investor {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  type: string;
  status: string;
  avatar: string; // 头像
}

export default function ListDetail({ investor, onDelete }: { investor: Investor; onDelete: () => void }) {
  if (!investor) { return null; }
  return (
    <Card className="rounded-xl shadow p-0 border bg-white dark:bg-zinc-900 border-gray-200 dark:border-zinc-700">
      <div className="flex items-center justify-between px-6 pt-6">
        <span className="inline-flex items-center gap-2 bg-gray-100 dark:bg-zinc-800 rounded-full px-3 py-1 font-medium text-sm text-gray-700 dark:text-gray-200">
          <span className={investor.type === "Customer"
            ? "w-3 h-3 rounded-full bg-purple-400 dark:bg-purple-500"
            : "w-3 h-3 rounded-full bg-sky-400 dark:bg-sky-500"
          } />
          {investor.type}
        </span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon"><MoreVertical /></Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={onDelete} className="text-destructive">删除</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="flex flex-col items-center px-6 pt-2 pb-4">
        <Avatar className="w-16 h-16 text-2xl mb-2 border border-gray-200 dark:border-zinc-700 bg-white dark:bg-zinc-900 rounded-full">
          <AvatarImage src={investor.avatar} alt={investor.firstName} className="rounded-full" />
        </Avatar>
        <div className="font-bold text-lg mt-2 text-gray-900 dark:text-gray-100">{investor.firstName} {investor.lastName}</div>
        <div className="text-muted-foreground text-sm">{investor.email}</div>
      </div>
      <div className="border-t border-gray-200 dark:border-zinc-700 px-6 py-4">
        <div className="font-semibold text-sm mb-2 text-gray-900 dark:text-gray-100">Details</div>
        <div className="flex items-center justify-between text-sm mb-2">
          <span className="text-gray-700 dark:text-gray-200">Status</span>
          <span className="inline-flex items-center gap-2 bg-gray-100 dark:bg-zinc-800 rounded-full px-3 py-1 font-medium text-sm text-gray-700 dark:text-gray-200">
            <span className={
              investor.status === "Active"
                ? "w-3 h-3 rounded-full bg-green-500 dark:bg-green-400"
                : investor.status === "New"
                  ? "w-3 h-3 rounded-full bg-blue-500 dark:bg-blue-400"
                  : "w-3 h-3 rounded-full bg-amber-600 dark:bg-amber-400"
            } />
            {investor.status}
          </span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-700 dark:text-gray-200">Signed up</span>
          <span className="text-gray-900 dark:text-gray-100">{new Date(investor.createdAt).toLocaleString("zh-CN", { year: "numeric", month: "long", day: "numeric", hour: "2-digit", minute: "2-digit" })}</span>
        </div>
      </div>
    </Card>
  );
}
