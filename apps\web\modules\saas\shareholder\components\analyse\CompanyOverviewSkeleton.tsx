/**
 * 股东名册分析 - 公司概览骨架屏组件
 * @file CompanyOverviewSkeleton.tsx
 * @description 用于展示公司概览数据加载时的骨架屏，与 CompanyOverview 组件布局完全一致
 * <AUTHOR>
 * @created 2025-06-16 13:52:37
 */

"use client";

import { Skeleton } from "@ui/components/skeleton";

/**
 * 指标卡片骨架屏组件
 * @returns 指标卡片骨架屏UI组件
 * <AUTHOR>
 * @created 2025-06-16 13:52:37
 * @modified 2025-06-30 10:21:12 - 添加border边框样式，与MetricCard组件保持一致，解决暗色主题下边界不可见问题 - hayden
 */
function MetricCardSkeleton(): JSX.Element {
	return (
		<div className="rounded-lg bg-card border border-border p-3 shadow-sm">
			{/* 标题骨架 */}
			<Skeleton className="h-3 w-20 mb-2" />
			{/* 内容区域 */}
			<div className="mt-1 flex items-end justify-between">
				{/* 主要数值骨架 */}
				<Skeleton className="h-6 w-16" />
				{/* 变化值骨架 */}
				<Skeleton className="h-4 w-12" />
			</div>
		</div>
	);
}

/**
 * 图表骨架屏组件
 * @param title 图表标题
 * @returns 图表骨架屏UI组件
 * <AUTHOR>
 * @created 2025-06-16 13:52:37
 */
function ChartSkeleton({ title }: { title: string }): JSX.Element {
	return (
		<div className="h-full">
			{/* 图表标题 */}
			<h3 className="mb-2 text-sm font-medium">{title}</h3>
			{/* 图表内容骨架 */}
			<div className="h-[250px] flex items-center justify-center">
				<div className="relative w-full h-full">
					{/* 圆形图表骨架 */}
					<Skeleton className="absolute inset-0 rounded-full w-40 h-40 mx-auto my-auto" />
					{/* 中心圆形骨架 */}
					<Skeleton className="absolute inset-0 rounded-full w-24 h-24 mx-auto my-auto" />
				</div>
			</div>
		</div>
	);
}

/**
 * 公司概览骨架屏组件
 * @returns 公司概览骨架屏UI组件
 * <AUTHOR>
 * @created 2025-06-16 13:52:37
 */
export function CompanyOverviewSkeleton(): JSX.Element {
	return (
		<div className="w-full">
			{/* 头部信息骨架 - 数据分析风格渐变背景 */}
			<div className="w-full bg-gradient-to-br  from-slate-900 via-gray-800 to-slate-800 p-6 rounded-lg text-white shadow-lg relative overflow-hidden">
				{/* 装饰性背景元素 */}
				<div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent" />
				<div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16" />
				<div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12" />

				{/* 内容区域 */}
				<div className="relative z-10">
					<div className="flex flex-col md:flex-row md:items-start md:justify-between">
						<div>
							{/* 公司名称骨架 */}
							<Skeleton className="h-8 w-64 mb-2 bg-white/20" />
							<div className="mt-1 flex items-center gap-2">
								{/* 股票代码骨架 */}
								<Skeleton className="h-6 w-16 bg-white/20 rounded-md" />
								{/* 分析范围骨架 */}
								<Skeleton className="h-4 w-48 bg-white/20" />
							</div>
						</div>
					</div>

					{/* 生成时间区域骨架 */}
					<div className="flex justify-end mt-[-24px] md:mt-[-20px]">
						<div className="flex items-center gap-1 text-sm">
							{/* 生成时间骨架 */}
							<Skeleton className="h-4 w-32 bg-white/20" />
							{/* 刷新按钮骨架 */}
							<Skeleton className="size-4 bg-white/20 rounded" />
						</div>
					</div>
				</div>
			</div>

			<div>
				{/* 第一行指标骨架 */}
				<div className="mt-6 mb-6 grid grid-cols-2 gap-2 md:grid-cols-3 lg:grid-cols-6">
					{Array.from({ length: 6 }).map((_, index) => (
						<MetricCardSkeleton key={`metric-1-${index}`} />
					))}
				</div>

				{/* 第二行指标骨架 */}
				<div className="mb-6 grid grid-cols-2 gap-2 md:grid-cols-3 lg:grid-cols-6">
					{Array.from({ length: 6 }).map((_, index) => (
						<MetricCardSkeleton key={`metric-2-${index}`} />
					))}
				</div>

				{/* 图表部分骨架 */}
				<div className="grid grid-cols-1 gap-6 md:grid-cols-3">
					<div className="rounded-lg border p-4">
						<ChartSkeleton title="持股比例分布" />
					</div>
					<div className="rounded-lg border p-4">
						<ChartSkeleton title="机构户数分布" />
					</div>
					<div className="rounded-lg border p-4">
						<ChartSkeleton title="机构持股分布" />
					</div>
				</div>
			</div>
		</div>
	);
}
