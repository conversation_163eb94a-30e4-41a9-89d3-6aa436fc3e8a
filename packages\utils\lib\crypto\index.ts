/**
 * 股东名册API加密工具
 * 提供加密、解密和签名验证功能
 */

// 导出所有常量
export * from './constants';

// 导出加密函数
export * from './encrypt';

// 导出解密函数
export * from './decrypt';

// 导出签名函数
export * from './sign';

// 导出请求包装函数
export const createEncryptedRequest = <T>(data: T): { content: string; sign: string } => {
  const { encryptRequestDataSync } = require('./encrypt');
  const { createSignedRequest } = require('./sign');
  
  // 先加密数据
  const encryptedContent = encryptRequestDataSync(data);
  // 再生成签名
  return createSignedRequest(encryptedContent);
}; 

// 导出异步请求包装函数
export const createEncryptedRequestAsync = async <T>(data: T): Promise<{ content: string; sign: string }> => {
  const { encryptRequestData } = require('./encrypt');
  const { createSignedRequest } = require('./sign');
  
  // 先加密数据（使用异步方法获取标准时间）
  const encryptedContent = await encryptRequestData(data);
  // 再生成签名
  return createSignedRequest(encryptedContent);
}; 