# 股东名册01和05字段对应关系参照表

## 1. 概述

本文档提供了股东名册01和05两种类型文件的字段对应关系详细参照表，用于支持合并逻辑的实施。文档包含了两种名册的原始字段、数据库字段映射以及合并处理时的注意事项。

### 1.1 名册类型说明

- **01名册**：文件名格式为`DQMC01_公司代码_日期.DBF`，包含普通股东信息
- **05名册**：文件名格式为`DQMC05_公司代码_日期.DBF`，包含信用账户股东信息

## 2. 字段对应关系完整参照表

下表详细列出了01名册和05名册的所有字段及其对应的数据库字段映射关系：

| 序号 | 数据库字段名 | 数据类型 | 01名册字段 | 05名册字段 | 字段说明 | 合并处理策略 |
|------|--------------|----------|------------|------------|----------|--------------|
| 1 | unifiedAccountNumber | String | YMTH | YMTH | 一码通账户号码，关键的共同标识 | 保留已有值 |
| 2 | securitiesAccountName | String | ZQZHMC | XYZHMC | 证券账户名称，01名册中可能带有#符号，XYZQZHMC
证券账户名称，01名册中可能带有#符号,过滤带# | 保留已有值 |
| 3 | shareholderId | String | ZJDM | XYZQZHZJDM | 证件号码，作为主键标识 | 保留已有值 |
| 4 | shareholderCategory | String | CYRLBMS | CYRLBMS | 持有人类别，如"境内自然人(03)" | 保留已有值 |
| 5 | reportDate | DateTime | 从文件名解析 | 从文件名解析 | 报告日期 | 保留已有值 |
| 6 | contactAddress | String | TXDZ | TXDZ | 通讯地址 | 保留已有值 |
| 7 | contactNumber | String | DHHM | 无 | 电话号码，05名册中无此字段 | 保留01名册值 |
| 8 | zipCode | String | YZBM | 无 | 邮政编码，05名册中无此字段 | 保留01名册值 |
| 9 | marginCollateralAccountNumber | String | 无 | HZZQZH | 汇总账户号码，05名册特有 | 保留05名册值 |
| 10 | marginCollateralAccountName | String | 无 | HZZHMC | 汇总账户名称，05名册特有 | 保留05名册值 |
| 11 | relatedPartyIndicator | String | GLGXBS | 无 | 关联关系确认标识，01名册特有 | 保留01名册值 |
| 12 | cashAccount | String | PTZQZH | 无 | 普通证券账户，01名册特有 | 保留01名册值 |
| 13 | sharesInCashAccount | Decimal | PTZHCGSL | 无 | 普通账户持股数量，01名册特有 | 保留01名册值 |
| 14 | marginAccount | String | XYZQZH | XYZQZH | 信用证券账户 | 保留已有值 |
| 15 | sharesInMarginAccount | Decimal | XYZHCGSL | CGSL | 信用账户持股数量，注意05名册中CGSL表示信用账户持股 | 保留已有值 |
| 16 | numberOfShares | Decimal | CGSL | 无 | 总持股数量，01名册特有 | 保留01名册值 |
| 17 | frozenShares | Decimal | DJGS | DJGS | 冻结股数 | 保留已有值 |
| 18 | lockedUpShares | Decimal | XSGSL | 无 | 限售股数量，01名册特有 | 保留01名册值 |
| 19 | shareholdingRatio | Decimal | CGBL | 无 | 持股比例(%)，01名册特有 | 保留01名册值 |
| 20 | natureOfShares | String | 无 | GFXZ | 股份性质，05名册特有，"00"表示无限售条件流通股 | 保留05名册值 |
| 21 | remarks | String | BZ | 无 | 备注，01名册特有 | 保留01名册值 |
| 22 | clientCategory | String | KHLB | 无 | 客户类别，01名册特有 | 保留01名册值 |
| 23 | companyCode | String | XH=0/-1/-2时YMTH | XH=0时YMTH | 公司代码，从特殊记录中提取 | 保留已有值 |

## 3. 名册字段详细说明

### 3.1 01名册字段详情

| 字段名 | 名称 | 类型 | 长度 | 说明 |
|--------|------|------|------|------|
| PMLX | 排名类型 | C | 1 | Q：全体股份排名 |
| XH | 序号 | N | 10,0 | 特殊值0/-1/-2有特殊含义 |
| YMTH | 一码通账户号码 | C | 20 | 序号为0/-1/-2时表示证券代码 |
| ZQZHMC | 证券账户名称 | C | 120 | 序号为0时包含总股数、总户数信息 |
| ZJDM | 证件号码 | C | 40 | 股东唯一标识 |
| CYRLBMS | 持有人类别 | C | 30 | 如"境内自然人(03)" |
| CGSL | 持股数量 | N | 17,2 | 总持股数量 |
| XSGSL | 限售股数量 | N | 17,2 | |
| CGBL | 持股比例 | N | 6,2 | |
| DJGS | 冻结股数 | N | 17,2 | 序号为0时表示总户数 |
| PTZQZH | 普通证券账户 | C | 20 | |
| PTZHCGSL | 普通账户持股数量 | N | 17,2 | |
| XYZQZH | 信用证券账户 | C | 20 | |
| XYZHCGSL | 信用账户持股数量 | N | 17,2 | |
| TXDZ | 通讯地址 | C | 120 | 序号为0/-1/-2时表示证券简称 |
| DHHM | 电话号码 | C | 20 | 序号为0/-1/-2时表示证券简称 |
| YZBM | 邮政编码 | C | 8 | |
| GLGXBS | 关联关系确认标识 | C | 1 | |
| KHLB | 客户类别 | C | 1 | |
| BZ | 备注 | C | 20 | |

### 3.2 05名册字段详情

| 字段名 | 名称 | 类型 | 长度 | 说明 |
|--------|------|------|------|------|
| YMTH | 一码通账户号码 | C | 20 | 序号为0时表示证券代码 |
| XYZQZHMC | 信用证券账户名称 | C | 120 | |
| XYZQZHZJDM | 信用证券账户证件号码 | C | 40 | 股东唯一标识 |
| CYRLBMS | 持有人类别 | C | 30 | 如"境内自然人(03)" |
| XYZQZH | 信用证券账户 | C | 20 | |
| CGSL | 持股数 | N | 17,2 | 信用账户持股数量 |
| DJGS | 冻结(质押/回购)数量 | N | 17,2 | |
| TXDZ | 通讯地址 | C | 120 | 序号为0时表示证券简称 |
| HZZQZH | 汇总证券账户 | C | 20 | |
| HZZHMC | 汇总证券账户名称 | C | 120 | |
| GFXZ | 股份性质 | C | 2 | "00"表示无限售条件流通股 |

## 4. 合并处理关键点

### 4.1 字段合并基本原则

1. 对于两种名册共有的字段，保留数据库中已有的值
2. 对于仅在一种名册中存在的字段，直接使用该名册提供的值
3. 以证件号码(shareholderId)作为唯一标识，用于匹配同一股东的记录

### 4.2 特殊字段处理说明

1. **信用账户持股数量(sharesInMarginAccount)**：
   - 01名册中对应字段为XYZHCGSL
   - 05名册中对应字段为CGSL
   - 注意05名册中的CGSL表示信用账户持股数量，而非总持股数量

2. **总持股数量(numberOfShares)**：
   - 仅01名册有此信息(CGSL)
   - 05名册中不包含总持股数量

3. **特殊记录处理**：
   - 序号(XH)为0、-1、-2的记录包含公司信息，不是股东记录
   - 这些记录应单独处理，用于提取公司基本信息

## 5. 实施建议

1. 在合并处理时，应先检查证件号码是否存在，再进行字段合并
2. 对于共有字段，应检查数据库中是否已有值，如有则保留原值
3. 对于特有字段，直接填入对应名册的值
4. 合并处理应在事务中进行，确保数据一致性
5. 应记录合并处理的日志，便于问题排查 