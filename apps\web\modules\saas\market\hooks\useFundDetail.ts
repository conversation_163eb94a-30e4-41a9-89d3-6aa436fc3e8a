import { useQuery } from "@tanstack/react-query";
import { fetchFundDetail } from "../lib/html_dialog";
import type { FundCard } from "../lib/TestData";

export interface UseFundDetailOptions {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
}

export interface UseFundDetailReturn {
  data: { html?: string; markdown?: string; error?: string } | undefined;
  isLoading: boolean;
  isFetching: boolean;
  error: Error | null;
  isError: boolean;
  isSuccess: boolean;
  refetch: () => Promise<any>;
  isRefetching: boolean;
}

/**
 * 获取基金详情HTML的hook，5分钟内点击刷新直接用缓存，5分钟后才请求接口
 * @param card 当前选中的基金卡片
 * @param options 配置项
 */
export function useFundDetail(
  card: FundCard | null,
  options: UseFundDetailOptions = {}
): UseFundDetailReturn {
  const {
    enabled = true,
    staleTime = 5 * 60 * 1000,
    gcTime = 10 * 60 * 1000,
  } = options;

  const query = useQuery<{ html?: string; markdown?: string; error?: string }, Error>({
    queryKey: ["fundDetail", card?.code],
    queryFn: () => (card ? fetchFundDetail(card) : Promise.resolve({ html: "" })),
    enabled: enabled && !!card,
    refetchOnWindowFocus: false,
    staleTime,
    gcTime,
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    refetch: query.refetch,
    isRefetching: query.isRefetching,
  };
}
