# 股东类型匹配规则函数实施方案

## 版本信息
| 版本 | 日期       | 作者   | 修订内容描述                                                                 |
|------|------------|--------|------------------------------------------------------------------------------|
| V.4  | 2025-06-26 | hayden | 修正isRuleGroupMatch函数，基于API返回的matchField进行匹配，避免硬编码逻辑    |
| V.3  | 2025-06-26 | hayden | 更新前端缓存数据结构，使用分组后的规则数据，减少缓存数据量                   |
| V.2  | 2025-06-26 | hayden | 新增matchField字段支持，更新匹配逻辑以支持三种匹配方式                       |
| V.1  | 2025-06-25 | hayden | 初版：股东类型匹配规则函数设计和实施方案                                     |

## 1. 概述

### 1.1 背景
为了在股东名册上传前自动为股东数据添加股东类型字段，需要在 `apps/web/modules/saas/shareholder/lib` 目录下创建一个股东类型匹配规则函数，该函数将根据股东分类规则数据库对股东进行自动分类。

### 1.2 核心目标
1. 创建股东类型匹配规则函数，支持多种名册类型的字段兼容
2. 实现加密规则数据的本地缓存机制
3. 提供规则更新时间检查和缓存更新功能
4. 在 ShareholderRegistryImport 组件上传前触发股东类型匹配

### 1.3 技术架构
- **缓存策略**：本地存储加密的规则数据，仅缓存时间为明文
- **匹配逻辑**：基于优先级的规则匹配，支持关键字和CYRLBMS代码匹配
- **字段兼容**：支持不同名册类型的股东名称和持有人类别字段
- **触发时机**：在 ShareholderRegistryImport 组件调用上传接口前执行

## 2. 项目结构分析

### 2.1 相关文件位置
```
apps/web/modules/saas/shareholder/lib/
├── shareholder-classifier.ts        # 新增：股东类型匹配规则函数（已创建）
├── classification-api.ts            # 新增：股东分类规则API客户端（已创建）
├── types.ts                         # 需修改：添加股东分类相关类型定义
├── config.ts                        # 现有：名册类型和配置定义
├── api.ts                          # 现有：API调用函数
├── registry-api.ts                 # 现有：股东名册API客户端
└── utils.ts                        # 现有：工具函数

apps/web/modules/saas/shareholder/components/registry-import/
└── ShareholderRegistryImport.tsx    # 需修改：在上传前调用股东类型匹配

packages/api/src/routes/shareholder-registry/
├── classification-rules.ts         # 后端：股东分类规则API（参考文档）
├── classification-update-time.ts   # 后端：更新时间检查API（参考文档）
└── router.ts                       # 后端：路由配置（参考文档）
```

### 2.2 股东数据字段映射
根据文档分析，不同名册类型的关键字段映射：

| 名册类型 | 股东名称字段 | 持有人类别字段 | 说明 |
|----------|-------------|---------------|------|
| 01       | ZQZHMC      | CYRLBMS       | 深市01名册 |
| 05       | XYZHMC      | CYRLBMS       | 深市05名册 |
| t1       | CYRMC       | CYRLB         | 沪市t1名册 |
| t2       | CYRMC       | CYRLB         | 沪市t2名册 |
| t3       | CYRMC       | CYRLB         | 沪市t3名册 |

## 3. 核心函数设计

### 3.1 股东类型匹配规则函数 (shareholder-classifier.ts)

#### 3.1.1 主要功能模块
```typescript
// 1. 缓存管理模块
interface ClassificationCache {
  rules: string;           // 加密的规则数据
  lastUpdatedAt: string;   // 最后更新时间（明文）
  cachedAt: number;        // 缓存时间戳
}

// 2. 前端缓存的分组规则数据类型
interface ShareholderClassificationRuleGroup {
  priority: number;        // 优先级 1-20
  type: string;           // 股东类型名称
  matchField: string;     // 匹配字段类型：SHAREHOLDER_NAME、SHAREHOLDER_CATEGORY、SHAREHOLDER_NAME_INCLUDE
  rules: string[];        // 该优先级和类型下的所有匹配规则
}

// 3. 前端缓存数据结构
interface ClassificationCacheData {
  rules: ShareholderClassificationRuleGroup[];  // 分组后的规则数据
  lastUpdatedAt: string;                        // 最后更新时间
  cachedAt: number;                            // 缓存时间戳
}

// 3. 匹配结果类型
interface ClassificationResult {
  shareholderType: string; // 匹配到的股东类型
  matchedRule?: {
    priority: number;
    type: string;
    rule: string;
  };
}
```

#### 3.1.2 核心函数列表
```typescript
/**
 * 主函数：为股东数据批量添加股东类型
 * @param shareholders 股东数据数组
 * @param registryType 名册类型
 * @returns 添加了shareholderType字段的股东数据
 */
export async function classifyShareholdersBatch(
  shareholders: any[], 
  registryType: string
): Promise<any[]>

/**
 * 获取或更新股东分类规则缓存
 * @returns 分组后的规则数据
 */
async function getClassificationRules(): Promise<ShareholderClassificationRuleGroup[]>

/**
 * 检查规则更新时间并决定是否更新缓存
 * @returns 是否需要更新缓存
 */
async function checkRulesUpdateTime(): Promise<boolean>

/**
 * 单个股东类型匹配
 * @param shareholder 股东数据
 * @param rules 分组后的规则数据
 * @param registryType 名册类型
 * @returns 匹配结果
 */
function classifySingleShareholder(
  shareholder: any,
  rules: ShareholderClassificationRuleGroup[],
  registryType: string
): ClassificationResult

/**
 * 获取股东名称字段值
 * @param shareholder 股东数据
 * @param registryType 名册类型
 * @returns 股东名称
 */
function getShareholderName(shareholder: any, registryType: string): string

/**
 * 获取持有人类别字段值
 * @param shareholder 股东数据
 * @param registryType 名册类型
 * @returns 持有人类别代码
 */
function getShareholderCategory(shareholder: any, registryType: string): string

/**
 * 规则组匹配逻辑
 * @param shareholderName 股东名称
 * @param shareholderCategory 持有人类别
 * @param ruleGroup 分组后的规则数据
 * @returns 是否匹配
 */
function isRuleGroupMatch(
  shareholderName: string,
  shareholderCategory: string,
  ruleGroup: ShareholderClassificationRuleGroup
): boolean
```

### 3.2 缓存机制设计

#### 3.2.1 本地存储结构
```typescript
// localStorage 存储键名
const CACHE_KEYS = {
  RULES_DATA: 'shareholder_classification_rules_encrypted',    // 加密的规则数据
  UPDATE_TIME: 'shareholder_classification_update_time'        // 明文的更新时间
};
```

#### 3.2.2 缓存更新流程
1. **首次加载**：调用规则查询API (`/rules/list`)，获取分组后的 `rules`、`total`、`lastUpdatedAt`
   - 将分组后的 `rules` 数组加密后存储到 `RULES_DATA`
   - 将 `lastUpdatedAt` 明文存储到 `UPDATE_TIME`
2. **后续使用**：调用更新时间检查API (`/check-update-time`)，获取最新的 `lastUpdatedAt`
3. **时间比较**：比较API返回的 `lastUpdatedAt` 与缓存的 `UPDATE_TIME`
4. **缓存策略**：
   - 如果时间一致：直接使用本地缓存的加密规则数据，解密后使用
   - 如果时间不一致：重新调用 `/rules/list` 获取最新分组规则并更新缓存

**缓存数据优势：**
- 数据量更小：去除了id、matchField、updatedAt等字段
- 结构简化：按priority和type分组，便于匹配逻辑处理
- 减少网络传输：分组后的数据结构更紧凑

### 3.3 API调用设计

#### 3.3.1 API接口定义
基于 `docs/股东归类数据库API方案.md` 文档中的接口设计：

**规则查询接口：** `POST /api/shareholder-registry/rules/list`
- 响应包含：`rules`、`total`、`lastUpdatedAt`
- 首次调用时缓存所有数据

**更新时间检查接口：** `POST /api/shareholder-registry/check-update-time`
- 响应包含：`lastUpdatedAt`、`totalRules`
- 后续调用时仅检查时间是否变化

#### 3.3.2 API调用函数
```typescript
/**
 * 调用股东分类规则查询API
 * @returns API响应数据（分组后的规则）
 */
async function fetchClassificationRules(): Promise<{
  rules: ShareholderClassificationRuleGroup[];
  total: number;
  lastUpdatedAt: string;
}>

/**
 * 调用更新时间检查API
 * @returns 最新更新时间
 */
async function fetchUpdateTime(): Promise<{
  lastUpdatedAt: string;
  totalRules: number;
}>
```

#### 3.3.2 加密解密处理
```typescript
// 使用项目现有的加密工具
import { 
  createEncryptedRequestAsync, 
  decryptData 
} from '@repo/utils/lib/crypto';

/**
 * 创建加密请求
 * @param data 请求数据
 * @returns 加密请求对象
 */
async function createEncryptedApiRequest(data: any): Promise<{
  content: string;
  sign: string;
}>

/**
 * 解密API响应数据
 * @param encryptedData 加密的响应数据
 * @returns 解密后的数据
 */
function decryptApiResponse<T>(encryptedData: string): T
```

## 4. 匹配规则逻辑

### 4.1 优先级匹配流程
```mermaid
graph TD
    A[开始处理单个股东] --> B[读取股东名称及持有人类别]
    B --> C[按优先级1开始遍历规则]
    C --> D{当前规则是否匹配}
    D -->|是| E[赋值shareholderType为当前规则类型]
    D -->|否| F[获取下一条优先级规则]
    F --> G{是否还有规则}
    G -->|是| D
    G -->|否| H[赋值为"其他机构"默认类型]
    E --> I[结束处理]
    H --> I
```

### 4.2 规则匹配类型

根据 `matchField` 字段值确定匹配方式：

1. **SHAREHOLDER_NAME**：股东名称完全匹配
   - 匹配逻辑：`shareholderName === rule`
   - 适用场景：精确匹配特定股东名称或机构名称
   - 示例：rule="证券股份有限公司"

2. **SHAREHOLDER_CATEGORY**：持有人类别匹配
   - 匹配逻辑：`shareholderCategory.includes(rule)`
   - 适用场景：根据持有人类别代码进行分类
   - 示例：rule="国有法人(01)" 或 rule="2100"

3. **SHAREHOLDER_NAME_INCLUDE**：股东名称包含匹配
   - 匹配逻辑：`shareholderName.includes(rule)`
   - 适用场景：关键字匹配，如保险公司需要同时包含"保险"和"公司"
   - 示例：rule="保险" 或 rule="公司"

### 4.3 匹配逻辑设计原则

**基于API返回的matchField进行匹配：**
1. **避免硬编码**：不根据股东类型名称写死匹配逻辑
2. **依赖API数据**：完全基于API返回的 `matchField` 字段确定匹配方式
3. **灵活扩展**：新增股东类型时无需修改前端代码，只需配置数据库规则

**特殊匹配逻辑：**
- **保险类AND逻辑**：当 `matchField` 为 `SHAREHOLDER_NAME_INCLUDE` 且 `type` 为 "保险" 时，使用AND逻辑（必须同时包含所有关键字）
- **其他OR逻辑**：其他情况下使用OR逻辑（满足任一规则即可）

### 4.4 匹配示例

```typescript
// 前端缓存的分组规则数据示例
const exampleRuleGroups: ShareholderClassificationRuleGroup[] = [
  { priority: 1, type: "知名牛散", matchField: "SHAREHOLDER_NAME", rules: ["葛卫东"] },
  { priority: 3, type: "QFII&RQFII", matchField: "SHAREHOLDER_NAME_INCLUDE", rules: ["瑞士银行"] },
  { priority: 13, type: "保险", matchField: "SHAREHOLDER_NAME_INCLUDE", rules: ["保险", "公司"] },
  { priority: 14, type: "证券", matchField: "SHAREHOLDER_NAME", rules: ["证券股份有限公司", "金融股份有限公司"] },
  { priority: 16, type: "国有机构", matchField: "SHAREHOLDER_CATEGORY", rules: ["国有法人(01)", "2100"] },
  { priority: 17, type: "境内机构", matchField: "SHAREHOLDER_CATEGORY", rules: ["境内一般法人(02)", "2000", "2002", "2001"] },
  { priority: 20, type: "其他机构", matchField: "SHAREHOLDER_NAME", rules: ["*"] } // 兜底规则
];

// 正确的规则组匹配逻辑示例（基于API返回的matchField）
function isRuleGroupMatch(
  shareholderName: string,
  shareholderCategory: string,
  ruleGroup: ShareholderClassificationRuleGroup
): boolean {
  const { matchField, rules } = ruleGroup;

  // 根据API返回的matchField确定匹配逻辑
  switch (matchField) {
    case "SHAREHOLDER_NAME":
      // 股东名称完全匹配（OR逻辑）
      return rules.some(rule => {
        if (rule === "*") return true; // 兜底规则
        return shareholderName === rule;
      });

    case "SHAREHOLDER_CATEGORY":
      // 持有人类别包含匹配（OR逻辑）
      return rules.some(rule => shareholderCategory.includes(rule));

    case "SHAREHOLDER_NAME_INCLUDE":
      // 股东名称包含匹配
      if (ruleGroup.type === "保险") {
        // 保险类特殊处理：需要同时包含所有关键字（AND逻辑）
        return rules.every(rule => shareholderName.includes(rule));
      } else {
        // 其他类型：包含任一关键字即可（OR逻辑）
        return rules.some(rule => shareholderName.includes(rule));
      }

    default:
      console.warn(`未知的匹配字段类型: ${matchField}`);
      return false;
  }
}

// 股东分类主函数示例
function classifySingleShareholder(
  shareholder: any,
  ruleGroups: ShareholderClassificationRuleGroup[],
  registryType: string
): ClassificationResult {
  const shareholderName = getShareholderName(shareholder, registryType);
  const shareholderCategory = getShareholderCategory(shareholder, registryType);

  // 按优先级遍历规则组
  for (const ruleGroup of ruleGroups.sort((a, b) => a.priority - b.priority)) {
    if (isRuleGroupMatch(shareholderName, shareholderCategory, ruleGroup)) {
      return {
        shareholderType: ruleGroup.type,
        matchedRule: {
          priority: ruleGroup.priority,
          type: ruleGroup.type,
          rule: ruleGroup.rules.join(", ")
        }
      };
    }
  }

  // 默认分类
  return { shareholderType: "其他机构" };
}
```

## 5. 集成方案

### 5.1 ShareholderRegistryImport 组件修改
在 `handleUpload` 函数中，上传前调用股东类型匹配：

```typescript
// 在 ShareholderRegistryImport.tsx 中的修改位置
async function handleUpload() {
  // ... 现有的验证逻辑
  
  // 新增：股东类型匹配
  try {
    const classifiedShareholders = await classifyShareholdersBatch(
      result.shareholders,
      result.registryType
    );
    
    // 更新解析结果中的股东数据
    result.shareholders = classifiedShareholders;
  } catch (error) {
    console.error('股东类型匹配失败:', error);
    // 匹配失败不阻断上传流程，继续使用原始数据
  }
  
  // ... 现有的上传逻辑
  await uploadRegistry({
    // ... 现有参数
    shareholders: result.shareholders // 使用分类后的数据
  });
}
```

### 5.2 类型定义扩展
在 `types.ts` 中添加股东类型相关定义：

```typescript
// 扩展 ShareholderItem 接口
export interface ShareholderItem {
  // ... 现有字段
  shareholderType?: string; // 新增：股东类型字段
}

// 新增股东分类相关类型（前端使用的分组数据结构）
export interface ShareholderClassificationRuleGroup {
  priority: number;
  type: string;
  matchField: string;
  rules: string[];
}

// 前端缓存数据结构
export interface ClassificationCacheData {
  rules: ShareholderClassificationRuleGroup[];
  lastUpdatedAt: string;
  cachedAt: number;
}

export interface ClassificationResult {
  shareholderType: string;
  matchedRule?: {
    priority: number;
    type: string;
    rule: string;
  };
}
```

## 6. 数据库字段使用说明

### 6.1 matchField字段详细说明

新增的 `matchField` 字段用于指定每条规则的匹配方式，支持以下三种值：

| matchField值 | 匹配逻辑 | 使用场景 | 数据示例 |
|-------------|----------|----------|----------|
| SHAREHOLDER_NAME | 股东名称完全匹配 | 精确匹配特定股东或机构 | "证券股份有限公司"、"金融股份有限公司" |
| SHAREHOLDER_CATEGORY | 持有人类别包含匹配 | 根据CYRLBMS代码分类 | "国有法人(01)"、"2100"、"2000" |
| SHAREHOLDER_NAME_INCLUDE | 股东名称包含匹配 | 关键字匹配 | "保险"、"公司"、"年金" |


### 6.3 匹配逻辑更新要点

1. **单规则匹配**：大部分股东类型使用单条规则即可匹配
2. **多规则AND逻辑**：保险类股东需要同时满足多个 `SHAREHOLDER_NAME_INCLUDE` 规则
3. **字段映射兼容**：匹配函数需要根据名册类型正确获取股东名称和持有人类别字段
4. **优先级处理**：按优先级从低到高遍历，首次匹配成功即停止

## 7. 实施步骤

### 7.1 第一阶段：核心函数开发 ✅ 已完成
1. 创建 `shareholder-classifier.ts` 文件
2. 创建 `classification-api.ts` API客户端文件
3. 实现缓存管理和API调用函数
4. 实现股东类型匹配核心逻辑
5. 添加字段映射和兼容性处理

### 7.2 第二阶段：数据库字段更新 🔄 待实施
1. 更新 Prisma Schema 添加 `matchField` 字段
2. 执行数据库迁移：`pnpm --filter database push`
3. 重新生成 Prisma 客户端：`pnpm --filter database generate`
4. 更新API验证器添加 `matchField` 字段验证

### 7.3 第三阶段：匹配逻辑更新 🔄 待实施
1. 修改 `shareholder-classifier.ts` 中的 `isRuleGroupMatch` 函数
2. 基于API返回的 `matchField` 字段进行匹配，避免硬编码
3. 实现三种匹配方式的逻辑分支：SHAREHOLDER_NAME、SHAREHOLDER_CATEGORY、SHAREHOLDER_NAME_INCLUDE
4. 添加保险类特殊AND逻辑处理（仅针对type="保险"且matchField="SHAREHOLDER_NAME_INCLUDE"的情况）

### 7.4 第四阶段：集成测试 🔄 待实施
1. 修改 `types.ts` 添加类型定义
2. 修改 `ShareholderRegistryImport.tsx` 集成匹配函数
3. 测试不同名册类型的字段兼容性
4. 测试缓存机制和API调用
5. 测试新的匹配字段类型功能

### 7.5 已创建的文件说明
- **`classification-api.ts`**：股东分类规则API客户端，处理加密请求和响应解密
- **`shareholder-classifier.ts`**：股东类型匹配核心函数，包含缓存管理和匹配逻辑

---

**文档作者：** hayden
**最后更新：** 2025-06-26 14:04:31
**文档版本：** V.4

