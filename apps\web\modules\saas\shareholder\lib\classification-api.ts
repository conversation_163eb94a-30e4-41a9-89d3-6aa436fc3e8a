/**
 * 股东分类规则API客户端
 *
 * 功能：
 * 1. 查询股东分类规则列表
 * 2. 检查规则更新时间
 * 3. 处理加密请求和响应解密
 *
 * <AUTHOR>
 * @created 2025-06-25 13:38:45
 * @modified 2025-06-25 14:04:10 - 规范化API调用格式，参考registry-api.ts规范
 * @description 基于股东归类数据库API方案实现的前端API客户端
 */

import { apiClient } from "@shared/lib/api-client";
import { decryptData, createEncryptedRequestAsync } from "@repo/utils";

// 定义API响应类型
interface ApiResponse {
	code: number;
	message: string;
	data: string | any;
	error?: {
		code: string;
		message: string;
		details?: any;
	};
}

/**
 * 股东分类规则数据类型（原始单条规则）
 */
export interface ShareholderClassificationRule {
  id: string;
  priority: number;        // 优先级 1-20
  type: string;           // 股东类型名称
  rule: string;           // 匹配规则
  updatedAt: string;      // 更新时间
}

/**
 * 分组后的股东分类规则数据类型（API返回的新格式）
 * <AUTHOR>
 * @created 2025-06-26 14:43:52
 */
export interface ShareholderClassificationRuleGroup {
  priority: number;        // 优先级 1-20
  type: string;           // 股东类型名称
  matchField: string;     // 匹配字段类型：SHAREHOLDER_NAME、SHAREHOLDER_CATEGORY、SHAREHOLDER_NAME_INCLUDE
  rules: string[];        // 该优先级和类型下的所有匹配规则
}



// 股东分类规则API客户端
export const shareholderClassificationApi = {
	/**
	 * 查询所有股东分类规则（分组后的数据结构）
	 *
	 * @returns 分组后的规则列表和相关信息
	 * <AUTHOR>
	 * @created 2025-06-25 14:04:10
	 * @modified 2025-06-26 14:43:52 - 更新返回类型为分组后的规则数据结构
	 */
	fetchRules: async (): Promise<{
		rules: ShareholderClassificationRuleGroup[];
		total: number;
		lastUpdatedAt: string;
	}> => {
		// 创建加密且带签名的请求参数
		const requestData = await createEncryptedRequestAsync({});

		try {
			// 发送请求到股东分类规则查询接口
						const response = await apiClient["shareholder-registry"].rules.list.$post({
				json: requestData,
			});

			// 获取响应数据
			const responseData = await response.json() as ApiResponse;

			// 检查响应状态码
			if (responseData.code !== 200) {
				// 直接返回错误响应，不抛出错误
				throw new Error(responseData.message || "获取股东分类规则失败");
			}

			let decryptedData: any;

			// 解密数据
			if (typeof responseData.data === 'string') {
				try {
					// 尝试解密
					decryptedData = decryptData(responseData.data);

					// 如果解密后是字符串，尝试解析为JSON
					if (typeof decryptedData === 'string') {
						try {
							decryptedData = JSON.parse(decryptedData);
						} catch (error) {
							// 解析失败，使用默认结构
							decryptedData = {
								rules: [],
								total: 0,
								lastUpdatedAt: new Date().toISOString()
							};
						}
					}
				} catch (error) {
					// 解密失败，使用默认结构
					decryptedData = {
						rules: [],
						total: 0,
						lastUpdatedAt: new Date().toISOString()
					};
				}
			} else {
				// 如果数据不是字符串，直接使用
				decryptedData = responseData.data;
			}

			// 确保返回格式正确
			if (!decryptedData || typeof decryptedData !== 'object') {
				return {
					rules: [],
					total: 0,
					lastUpdatedAt: new Date().toISOString()
				};
			}

			// 确保rules是数组
			if (!decryptedData.rules || !Array.isArray(decryptedData.rules)) {
				decryptedData.rules = [];
			}

			// 确保total存在
			if (typeof decryptedData.total !== 'number') {
				decryptedData.total = decryptedData.rules.length;
			}

			// 确保lastUpdatedAt存在
			if (!decryptedData.lastUpdatedAt) {
				decryptedData.lastUpdatedAt = new Date().toISOString();
			}

			return decryptedData;
		} catch (error) {
			// 失败时返回默认结构
			console.error('获取股东分类规则失败:', error);
			throw new Error(`获取股东分类规则失败: ${error instanceof Error ? error.message : '未知错误'}`);
		}
	},

	/**
	 * 检查规则更新时间
	 *
	 * @returns 最新更新时间和规则总数
	 * <AUTHOR>
	 * @created 2025-06-25 14:04:10
	 */
	checkUpdateTime: async (): Promise<{
		lastUpdatedAt: string;
		totalRules: number;
	}> => {
		// 创建加密且带签名的请求参数
		const requestData = await createEncryptedRequestAsync({});

		try {
			// 发送请求到股东分类规则更新时间检查接口
						const response = await apiClient["shareholder-registry"].rules[
				"check-update-time"
			].$post({
				json: requestData,
			});

			// 获取响应数据
			const responseData = await response.json() as ApiResponse;

			// 检查响应状态码
			if (responseData.code !== 200) {
				throw new Error(responseData.message || "检查规则更新时间失败");
			}

			let decryptedData: any;

			// 解密数据
			if (typeof responseData.data === 'string') {
				try {
					// 尝试解密
					decryptedData = decryptData(responseData.data);

					// 如果解密后是字符串，尝试解析为JSON
					if (typeof decryptedData === 'string') {
						try {
							decryptedData = JSON.parse(decryptedData);
						} catch (error) {
							// 解析失败，使用默认结构
							decryptedData = {
								lastUpdatedAt: new Date().toISOString(),
								totalRules: 0
							};
						}
					}
				} catch (error) {
					// 解密失败，使用默认结构
					decryptedData = {
						lastUpdatedAt: new Date().toISOString(),
						totalRules: 0
					};
				}
			} else {
				// 如果数据不是字符串，直接使用
				decryptedData = responseData.data;
			}

			// 确保返回格式正确
			if (!decryptedData || typeof decryptedData !== 'object') {
				return {
					lastUpdatedAt: new Date().toISOString(),
					totalRules: 0
				};
			}

			// 确保lastUpdatedAt存在
			if (!decryptedData.lastUpdatedAt) {
				decryptedData.lastUpdatedAt = new Date().toISOString();
			}

			// 确保totalRules存在
			if (typeof decryptedData.totalRules !== 'number') {
				decryptedData.totalRules = 0;
			}

			return decryptedData;
		} catch (error) {
			console.error('检查规则更新时间失败:', error);
			throw new Error(`检查规则更新时间失败: ${error instanceof Error ? error.message : '未知错误'}`);
		}
	}
};

/**
 * 导出API客户端的类型定义，供其他模块使用
 *
 * <AUTHOR>
 * @created 2025-06-25 14:04:10
 */
export type ShareholderClassificationApiClient = typeof shareholderClassificationApi;
