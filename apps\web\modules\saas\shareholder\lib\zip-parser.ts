/**
 * ZIP文件解析器
 * 支持ZIP压缩文件的解析和内部文件名验证
 * 
 * 主要功能：
 * 1. 解析ZIP文件并提取内部的DBF或Excel文件
 * 2. 验证ZIP文件名与内部文件名的匹配关系
 * 3. 支持严格的文件名格式验证
 * 4. 提供详细的错误信息和建议
 * 
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 */

import JSZip from "jszip";
import { parseDBFFile } from "./dbf-parser";
import * as ExcelParser from "./excel-parser";
import type { ShareholderRegistryParseResult } from "./dbf-parser/common";
import {
  validateZipFileName as configValidateZipFileName,
  STRICT_FILENAME_VALIDATION,
  FILENAME_ERROR_MESSAGES
} from "./config";

// 重新导出配置中的验证函数
export { configValidateZipFileName as validateZipFileName };

/**
 * 检查文件类型是否为ZIP文件
 *
 * @param fileName 文件名
 * @returns 是否为ZIP文件
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 */
export function isZipFile(fileName: string): boolean {
  const lowercaseName = fileName.toLowerCase();
  return lowercaseName.endsWith(".zip");
}

/**
 * 检查文件类型是否为DBF文件
 *
 * @param fileName 文件名
 * @returns 是否为DBF文件
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 */
function isDbfFile(fileName: string): boolean {
  const lowercaseName = fileName.toLowerCase();
  return lowercaseName.endsWith(".dbf");
}

/**
 * 验证ZIP文件内部文件名与ZIP文件名的匹配关系
 *
 * @param zipFileName ZIP文件名
 * @param internalFileName 内部文件名
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 */
export function validateZipInternalFileName(
  zipFileName: string, 
  internalFileName: string
): {
  isValid: boolean;
  error?: string;
} {
  if (!STRICT_FILENAME_VALIDATION) {
    return { isValid: true };
  }

  // 验证ZIP文件名格式
  const zipValidation = configValidateZipFileName(zipFileName);
  if (!zipValidation.isValid) {
    return {
      isValid: false,
      error: zipValidation.error
    };
  }

  // 检查内部文件名是否与期望的文件名匹配
  if (zipValidation.expectedInternalFileName) {
    const expectedName = zipValidation.expectedInternalFileName;
    
    // 进行大小写不敏感的比较
    if (internalFileName.toLowerCase() !== expectedName.toLowerCase()) {
      return {
        isValid: false,
        error: `${FILENAME_ERROR_MESSAGES.ZIP_CONTENT_MISMATCH}，期望文件名：${expectedName}，实际文件名：${internalFileName}`
      };
    }
  }

  return { isValid: true };
}

/**
 * 从ZIP文件中提取并解析股东名册文件
 *
 * @param file ZIP文件
 * @returns 解析结果
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 * @update 2025-06-23 16:01:13 - 添加严格文件名验证逻辑
 */
export async function parseZipFile(file: File): Promise<ShareholderRegistryParseResult> {
  try {
    // 在严格模式下，首先验证ZIP文件名格式
    if (STRICT_FILENAME_VALIDATION) {
      const zipValidation = configValidateZipFileName(file.name);
      if (!zipValidation.isValid) {
        return {
          success: false,
          fileName: file.name,
          error: {
            type: "FILE_ERROR",
            message: zipValidation.error || FILENAME_ERROR_MESSAGES.INVALID_ZIP_FORMAT,
          },
        };
      }
    }

    // 读取ZIP文件
    const arrayBuffer = await file.arrayBuffer();
    const zip = new JSZip();
    const zipContents = await zip.loadAsync(arrayBuffer);
    
    // 查找支持的文件
    let foundFile: { name: string; content: Blob } | null = null;
    
    // 首先查找DBF文件
    for (const fileName in zipContents.files) {
      if (isDbfFile(fileName) && !zipContents.files[fileName].dir) {
        const content = await zipContents.files[fileName].async("blob");
        foundFile = { name: fileName, content };
        break;
      }
    }
    
    // 如果没有找到DBF文件，查找Excel文件
    if (!foundFile) {
      for (const fileName in zipContents.files) {
        if (ExcelParser.isExcelFile(fileName) && !zipContents.files[fileName].dir) {
          const content = await zipContents.files[fileName].async("blob");
          foundFile = { name: fileName, content };
          break;
        }
      }
    }
    
    // 如果没有找到支持的文件，返回错误
    if (!foundFile) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILE_ERROR",
          message: FILENAME_ERROR_MESSAGES.UNSUPPORTED_TYPE,
        },
      };
    }

    // 在严格模式下且未跳过验证时，验证内部文件名与ZIP文件名的匹配关系
    if (STRICT_FILENAME_VALIDATION) {
      const internalValidation = validateZipInternalFileName(file.name, foundFile.name);
      if (!internalValidation.isValid) {
        return {
          success: false,
          fileName: file.name,
          error: {
            type: "FILE_ERROR",
            message: internalValidation.error || FILENAME_ERROR_MESSAGES.ZIP_CONTENT_MISMATCH,
          },
        };
      }
    }
    
    // 根据找到的文件类型调用相应的解析方法
    const extractedFile = new File([foundFile.content], foundFile.name);
    
    if (isDbfFile(foundFile.name)) {
      return await parseDBFFile(extractedFile);
    }
    
    if (ExcelParser.isExcelFile(foundFile.name)) {
      return await ExcelParser.parseExcelFile(extractedFile);
    }
    
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: "ZIP文件中的文件格式不受支持",
      },
    };
  } catch (error) {
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: error instanceof Error ? error.message : "解析ZIP文件失败",
      },
    };
  }
}

/**
 * 获取ZIP文件中的文件列表
 *
 * @param file ZIP文件
 * @returns 文件列表
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 */
export async function getZipFileList(file: File): Promise<string[]> {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const zip = new JSZip();
    const zipContents = await zip.loadAsync(arrayBuffer);
    
    const fileList: string[] = [];
    for (const fileName in zipContents.files) {
      if (!zipContents.files[fileName].dir) {
        fileList.push(fileName);
      }
    }
    
    return fileList;
  } catch (error) {
    console.error('获取ZIP文件列表失败:', error);
    return [];
  }
}
