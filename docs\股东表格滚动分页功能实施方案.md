# 股东表格滚动分页功能实施方案

## 概述

本文档记录了股东表格滚动分页功能的实施过程，解决了三个关键问题：
1. 点击排序时分页页码没有重置
2. 数据滚动时没有进行叠加缓存
3. 排序后滚动条没有回到顶部位置

## 实施时间

**实施日期**: 2025年06月13日10:13:43
**最后更新**: 2025年06月13日11:12:58
**实施人员**: hayden

## 问题分析

### 问题1：排序时分页页码没有重置

**原因**: 
- `useEffect` 依赖于 `onSort` 函数引用，但该函数从父组件传入，可能不会在每次排序时重新创建
- 缺少明确的页码重置逻辑

**解决方案**:
- 修改 `useEffect` 依赖为具体的排序参数 `sortBy` 和 `sortOrder`
- 在 `handleSort` 函数中添加 `onPageChange(1)` 来主动重置页码

### 问题2：数据滚动时没有进行叠加缓存

**原因**:
- 组件直接使用传入的 `shareholders` 数据，每次新页面数据会替换之前的数据
- 缺少数据累积逻辑

**解决方案**:
- 添加 `accumulatedData` 状态来存储累积的数据
- 根据页码判断是替换数据（第一页）还是累积数据（后续页）
- 使用去重逻辑避免重复数据

### 问题3：排序后滚动条没有回到顶部位置

**原因**:
- 排序后虽然重置了页码，但滚动位置仍然保持在之前的位置
- 用户可能看不到新的排序结果，影响用户体验

**解决方案**:
- 添加 `tableScrollRef` 引用来获取表格滚动容器
- 在排序和搜索变化时，使用 `scrollTo` 方法重置滚动位置
- 使用平滑滚动效果提升用户体验

## 修改文件列表

### 1. ShareholderTable.tsx

**主要修改**:
- 添加 `accumulatedData` 状态管理累积数据
- 修改 `useEffect` 依赖为具体参数而非函数引用
- 在 `handleSort` 中添加页码重置逻辑
- 修改数据转换逻辑使用累积数据
- 优化滚动事件处理

**关键代码变更**:
```typescript
// 添加累积数据状态和滚动容器引用
const [accumulatedData, setAccumulatedData] = useState<ShareholderItem[]>([]);
const tableScrollRef = useRef<HTMLDivElement>(null);

// 修改useEffect依赖，添加滚动位置重置
useEffect(() => {
  setAccumulatedData(validShareholders);
  setResetPaginationFlag(true);

  // 重置滚动位置到顶部
  if (tableScrollRef.current) {
    tableScrollRef.current.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // 重置滚动记录
  scrollRef.current = { left: 0, top: 0 };
  // ...
}, [sortBy, sortOrder, searchTerm]); // 依赖具体参数

// 数据累积逻辑
useEffect(() => {
  if (page === 1) {
    setAccumulatedData(validShareholders);
  } else {
    setAccumulatedData(prevData => {
      const existingIds = new Set(prevData.map(item => item.id));
      const newItems = validShareholders.filter(item => !existingIds.has(item.id));
      return [...prevData, ...newItems];
    });
  }
}, [validShareholders, page]);

// 排序时重置页码和滚动位置
const handleSort = (column: string, order: 'asc' | 'desc') => {
  // ... 排序字段转换逻辑
  onPageChange(1); // 重置页码

  // 立即重置滚动位置到顶部
  if (tableScrollRef.current) {
    tableScrollRef.current.scrollTo({ top: 0, behavior: 'smooth' });
  }

  onSort(apiSortField, order);
};

// 滚动事件处理中记录滚动容器引用 - 修改于2025年06月13日10:22:15
const handleTableScroll = (event: React.UIEvent<HTMLDivElement>) => {
  const { scrollTop, clientHeight, scrollHeight, scrollLeft } =
    event.target as HTMLDivElement;

  // 记录滚动容器引用 - 添加于2025年06月13日10:22:15
  if (!tableScrollRef.current && event.target) {
    tableScrollRef.current = event.target as HTMLDivElement;
  }

  // 计算是否还有更多数据可以加载
  const totalPages = pagination?.totalPages || 1;
  const hasMoreData = page < totalPages;

  // 垂直滚动 & 到底了 & 有更多数据 & 不在加载中
  if (
    Math.abs(scrollTop - scrollRef.current.top) > 0 &&
    scrollTop + clientHeight >= scrollHeight - 50 && // 提前50px触发加载
    hasMoreData &&
    !isScrollLoading &&
    !isLoading
  ) {
    // 设置滚动加载状态
    setIsScrollLoading(true);

    // 调用页面变更函数，加载下一页
    onPageChange(page + 1);

    // 延迟重置加载状态，避免频繁触发
    setTimeout(() => {
      setIsScrollLoading(false);
    }, 500);
  }

  // 记录当前滚动信息
  scrollRef.current = {
    left: scrollLeft,
    top: scrollTop,
  };
};

// 设置滚动配置 - 添加于2025年06月13日10:22:15
const scrollConfig = useMemo(() => {
  return {
    x: "max-content", // 启用水平滚动
    y: "calc(90vh - 300px)", // 固定高度，与示例代码保持一致
    scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
  };
}, []);
```

### 2. TablePagination.tsx

**主要修改**:
- 重构为纯滚动加载模式
- 移除"加载更多"按钮
- 只显示加载状态和数据统计
- 清理不再使用的导入

**关键代码变更**:
```typescript
// 移除按钮加载逻辑，只保留状态显示
export function TablePagination({
  pagination,
  page,
  onPageChange, // 保留但不再使用，避免破坏接口兼容性
  isScrollLoading = false,
  resetPagination = false,
}: TablePaginationProps) {
  // 只显示加载状态和数据统计，不显示按钮
  if (isScrollLoading) {
    return (
      <div className="flex flex-col items-center justify-center px-2 py-4 text-xs">
        <div className="flex items-center text-foreground mb-2">
          <Loader2 className="animate-spin mr-2 h-4 w-4" />
          正在加载更多数据...
        </div>
      </div>
    );
  }
  // ... 其他状态显示逻辑
}
```

### 3. ant-shareholder-table.tsx

**主要修改**:
- 添加 `onScroll` 和 `scroll` 属性支持
- 修改滚动配置逻辑优先使用传入的配置
- 将滚动事件传递给 Ant Design Table

**关键代码变更**:
```typescript
// 添加接口属性
export interface AntShareholderTableProps {
  // ... 其他属性
  onScroll?: (event: React.UIEvent<HTMLDivElement>) => void;
  scroll?: object;
}

// 滚动配置逻辑
const scroll = React.useMemo(() => {
  if (scrollProp) {
    return scrollProp; // 优先使用传入的配置
  }
  // ... 默认配置逻辑
}, [data.length, scrollProp]);

// 传递给Ant Design Table
<Table
  // ... 其他属性
  onScroll={onScroll}
  scroll={scroll}
/>
```

## 技术实现详解

### 1. 滚动事件处理机制

#### 1.1 滚动容器引用管理
```typescript
/**
 * 滚动容器引用管理
 * @description 动态获取并缓存表格滚动容器的引用，用于后续的滚动位置控制
 * <AUTHOR> Agent
 * @date 2025年06月13日10:22:15
 */
const tableScrollRef = useRef<HTMLDivElement>(null);

// 在滚动事件中动态记录容器引用
if (!tableScrollRef.current && event.target) {
  tableScrollRef.current = event.target as HTMLDivElement;
}
```

#### 1.2 滚动位置计算
```typescript
/**
 * 滚动位置检测算法
 * @description 精确计算滚动位置，判断是否需要触发分页加载
 * @param scrollTop 当前垂直滚动位置
 * @param clientHeight 可视区域高度
 * @param scrollHeight 总内容高度
 * @returns boolean 是否触发加载
 */
const shouldTriggerLoad = (scrollTop: number, clientHeight: number, scrollHeight: number) => {
  const TRIGGER_THRESHOLD = 50; // 提前50px触发加载
  return scrollTop + clientHeight >= scrollHeight - TRIGGER_THRESHOLD;
};
```

#### 1.3 防抖机制
```typescript
/**
 * 滚动加载防抖机制
 * @description 防止频繁触发加载请求，提升性能和用户体验
 * @delay 500ms 延迟重置加载状态
 */
setTimeout(() => {
  setIsScrollLoading(false);
}, 500);
```

### 2. 数据累积策略

#### 2.1 智能数据合并
```typescript
/**
 * 数据累积逻辑
 * @description 根据页码智能处理数据合并，第一页替换，后续页累积
 * @param page 当前页码
 * @param validShareholders 新数据
 * @returns 处理后的累积数据
 */
useEffect(() => {
  if (page === 1) {
    // 第一页：直接替换数据，确保排序和搜索结果正确
    setAccumulatedData(validShareholders);
  } else {
    // 后续页：累积数据，支持无限滚动
    setAccumulatedData(prevData => {
      const existingIds = new Set(prevData.map(item => item.id));
      const newItems = validShareholders.filter(item => !existingIds.has(item.id));
      return [...prevData, ...newItems];
    });
  }
}, [validShareholders, page]);
```

#### 2.2 数据去重算法
```typescript
/**
 * 数据去重策略
 * @description 基于ID的高效去重算法，避免重复数据显示
 * @complexity O(n) 时间复杂度
 */
const existingIds = new Set(prevData.map(item => item.id));
const newItems = validShareholders.filter(item => !existingIds.has(item.id));
```

### 3. 滚动配置优化

#### 3.1 响应式高度计算
```typescript
/**
 * 动态滚动配置
 * @description 根据视口高度动态计算表格滚动区域高度
 * @formula calc(90vh - 300px) 预留300px给其他UI元素
 */
const scrollConfig = useMemo(() => {
  return {
    x: "max-content", // 启用水平滚动，适应宽表格
    y: "calc(90vh - 300px)", // 动态高度，响应式设计
    scrollToFirstRowOnChange: false, // 优化滚动指示器显示
  };
}, []);
```

