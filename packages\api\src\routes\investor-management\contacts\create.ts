/**
 * 创建投资人联系人路由
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建联系人创建接口
 * @description 创建投资人联系人信息，支持姓名、电话、邮箱、地址和备注信息
 */
import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { CreateInvestorContactSchema } from "../lib/validators";

export const contactsCreateRouter = new Hono().post(
  "/create",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = CreateInvestorContactSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { organizationId, name, phoneNumber, email, address, remarks } = validationResult.data;

      // 创建联系人记录
      const newContact = await db.investorContact.create({
        data: {
          organizationId,
          name,
          phoneNumber: phoneNumber || "",
          email: email || "",
          address: address || "",
          remarks: remarks || "",
          createdBy: user.id,
        }
      });

      c.set("response", {
        code: 200,
        message: "联系人创建成功",
        data: {
          contactId: newContact.contactId
        }
      });
      return;

    } catch (error) {
      if (error instanceof HTTPException) {
        c.set("response", {
          code: error.status,
          message: error.message,
          data: null
        });
        return;
      }
      
      console.error("投资人管理API错误:", error);
      c.set("response", {
        code: 500,
        message: "服务器内部错误",
        data: null
      });
      return;
    }
  }
);
