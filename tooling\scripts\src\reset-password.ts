import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { nanoid } from "nanoid";

async function main() {
	logger.info("🔑 Reset User Password - 重置用户密码");
	logger.info("此脚本将为指定用户生成新密码并更新数据库");

	// 获取用户标识 (支持邮箱或ID)
	const userIdentifier = await logger.prompt("请输入要重置密码的用户邮箱或ID:", {
		required: true,
		placeholder: "<EMAIL> 或 user_id_123",
		type: "text",
	});

	// 查找用户
	logger.info("🔍 正在查找用户...");
	
	const user = await db.user.findFirst({
		where: {
			OR: [
				{ email: userIdentifier },
				{ id: userIdentifier },
			],
		},
		include: {
			accounts: {
				where: {
					providerId: "credential",
				},
			},
		},
	});

	if (!user) {
		logger.error("❌ 未找到指定的用户！");
		return;
	}

	// 显示用户信息
	logger.info("📋 找到用户信息:");
	logger.info(`   ID: ${user.id}`);
	logger.info(`   姓名: ${user.name}`);
	logger.info(`   邮箱: ${user.email}`);
	logger.info(`   角色: ${user.role || "user"}`);
	logger.info(`   创建时间: ${user.createdAt.toLocaleString()}`);

	// 检查是否有credential账户
	const credentialAccount = user.accounts.find(
		(account) => account.providerId === "credential"
	);

	if (!credentialAccount) {
		logger.warn("⚠️  该用户没有密码登录账户（可能只使用第三方登录）");
		
		const createCredentialAccount = await logger.prompt(
			"是否要为该用户创建密码登录账户？",
			{
				required: true,
				type: "confirm",
				default: false,
			}
		);

		if (!createCredentialAccount) {
			logger.info("✅ 操作已取消。");
			return;
		}
	}

	// 确认重置密码
	const confirmReset = await logger.prompt(
		"❓ 确认为此用户重置密码吗？",
		{
			required: true,
			type: "confirm",
			default: false,
		}
	);

	if (!confirmReset) {
		logger.info("✅ 操作已取消，密码未被重置。");
		return;
	}

	// 生成新密码和哈希 (与create-user.ts保持一致)
	logger.info("🔄 正在生成新密码...");
	const authContext = await auth.$context;
	const newPassword = nanoid(16);
	const hashedPassword = await authContext.password.hash(newPassword);

	try {
		await db.$transaction(async (tx) => {
			if (credentialAccount) {
				// 更新现有的credential账户密码
				logger.info("   🔐 更新现有密码账户...");
				await tx.account.update({
					where: {
						id: credentialAccount.id,
					},
					data: {
						password: hashedPassword,
						updatedAt: new Date(),
					},
				});
			} else {
				// 创建新的credential账户
				logger.info("   🆕 创建新的密码登录账户...");
				await tx.account.create({
					data: {
						id: nanoid(),
						userId: user.id,
						accountId: user.id,
						providerId: "credential",
						createdAt: new Date(),
						updatedAt: new Date(),
						password: hashedPassword,
					},
				});
			}

			// 更新用户的updatedAt时间戳
			await tx.user.update({
				where: {
					id: user.id,
				},
				data: {
					updatedAt: new Date(),
				},
			});
		});

		logger.success("✅ 密码重置成功！");
		logger.info("📋 重置操作摘要:");
		logger.info(`   - 用户: ${user.name} (${user.email})`);
		logger.info(`   - 操作: ${credentialAccount ? "更新现有密码" : "创建新密码账户"}`);
		logger.info(`   - 新密码: ${newPassword}`);
		logger.warn("⚠️  请妥善保管新密码，并建议用户首次登录后立即修改！");

	} catch (error) {
		logger.error("❌ 重置密码时发生错误:");
		logger.error(error);
		logger.error("密码未被重置，请检查错误信息后重试。");
	}
}

main().catch((error) => {
	logger.error("脚本执行失败:");
	logger.error(error);
	process.exit(1);
});
