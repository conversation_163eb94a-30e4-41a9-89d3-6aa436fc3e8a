import type { PrismaClient } from "@prisma/client";
import { HTTPException } from "hono/http-exception";
import type { UploadRegistryRequest } from "../types";
import type { RegistryType } from "./utils";
import { ShareholderRegistryConfig } from "./config";

/**
 * 股东名册处理工具函数集合
 * 
 * 本模块提供了一系列用于处理股东名册数据的工具函数，
 * 包括验证、绑定、记录处理、股东数据处理等功能。
 * 
 * 注意：该文件已于2025年06月03日 17:01:59更新，以支持新的字段映射关系。
 * 特别是securitiesAccountName字段在不同名册类型中的映射关系：
 * - 01名册：ZQZHMC (证券账户名称)
 * - 05名册：XYZQZHMC (证券账户名称)
 * - t1/t2/t3名册：CYRMC (持有人名称)
 */

/**
 * 验证组织和名册类型
 * 
 * 功能：
 * 1. 验证组织是否存在
 * 2. 验证名册类型合法性
 * 3. 检查是否存在同类型名册
 * 4. 检查是否已存在完整名册
 * 
 * 更新记录:
 * - 2025-06-04 13:25:28.469: 优化名册类型检测逻辑，支持t1/t2/t3开头的文件名格式
 *   如t36053380320241231t200.c31.xls、t16053380320240625all.625.xls等
 * 
 * @param tx Prisma事务客户端
 * @param data 请求数据
 * @param registryType 当前名册类型
 * @returns 组织信息和现有名册记录
 */
export async function validateOrganizationAndRegistry(
  tx: Omit<PrismaClient, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">,
  data: UploadRegistryRequest,
  registryType: RegistryType
) {
  // 并行查询组织信息和同日期名册记录
  const [organization, existingRegistry] = await Promise.all([
    // 查询组织信息
    tx.organization.findUnique({
      where: { id: data.organizationId },
      select: { id: true, metadata: true },
    }),
    // 查询是否存在同一日期的名册记录
    tx.shareholderRegistry.findFirst({
      where: {
        organizationId: data.organizationId,
        companyCode: data.companyCode,
        registerDate: new Date(data.registerDate),
      },
      select: { id: true, fileName: true },
    }),
  ]);

  // 检查组织是否存在
  if (!organization) {
    throw new HTTPException(404, { message: "组织不存在" });
  }

  // 名册类型验证
  if (existingRegistry) {
    const existingFileName = existingRegistry.fileName.toLowerCase();
    
    // 检查现有名册类型，使用正则表达式和关键字匹配相结合的方式
    const hasType01 = existingFileName.includes("dqmc01");
    const hasType05 = existingFileName.includes("dqmc05");
    
    // 更新于2025-06-19 18:57:58: hayden 修复对t2和t3名册的错误识别问题
    // 使用与detectRegistryTypeByFileName一致的严格检测逻辑
    // 特别注意t36053380320241231t200.c31.xls中的t200不应被识别为t2
    const hasT1 = /^t1\d/.test(existingFileName);
    const hasT2 = /^t2\d/.test(existingFileName) && !existingFileName.startsWith('t200');
    const hasT3 = /^t3\d/.test(existingFileName);
    
    // 检查是否存在同类型名册
    switch (registryType) {
      case "01":
        if (hasType01) {
          throw new HTTPException(400, { 
            message: "DUPLICATE_REGISTRY_TYPE:已存在01类型名册，不能重复上传"
          });
        }
        // 检查是否已同时存在01和05名册
        if (hasType01 && hasType05) {
          throw new HTTPException(400, { 
            message: "COMPLETE_REGISTRY:已存在01和05类型名册的合并数据，不能继续上传"
          });
        }
        break;
      case "05":
        if (hasType05) {
          throw new HTTPException(400, { 
            message: "DUPLICATE_REGISTRY_TYPE:已存在05类型名册，不能重复上传"
          });
        }
        // 检查是否已同时存在01和05名册
        if (hasType01 && hasType05) {
          throw new HTTPException(400, { 
            message: "COMPLETE_REGISTRY:已存在01和05类型名册的合并数据，不能继续上传"
          });
        }
        break;
      case "t1":
        if (hasT1) {
          throw new HTTPException(400, { 
            message: "DUPLICATE_REGISTRY_TYPE:已存在t1类型名册，不能重复上传"
          });
        }
        // 检查是否已同时存在t1、t2和t3名册
        if (hasT1 && hasT2 && hasT3) {
          throw new HTTPException(400, { 
            message: "COMPLETE_REGISTRY:已存在t1、t2和t3类型名册的合并数据，不能继续上传"
          });
        }
        break;
      case "t2":
        if (hasT2) {
          throw new HTTPException(400, { 
            message: "DUPLICATE_REGISTRY_TYPE:已存在t2类型名册，不能重复上传"
          });
        }
        // 检查是否已同时存在t1、t2和t3名册
        if (hasT1 && hasT2 && hasT3) {
          throw new HTTPException(400, { 
            message: "COMPLETE_REGISTRY:已存在t1、t2和t3类型名册的合并数据，不能继续上传"
          });
        }
        break;
      case "t3": {
        // 2025-06-13 14:02:21 hayden 修改：支持T3全量名册替换T3前200版本
        // 检查当前上传的是否为T3全量名册
        const { isT3FullRegistry } = await import("../lib/utils");
        const isCurrentFullRegistry = isT3FullRegistry(data.fileName);

        // 如果当前上传的是T3全量名册，允许替换现有的T3前200版本
        if (hasT3 && !isCurrentFullRegistry) {
          throw new HTTPException(400, {
            message: "DUPLICATE_REGISTRY_TYPE:已存在t3类型名册，不能重复上传"
          });
        }

        // 如果当前上传的是T3全量名册且存在T3前200版本，允许替换（不抛出错误）
        // 具体的替换逻辑在upload-t3.ts中处理

        // 检查是否已同时存在t1、t2和t3名册
        if (hasT1 && hasT2 && hasT3) {
          throw new HTTPException(400, {
            message: "COMPLETE_REGISTRY:已存在t1、t2和t3类型名册的合并数据，不能继续上传"
          });
        }
        break;
      }
    }
  }

  return { organization, existingRegistry };
}

/**
 * 处理组织与公司的绑定关系
 *
 * 功能：
 * 1. 检查是否已绑定公司代码
 * 2. 首次绑定时更新组织元数据
 * 3. 如果已绑定但公司名称为空且当前有公司名称，则更新公司名称
 *
 * 更新记录:
 * - 2025-07-02 11:07:59: hayden 修改 - 修复05名册先上传导致公司简称为空的问题，当已绑定组织的公司名称为空且当前名册有公司名称时进行更新
 *
 * @param tx Prisma事务客户端
 * @param organizationId 组织ID
 * @param metadata 组织元数据
 * @param companyCode 公司代码
 * @param companyName 公司名称
 */
export async function handleCompanyBinding(
  tx: Omit<PrismaClient, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">,
  organizationId: string,
  metadata: Record<string, any>,
  companyCode: string,
  companyName: string
) {
  // 检查是否已绑定公司代码
  if (
    metadata.boundCompanyCode &&
    metadata.boundCompanyCode !== companyCode
  ) {
    throw new HTTPException(400, {
      message: `该组织已绑定公司代码 ${metadata.boundCompanyCode}，不能上传其他公司的股东数据`,
    });
  }

  // 首次绑定公司代码到组织
  if (!metadata.boundCompanyCode) {
    metadata.boundCompanyCode = companyCode;
    metadata.boundCompanyName = companyName || "";
    metadata.boundAt = new Date().toISOString();

    // 更新组织的metadata字段
    await tx.organization.update({
      where: { id: organizationId },
      data: { metadata: JSON.stringify(metadata) },
    });
  } else {
    // 2025-07-02 11:07:59: hayden 修改 - 如果已绑定但公司名称为空且当前有公司名称，则更新公司名称
    // 这种情况通常发生在先上传05名册（无公司简称）后上传01名册（有公司简称）的场景
    if ((!metadata.boundCompanyName || metadata.boundCompanyName === "") && companyName && companyName.trim() !== "") {
      metadata.boundCompanyName = companyName;

      // 更新组织的metadata字段
      await tx.organization.update({
        where: { id: organizationId },
        data: { metadata: JSON.stringify(metadata) },
      });
    }
  }
}

/**
 * 处理名册记录创建或更新
 * 
 * 功能：
 * 1. 如果存在同日期名册，合并文件名
 * 2. 如果不存在，创建新名册和公司信息
 * 
 * @param tx Prisma事务客户端
 * @param data 请求数据
 * @param existingRegistry 现有名册记录
 * @param userId 用户ID
 * @returns 名册记录和是否为新创建的标志
 */
export async function handleRegistryRecord(
  tx: Omit<PrismaClient, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">,
  data: UploadRegistryRequest,
  existingRegistry: { id: string; fileName: string } | null,
  userId: string
) {
  let registry: any;
  let isNewRegistry = false;

  // 名册记录处理
  if (existingRegistry) {
    // 更新现有名册，合并文件名
    const newFileName = `${existingRegistry.fileName}\n${data.fileName}`;

    registry = await tx.shareholderRegistry.update({
      where: { id: existingRegistry.id },
      data: { fileName: newFileName },
    });
  } else {
    // 创建新名册
    isNewRegistry = true;

    registry = await tx.shareholderRegistry.create({
      data: {
        fileName: data.fileName,
        recordCount: data.recordCount,
        registerDate: new Date(data.registerDate),
        companyCode: data.companyCode,
        organizationId: data.organizationId,
        userId: userId,
        // 内联创建公司信息
        companyInfo: {
          create: {
            organizationId: data.organizationId,
            companyCode: data.companyCode,
            companyName: data.companyInfo.companyName || "",
            totalShares: data.companyInfo.totalShares,
            totalShareholders: data.companyInfo.totalShareholders,
            totalInstitutions: data.companyInfo.totalInstitutions,
            largeSharesCount: data.companyInfo.largeSharesCount,
            institutionShares: data.companyInfo.institutionShares,
            largeShareholdersCount: data.companyInfo.largeShareholdersCount,
            // 新增信用股东统计字段 - 2025-06-13 12:02:02 hayden 添加
            marginAccounts: data.companyInfo.marginAccounts || 0,
            marginShares: data.companyInfo.marginShares || "0",
            registerDate: new Date(data.registerDate),
          },
        },
      },
      include: {
        companyInfo: true,
      },
    });
  }

  return { registry, isNewRegistry };
}

/**
 * 处理股东数据
 * 
 * 功能：
 * 1. 查询现有股东记录
 * 2. 构建高效查找映射
 * 3. 批量处理股东数据
 * 4. 批量创建和更新股东记录
 * 
 * 更新记录:
 * - 2025-06-04 17:10:28: 使用配置文件中的固定批量大小，而非动态计算
 * 
 * @param tx Prisma事务客户端
 * @param data 请求数据
 * @param registry 名册记录
 * @param registryType 名册类型
 * @param processUpdateFn 处理更新的回调函数
 * @param processCreateFn 处理创建的回调函数
 */
export async function processShareholders(
  tx: Omit<PrismaClient, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">,
  data: UploadRegistryRequest,
  registry: any,
  registryType: RegistryType,
  processUpdateFn?: (shareholder: any, existingShareholder: any) => Record<string, unknown> | null,
  processCreateFn?: (shareholder: any) => Record<string, unknown>
) {
  // 股东数据批量处理
  // 过滤掉undefined值，并确保所有值都是字符串类型
  const shareholderIds = data.shareholders
    .map((s) => s.shareholderId)
    .filter((id): id is string => id !== undefined && id !== null)
    .map(id => String(id));
  
  const unifiedAccountNumbers = data.shareholders
    .map((s) => s.unifiedAccountNumber)
    .filter((num): num is string => num !== undefined && num !== null)
    .map(num => String(num));

  // 构建查询条件
  const whereCondition: any = {
    registryId: registry.id,
    organizationId: data.organizationId,
    registerDate: new Date(data.registerDate),
  };

  // 只有当数组不为空时才添加OR条件
  if (shareholderIds.length > 0 || unifiedAccountNumbers.length > 0) {
    whereCondition.OR = [];
    
    if (shareholderIds.length > 0) {
      whereCondition.OR.push({ shareholderId: { in: shareholderIds } });
    }
    
    if (unifiedAccountNumbers.length > 0) {
      whereCondition.OR.push({ unifiedAccountNumber: { in: unifiedAccountNumbers } });
    }
  }

  // 批量查询现有股东记录
  const existingShareholders = await tx.shareholder.findMany({
    where: whereCondition,
  });

  // 构建高效查找映射 - 使用复合键提高查找效率
  const shareholderMap = new Map();
  for (const shareholder of existingShareholders) {
    // 使用shareholderId和unifiedAccountNumber组合作为查找键
    const lookupKey = `${shareholder.unifiedAccountNumber}_${shareholder.shareholderId}`;
    shareholderMap.set(lookupKey, shareholder);
  }

  // 准备批量创建和更新的数据集合
  const shareholdersToCreate: Array<Record<string, unknown>> = [];
  const shareholdersToUpdate: Array<Record<string, unknown>> = [];

  // 股东记录处理循环
  for (const shareholder of data.shareholders) {
    const lookupKey = `${shareholder.unifiedAccountNumber}_${shareholder.shareholderId}`;
    const existingShareholder = shareholderMap.get(lookupKey);

    if (existingShareholder) {
      // 使用自定义处理更新逻辑
      if (processUpdateFn) {
        const updateData = processUpdateFn(shareholder, existingShareholder);
        if (updateData) {
          shareholdersToUpdate.push(updateData);
        }
      }
    } else {
      // 股东不存在，执行创建逻辑
      if (processCreateFn) {
        // 使用自定义处理创建逻辑
        const createData = processCreateFn(shareholder);
        shareholdersToCreate.push(createData);
      } else {
        // 默认创建逻辑
        const baseCreateData = {
          registryId: registry.id,
          organizationId: data.organizationId,
          shareholderId: shareholder.shareholderId,
          unifiedAccountNumber: shareholder.unifiedAccountNumber,
          securitiesAccountName: shareholder.securitiesAccountName,
          shareholderCategory: shareholder.shareholderCategory,
          numberOfShares: shareholder.numberOfShares || "0",
          lockedUpShares: shareholder.lockedUpShares || "0",
          shareholdingRatio: shareholder.shareholdingRatio || "0",
          frozenShares: shareholder.frozenShares || "0",
          cashAccount: shareholder.cashAccount,
          sharesInCashAccount: shareholder.sharesInCashAccount,
          marginAccount: shareholder.marginAccount,
          sharesInMarginAccount: shareholder.sharesInMarginAccount,
          contactAddress: shareholder.contactAddress,
          contactNumber: shareholder.contactNumber,
          zipCode: shareholder.zipCode,
          remarks: shareholder.remarks,

          // 新增股东类型字段 - 2025-06-25 11:44:05 hayden 添加
          shareholderType: shareholder.shareholderType,

          registerDate: new Date(data.registerDate),
        };
        
        // 根据名册类型添加特有字段
        if (registryType === "t1") {
          Object.assign(baseCreateData, {
            shareTradingCategory: shareholder.shareTradingCategory,
            rightsCategory: shareholder.rightsCategory,
            investorName: shareholder.investorName,
            shareholderName: shareholder.shareholderName,
            certificateType: shareholder.certificateType,
            shareholderNature: shareholder.shareholderNature,
            accountStatus: shareholder.accountStatus,
          });
        } else if (registryType === "t2" || registryType === "t3") {
          Object.assign(baseCreateData, {
            investorName: shareholder.investorName,
            shareholderName: shareholder.shareholderName,
            certificateType: shareholder.certificateType,
            shareholderNature: shareholder.shareholderNature,
            accountStatus: shareholder.accountStatus,
          });
        } else if (registryType === "05") {
          Object.assign(baseCreateData, {
            marginCollateralAccountNumber: shareholder.marginCollateralAccountNumber,
            marginCollateralAccountName: shareholder.marginCollateralAccountName,
            natureOfShares: shareholder.natureOfShares,
          });
        }
        
        shareholdersToCreate.push(baseCreateData);
      }
    }
  }

  // 批量创建股东记录 - 使用配置文件中的固定批次大小
  if (shareholdersToCreate.length > 0) {
    // 使用配置文件中的批次大小
    const batchSize = ShareholderRegistryConfig.batchSize;
    
    // 分批执行创建操作
    for (let i = 0; i < shareholdersToCreate.length; i += batchSize) {
      const batch = shareholdersToCreate.slice(i, i + batchSize);
      await tx.shareholder.createMany({
        data: batch as any[],
        skipDuplicates: true,
      });
    }
  }

  // 批量更新股东记录 - 使用配置文件中的固定并发限制
  if (shareholdersToUpdate.length > 0) {
    // 使用配置文件中的并发限制
    const concurrencyLimit = ShareholderRegistryConfig.concurrencyLimit;

    // 分批并行处理更新
    for (let i = 0; i < shareholdersToUpdate.length; i += concurrencyLimit) {
      const updateBatch = shareholdersToUpdate.slice(i, i + concurrencyLimit);

      // 并行执行当前批次的更新操作
      await Promise.all(
        updateBatch.map((updateData) => {
          const { id, shareholderId, ...data } = updateData;
          return tx.shareholder.update({
            where: {
              shareholderId_id: {
                shareholderId: shareholderId as string,
                id: id as string,
              },
            },
            data,
          });
        })
      );
    }
  }

  return { shareholdersToCreate, shareholdersToUpdate };
}

/**
 * 更新名册记录数
 * 
 * 功能：
 * 1. 查询实际股东数量
 * 2. 更新名册记录数
 * 3. 更新公司信息 - 采用"先到先得"原则，避免有价值的数据被覆盖
 * 
 * 更新记录:
 * - 2025-06-04 15:09:18: 修改公司信息更新逻辑，采用"先到先得"原则，避免T3名册覆盖T2名册的机构总数和机构总持股
 *
 * @param tx Prisma事务客户端
 * @param registry 名册记录
 * @param data 请求数据
 */
export async function updateRecordCount(
  tx: Omit<PrismaClient, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">,
  registry: any,
  data: UploadRegistryRequest
) {
  // 查询当前实际的股东记录数量
  const actualShareholderCount = await tx.shareholder.count({
    where: {
      registryId: registry.id,
      organizationId: data.organizationId,
      registerDate: new Date(data.registerDate),
    },
  });

  // 更新名册记录数为实际合并后的股东数量
  await tx.shareholderRegistry.update({
    where: { id: registry.id },
    data: { recordCount: actualShareholderCount },
  });

  // 先查询现有的公司信息，包含所有字段
  let existingCompanyInfo = await tx.companyInfo.findFirst({
    where: {
      registryId: registry.id,
      organizationId: data.organizationId,
    },
    select: {
      id: true,
      registryId: true,
      organizationId: true,
      companyCode: true,
      companyName: true,
      totalShares: true,
      totalShareholders: true,
      totalInstitutions: true,
      largeSharesCount: true,
      institutionShares: true,
      largeShareholdersCount: true,
      marginAccounts: true,
      marginShares: true,
      registerDate: true,
      uploadedAt: true,
    },
  });
  // 2025-06-19 16:05:47 hayden 修改：修复合并处理器bug，避免重复创建CompanyInfo记录
  // 如果找不到公司信息，检查是否已经在名册创建时内联创建了CompanyInfo
  if (!existingCompanyInfo) {
    // 再次查询，确保没有遗漏已创建的CompanyInfo记录
    const reCheckCompanyInfo = await tx.companyInfo.findFirst({
      where: {
        registryId: registry.id,
        organizationId: data.organizationId,
        companyCode: data.companyCode,
      },
    });

    // 如果确实没有找到，才创建新的CompanyInfo记录
    if (!reCheckCompanyInfo) {
      await tx.companyInfo.create({
        data: {
          registryId: registry.id,
          organizationId: data.organizationId,
          companyCode: data.companyCode,
          companyName: data.companyInfo.companyName || "",
          totalShares: data.companyInfo.totalShares,
          totalShareholders: data.companyInfo.totalShareholders,
          totalInstitutions: data.companyInfo.totalInstitutions,
          largeSharesCount: data.companyInfo.largeSharesCount,
          institutionShares: data.companyInfo.institutionShares,
          largeShareholdersCount: data.companyInfo.largeShareholdersCount,
          // 新增信用股东统计字段 - 2025-06-13 12:02:02 hayden 添加
          marginAccounts: data.companyInfo.marginAccounts || 0,
          marginShares: data.companyInfo.marginShares || "0",
          registerDate: new Date(data.registerDate),
        },
      });
      return;
    }

    // 如果找到了，将其设为existingCompanyInfo继续后续更新逻辑
    // 修复：移除不必要的else子句 - 2025-06-19 16:45:18 hayden 修复Biome lint错误
    existingCompanyInfo = reCheckCompanyInfo;
  }

  // 准备更新数据，采用"先到先得"原则
  const updateData: Record<string, unknown> = {};

  // 公司名称 - 如果现有名称为空且新数据有值
  if ((!existingCompanyInfo.companyName || existingCompanyInfo.companyName === "") && data.companyInfo.companyName) {
    updateData.companyName = data.companyInfo.companyName;
  }

  // 总股本 - 如果现有总股本为0且新数据有值
  if (existingCompanyInfo.totalShares.equals(0) && data.companyInfo.totalShares) {
    updateData.totalShares = data.companyInfo.totalShares;
  }

  // 总户数 - 如果现有总户数为0且新数据有值
  if (existingCompanyInfo.totalShareholders === 0 && data.companyInfo.totalShareholders > 0) {
    updateData.totalShareholders = data.companyInfo.totalShareholders;
  }

  // 机构总数 - 如果现有机构总数为0且新数据有值
  if (existingCompanyInfo.totalInstitutions === 0 && data.companyInfo.totalInstitutions > 0) {
    updateData.totalInstitutions = data.companyInfo.totalInstitutions;
  }

  // 大股东数量 - 如果现有大股东数量为0且新数据有值
  if (existingCompanyInfo.largeShareholdersCount === 0 && data.companyInfo.largeShareholdersCount > 0) {
    updateData.largeShareholdersCount = data.companyInfo.largeShareholdersCount;
  }

  // 大股东持股数 - 如果现有大股东持股数为0且新数据有值
  if (existingCompanyInfo.largeSharesCount.equals(0) && data.companyInfo.largeSharesCount) {
    updateData.largeSharesCount = data.companyInfo.largeSharesCount;
  }

  // 机构持股数 - 如果现有机构持股数为0且新数据有值
  if (existingCompanyInfo.institutionShares.equals(0) && data.companyInfo.institutionShares) {
    updateData.institutionShares = data.companyInfo.institutionShares;
  }

  // 新增信用股东统计字段更新逻辑 - 2025-06-13 12:02:02 hayden 添加
  // 信用总户数 - 如果现有信用总户数为0且新数据有值
  if ((existingCompanyInfo.marginAccounts === null || existingCompanyInfo.marginAccounts === 0) &&
      data.companyInfo.marginAccounts !== undefined && data.companyInfo.marginAccounts > 0) {
    updateData.marginAccounts = data.companyInfo.marginAccounts;
  }

  // 信用总持股 - 如果现有信用总持股为0且新数据有值
  if ((existingCompanyInfo.marginShares === null || existingCompanyInfo.marginShares.equals(0)) &&
      data.companyInfo.marginShares) {
    updateData.marginShares = data.companyInfo.marginShares;
  }

  // 只有当有字段需要更新时才执行更新操作
  if (Object.keys(updateData).length > 0) {
    await tx.companyInfo.updateMany({
      where: {
        registryId: registry.id,
        organizationId: data.organizationId,
      },
      data: updateData,
    });
  }
} 