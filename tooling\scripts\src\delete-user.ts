import { db } from "@repo/database";
import { logger } from "@repo/logs";

async function main() {
	logger.info("🗑️  Delete User Script - 删除用户脚本");
	logger.warn("⚠️  警告：此操作将永久删除用户及其所有相关数据，无法恢复！");

	// 获取用户标识 (支持邮箱或ID)
	const userIdentifier = await logger.prompt("请输入要删除的用户邮箱或ID:", {
		required: true,
		placeholder: "<EMAIL> 或 user_id_123",
		type: "text",
	});

	// 查找用户
	logger.info("🔍 正在查找用户...");
	
	const user = await db.user.findFirst({
		where: {
			OR: [
				{ email: userIdentifier },
				{ id: userIdentifier },
			],
		},
		include: {
			accounts: true,
			sessions: true,
			passkeys: true,
			memberships: {
				include: {
					organization: {
						select: {
							id: true,
							name: true,
						},
					},
				},
			},
			invitations: true,
			purchases: true,
			aiChats: true,
			shareholderRegistries: true,
		},
	});

	if (!user) {
		logger.error("❌ 未找到指定的用户！");
		return;
	}

	// 显示用户信息
	logger.info("📋 找到用户信息:");
	logger.info(`   ID: ${user.id}`);
	logger.info(`   姓名: ${user.name}`);
	logger.info(`   邮箱: ${user.email}`);
	logger.info(`   角色: ${user.role || "user"}`);
	logger.info(`   创建时间: ${user.createdAt.toLocaleString()}`);
	logger.info(`   邮箱验证: ${user.emailVerified ? "已验证" : "未验证"}`);

	// 显示关联数据统计
	logger.info("\n📊 关联数据统计:");
	logger.info(`   认证账户: ${user.accounts.length} 个`);
	logger.info(`   活跃会话: ${user.sessions.length} 个`);
	logger.info(`   WebAuthn密钥: ${user.passkeys.length} 个`);
	logger.info(`   组织成员身份: ${user.memberships.length} 个`);
	logger.info(`   发出的邀请: ${user.invitations.length} 个`);
	logger.info(`   购买记录: ${user.purchases.length} 个`);
	logger.info(`   AI聊天记录: ${user.aiChats.length} 个`);
	logger.info(`   上传的股东名册: ${user.shareholderRegistries.length} 个`);

	// 检查是否为组织owner
	const ownerMemberships = user.memberships.filter(m => m.role === "owner");
	if (ownerMemberships.length > 0) {
		logger.warn("\n⚠️  警告：该用户是以下组织的所有者:");
		ownerMemberships.forEach(membership => {
			logger.warn(`   - ${membership.organization.name} (${membership.organization.id})`);
		});
		logger.warn("删除该用户可能会影响这些组织的正常运行！");
	}

	// 检查是否为管理员
	if (user.role === "admin") {
		logger.warn("\n⚠️  警告：该用户具有管理员权限！");
	}

	// 最终确认
	const confirmDelete = await logger.prompt(
		"\n❓ 确认删除此用户及其所有相关数据吗？此操作不可逆！",
		{
			required: true,
			type: "confirm",
			default: false,
		}
	);

	if (!confirmDelete) {
		logger.info("✅ 操作已取消，用户未被删除。");
		return;
	}

	// 二次确认 (对于管理员或组织owner)
	if (user.role === "admin" || ownerMemberships.length > 0) {
		const doubleConfirm = await logger.prompt(
			"🚨 这是一个高风险操作！请再次确认删除:",
			{
				required: true,
				type: "confirm",
				default: false,
			}
		);

		if (!doubleConfirm) {
			logger.info("✅ 操作已取消，用户未被删除。");
			return;
		}
	}

	// 执行删除操作
	logger.info("🔄 开始删除用户数据...");

	try {
		await db.$transaction(async (tx) => {
			// 1. 删除认证相关数据
			logger.info("   🔐 删除认证数据...");
			await tx.account.deleteMany({
				where: { userId: user.id },
			});

			await tx.session.deleteMany({
				where: { userId: user.id },
			});

			// await tx.passkey.deleteMany({
			// 	where: { userId: user.id },
			// });

			// 2. 删除组织相关数据
			logger.info("   🏢 删除组织关联数据...");
			await tx.member.deleteMany({
				where: { userId: user.id },
			});

			// await tx.invitation.deleteMany({
			// 	where: { inviterId: user.id },
			// });

			// // 3. 删除业务数据
			// logger.info("   💼 删除业务数据...");
			// await tx.purchase.deleteMany({
			// 	where: { userId: user.id },
			// });

			// await tx.aiChat.deleteMany({
			// 	where: { userId: user.id },
			// });

			// 4. 处理股东名册数据 - 标记删除用户
			if (user.shareholderRegistries.length > 0) {
				logger.info("   📊 处理股东名册数据...");

				// 创建删除标识符，使用用户名而不是用户ID
				// const deletedUserMarker = `DELETED_${user.username || user.email.split('@')[0] || user.id}`;

				// 更新所有相关的股东名册记录，将userId替换为删除标识符
				await tx.shareholderRegistry.updateMany({
					where: { userId: user.id },
					data: { userId: "Exited_User" },
				});

				logger.info(
					`   📊 已标记 ${user.shareholderRegistries.length} 个股东名册的用户为Exited_User`,
				);
			}

			// 5. 清理验证记录 (通过邮箱关联)
			logger.info("   📧 清理验证记录...");
			await tx.verification.deleteMany({
				where: { identifier: user.email },
			});

			// 6. 最后删除用户记录
			logger.info("   👤 删除用户主记录...");
			await tx.user.delete({
				where: { id: user.id },
			});
		});

		logger.success("✅ 用户删除成功！");
		logger.info("📋 删除操作摘要:");
		logger.info(`   - 用户: ${user.name} (${user.email})`);
		logger.info(`   - 认证账户: ${user.accounts.length} 个`);
		logger.info(`   - 会话记录: ${user.sessions.length} 个`);
		logger.info(`   - 组织成员身份: ${user.memberships.length} 个`);
		// logger.info(`   - 业务数据: ${user.purchases.length + user.aiChats.length} 条`);
		if (user.shareholderRegistries.length > 0) {
			logger.info(
				`   - 股东名册: ${user.shareholderRegistries.length} 个 (已标记为Exited_User)`,
			);
		}

	} catch (error) {
		logger.error("❌ 删除用户时发生错误:");
		logger.error(error);
		logger.error("用户数据未被删除，请检查错误信息后重试。");
	}
}

main().catch((error) => {
	logger.error("脚本执行失败:");
	logger.error(error);
	process.exit(1);
});
