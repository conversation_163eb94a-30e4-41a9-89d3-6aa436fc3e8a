import { operatorId, meetingApiClient } from "./config";

// 定义类型
export interface InviteActivateResponse {
  success: boolean;
  data: {
    invite_activate_list: {
      userid: string;
      invite_activate_url: string;
    }[];
  };
}

// 获取用户激活链接
export const getInviteActivateLinks = {
  method: "POST",
  path: "/meetings/invite-activate",
  handler: async (useridList: string[]) => {
    try {
      // console.log('获取用户激活链接参数:', { useridList });
      
      const response = await meetingApiClient.post("/v1/users/invite-activate", {
        userid_list: useridList,
        operator_id: operatorId,
        operator_id_type: "1"
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.log("Error getting invite activate links:", error);
      throw error;
    }
  },
};
