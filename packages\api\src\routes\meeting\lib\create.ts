import { operatorId, meetingApiClient } from "./config";

// 创建会议参数接口
export interface RecurringRule {
  recurring_type: number; // 重复类型：0每天，1每周一至周五，2每周，3每两周，4每月，5自定义
  until_type: number; // 结束重复类型：0按日期结束，1按次数结束
  until_date?: number; // 结束日期时间戳
  until_count?: number; // 限定会议次数
}

export interface CreateMeetingParams {
  userid?: string; // 用户ID，可选，如果不提供则使用默认operatorId
  subject: string; // 会议主题
  type: number; // 会议类型，0为预约会议，1为快速会议，默认为0
  start_time: string; // 会议开始时间的日期和时间
  end_time: string; // 会议结束时间的日期和时间
  password?: string; // 会议密码，可选
  hosts?: Array<any>; // 主持人列表，可选
  settings?: { // 会议设置参数，可选
    mute_enable_type_join?: number; // 成员入会时静音选项，0:关闭 1:开启 2:超过6人后自动开启
    mute_enable_join?: boolean; // 入会时静音，默认true
    allow_unmute_self?: boolean; // 允许参会者取消静音，默认true
    play_ivr_on_leave?: boolean; // 参会者离开时播放提示音，暂不支持
    play_ivr_on_join?: boolean; // 有新的与会者加入时播放提示音，暂不支持
    allow_in_before_host?: boolean; // 是否允许成员在主持人进会前加入会议，默认true
    auto_in_waiting_room?: boolean; // 是否开启等候室，默认false
    allow_screen_shared_watermark?: boolean; // 是否开启屏幕共享水印，默认false
    water_mark_type?: number; // 水印样式，0:单排 1:多排
    only_enterprise_user_allowed?: boolean; // 是否仅企业内部成员可入会，默认false
    only_user_join_type?: number; // 成员入会限制，1:所有成员 2:仅受邀成员 3:仅企业内部成员
    auto_record_type: 'cloud'; // 自动会议录制类型
    participant_join_auto_record?: boolean; // 当有参会成员入会时立即开启云录制，默认false
    enable_host_pause_auto_record?: boolean; // 允许主持人暂停或者停止云录制，默认true
    allow_multi_device?: boolean; // 是否允许成员多端入会
    change_nickname?: number; // 是否允许用户自己改名，1:允许 2:不允许
    auto_asr?: boolean; // 入会后自动开启文字转写，默认false
    open_asr_view?: number; // 会中开启文字转写功能后是否自动显示转写侧边栏，0:打开 1:不打开
  };
  meeting_type?: number; // 会议类型，可选
  recurring_rule?: any; // 会议重复规则，可选
  enable_live?: boolean; // 是否开启直播，可选
  live_config?: any; // 直播配置，可选
  enable_doc_upload_permission?: boolean; // 是否允许成员上传文档，可选
  media_set_type?: number; // 媒体设置类型，可选
  enable_interpreter?: boolean; // 是否开启翻译，可选
  enable_enroll?: boolean; // 是否开启报名，可选
  enable_host_key?: boolean; // 是否开启主持人密钥，可选
  host_key?: string; // 主持人密钥，可选
  sync_to_wework?: boolean; // 是否同步到企业微信，可选
  time_zone?: string; // 时区，可选
  location?: string; // 会议地点，可选
  allow_enterprise_intranet_only?: boolean; // 是否仅允许企业内网访问，可选
}

/* 修改块开始: 支持传入用户ID创建会议
 * 修改范围: createMeeting handler函数
 * 对应需求: 使用真实用户ID而非固定operatorId创建会议
 * 恢复方法: 删除userid参数处理，恢复使用固定operatorId
 */
/**
 * 创建会议API
 */
export const createMeeting = {
  method: "POST",
  path: "/meetings/create",
  handler: async (params: CreateMeetingParams) => {
    try {
      // 使用传入的用户ID，如果没有则使用默认的operatorId
      const targetUserId = params.userid || operatorId;
      
      // 构建请求体
      const requestBody = {
        userid: targetUserId,
        instanceid: 1,
        subject: params.subject,
        type: params.type,
        start_time: params.start_time,
        end_time: params.end_time,
        // 修改 settings 的处理方式
        settings: {
          ...params.settings,
          // 确保水印相关的设置被正确处理
          allow_screen_shared_watermark: params.settings?.allow_screen_shared_watermark ?? false,
          water_mark_type: params.settings?.allow_screen_shared_watermark ? (params.settings?.water_mark_type ?? 0) : undefined,
        },
        // 可选参数，如果存在则添加到请求体
        ...(params.password && { password: params.password }),
        ...(params.hosts && { hosts: params.hosts }),
        ...(params.meeting_type !== undefined && { meeting_type: params.meeting_type }),
        ...(params.recurring_rule && { recurring_rule: params.recurring_rule }),
        ...(params.enable_live !== undefined && { enable_live: params.enable_live }),
        ...(params.live_config && { live_config: params.live_config }),
        ...(params.enable_doc_upload_permission !== undefined && { enable_doc_upload_permission: params.enable_doc_upload_permission }),
        ...(params.media_set_type !== undefined && { media_set_type: params.media_set_type }),
        ...(params.enable_interpreter !== undefined && { enable_interpreter: params.enable_interpreter }),
        ...(params.enable_enroll !== undefined && { enable_enroll: params.enable_enroll }),
        ...(params.enable_host_key !== undefined && { enable_host_key: params.enable_host_key }),
        ...(params.host_key && { host_key: params.host_key }),
        ...(params.sync_to_wework !== undefined && { sync_to_wework: params.sync_to_wework }),
        ...(params.time_zone && { time_zone: params.time_zone }),
        ...(params.location && { location: params.location }),
        ...(params.allow_enterprise_intranet_only !== undefined && { allow_enterprise_intranet_only: params.allow_enterprise_intranet_only }),
      };

      // console.log("Meeting API request:", JSON.stringify(requestBody));
      const response = await meetingApiClient.post("/v1/meetings", requestBody);

      // console.log("Meeting created:", response.data);
      return response.data;
    } catch (error: any) {
      console.log("Error creating meeting:", error);
      throw error;
    }
  },
};
