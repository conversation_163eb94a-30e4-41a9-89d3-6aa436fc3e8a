# 股东名册数据库设计文档

## 1. 概述

本文档描述了股东名册系统的数据库设计。系统包含三个主要表：

1. 股东名册表 (ShareholderRegistry)：记录上传的DBF文件信息
2. 公司基本信息表 (CompanyInfo)：存储公司的基本信息
3. 股东信息表 (Shareholder)：存储每个股东的详细信息

这三个表相互关联，通过股东名册表作为入口进行数据访问。此外，股东名册表与已有的组织表(Organization)关联，使得可以通过组织ID查找相关的股东名册记录。

## 2. 数据表设计

### 2.1 股东名册表 (ShareholderRegistry)

记录上传的DBF文件信息，作为其他相关数据的入口。

| 字段名 | 类型 | 描述 | 说明 |
|--------|------|------|------|
| id | String | 主键 | @id @default(cuid()) |
| fileName | String | DBF文件名 | |
| recordCount | Int | 记录数量 | 上传的记录数量 |
| registerDate | DateTime | 报告日期 | 名册报告日期 |
| companyCode | String | 公司代码 | 从文件名提取的公司代码 |
| organizationId | String | 关联的组织ID | 外键，关联Organization表 |
| userId | String | 上传用户ID | 外键，关联User表 |
| uploadedAt | DateTime | 上传时间 | @default(now()) |

索引：
- companyCode 索引：提高按公司代码查询的性能
- organizationId 索引：提高按组织查询的性能
- registerDate 索引：提高按报告日期查询的性能

### 2.2 公司基本信息表 (CompanyInfo)

存储公司基本信息，从DBF文件的特殊记录（序号为0、-1、-2）中提取。

| 字段名 | 类型 | 描述 | 说明 |
|--------|------|------|------|
| id | String | 主键 | @id @default(cuid()) |
| registryId | String | 关联的股东名册ID | 外键，关联ShareholderRegistry表 |
| organizationId | String | 关联的组织ID | 外键，关联Organization表 |
| companyCode | String | 公司代码 | |
| companyName | String | 公司名称 | |
| totalShares | Decimal | 总股数 | @db.Decimal(17,2) |
| totalShareholders | Int | 总户数 | |
| totalInstitutions | Int | 机构总数 | |
| largeSharesCount | Decimal | 持有万份以上总份数 | @db.Decimal(17,2) |
| largeShareholdersCount | Int | 持有万份以上总户数 | |
| registerDate | DateTime | 报告期日期 | 与名册报告日期一致 |
| uploadedAt | DateTime | 上传时间 | @default(now()) |


索引：
- registryId 索引：提高按名册ID查询的性能
- companyCode 索引：提高按公司代码查询的性能
- organizationId 索引：提高按组织查询的性能
- registerDate 索引：提高按报告日期查询的性能

### 2.3 股东信息表 (Shareholder)

存储每个股东的详细信息，基于DBF文件中的记录。

| 字段名 | 类型 | 描述 | 说明 |
|--------|------|------|------|
| id | String | 主键之一 | @default(cuid()) |
| shareholderId | String | 主键之一（优先） | 证件号码，原ZJDM字段，作为优先主键 |
| registryId | String | 关联的股东名册ID | 外键，关联ShareholderRegistry表 |
| organizationId | String | 关联的组织ID | 外键，关联Organization表 |
| unifiedAccountNumber | String | 一码通账户号码 | 原YMTH字段 |
| securitiesAccountName | String | 证券账户名称 | 原ZQZHMC字段 |
| shareholderCategory | String | 持有人类别 | 原CYRLBMS字段 |
| numberOfShares | Decimal | 持股数量 | @db.Decimal(17,2)，原CGSL字段 |
| lockedUpShares | Decimal | 限售股数量 | @db.Decimal(17,2)，原XSGSL字段 |
| shareholdingRatio | Decimal | 持股比例 | @db.Decimal(6,2)，原CGBL字段 |
| frozenShares | Decimal | 冻结股数 | @db.Decimal(17,2)，原DJGS字段 |
| cashAccount | String? | 普通证券账户 | 原PTZQZH字段 |
| sharesInCashAccount | Decimal? | 普通账户持股数量 | @db.Decimal(17,2)，原PTZHCGSL字段 |
| marginAccount | String? | 信用证券账户 | 原XYZQZH字段 |
| sharesInMarginAccount | Decimal? | 信用账户持股数量 | @db.Decimal(17,2)，原XYZHCGSL字段 |
| contactAddress | String? | 通讯地址 | 原TXDZ字段 |
| contactNumber | String? | 电话号码 | 原DHHM字段 |
| zipCode | String? | 邮政编码 | 原YZBM字段 |
| relatedPartyIndicator | String? | 关联关系确认标识 | 原GLGXBS字段 |
| clientCategory | String? | 客户类别 | 原KHLB字段 |
| remarks | String? | 备注 | 原BZ字段 |
| registerDate | DateTime | 报告日期 | 与名册报告日期一致 |
| uploadedAt | DateTime | 上传时间 | @default(now()) |


索引：
- registryId 索引：提高按名册ID查询的性能
- organizationId 索引：提高按组织查询的性能
- unifiedAccountNumber 索引：提高按一码通账户查询的性能
- securitiesAccountName 索引：提高按证券账户名称查询的性能
- registerDate 索引：提高按报告日期查询的性能
- shareholderId 和 registerDate 组合索引：优化查询相同证件号码在不同报告期的记录

## 3. 关联关系

### 3.1 组织与各表的关联
- 股东名册表通过 `organizationId` 字段与 `Organization` 表关联
- 公司基本信息表通过 `organizationId` 字段与 `Organization` 表关联
- 股东信息表通过 `organizationId` 字段与 `Organization` 表关联
- 这允许通过组织ID直接查找相关的各类记录

### 3.2 股东名册与公司信息、股东信息的关联
- 公司基本信息表和股东信息表都通过 `registryId` 字段与股东名册表关联
- 当删除股东名册记录时，关联的公司基本信息和股东信息也会被级联删除

### 3.3 股东名册与用户的关联
- 股东名册表通过 `userId` 字段与 `User` 表关联
- 这允许跟踪谁上传了特定的股东名册

## 4. Prisma Schema 扩展

```prisma
// 股东名册表
model ShareholderRegistry {
  id             String         @id @default(cuid()) // 主键,自动生成
  fileName       String         // DBF文件名
  recordCount    Int            // 记录数量
  registerDate     DateTime       // 报告日期
  companyCode    String         // 公司代码
  organizationId String         // 关联的组织ID
  organization   Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  userId         String         // 上传用户ID
  user           User           @relation(fields: [userId], references: [id], onDelete: SetNull) // 关联用户 指定 onDelete 删除用户时不会影响股东名册
  companyInfo    CompanyInfo[]  // 关联的公司信息
  shareholders   Shareholder[]  // 关联的股东信息
  uploadedAt     DateTime       @default(now()) // 上传时间

  @@index([companyCode]) // 公司代码索引
  @@index([organizationId]) // 组织ID索引
  @@index([registerDate]) // 报告日期索引
  @@unique([organizationId, companyCode, registerDate])  // 防止同一组织同一日期重复导入同一公司代码的名册
  @@map("shareholder_registry") // 映射到数据库表"shareholder_registry"
}

// 公司基本信息表
model CompanyInfo {
  id                     String              @id @default(cuid()) // 主键,自动生成
  registryId             String              // 关联的股东名册ID
  registry               ShareholderRegistry @relation(fields: [registryId], references: [id], onDelete: Cascade) // 关联股东名册，级联删除
  organizationId         String              // 关联的组织ID
  organization           Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  companyCode            String              // 公司代码
  companyName            String              // 公司名称
  totalShares            Decimal             @db.Decimal(17, 2) // 总股数
  totalShareholders      Int                 // 总户数
  totalInstitutions      Int                 // 机构总数
  largeSharesCount       Decimal             @db.Decimal(17, 2) // 持有万份以上总份数
  largeShareholdersCount Int                 // 持有万份以上总户数
  registerDate             DateTime            // 报告期日期
  uploadedAt             DateTime            @default(now()) // 上传时间


  @@index([registryId]) // 名册ID索引
  @@index([companyCode]) // 公司代码索引
  @@index([organizationId]) // 组织ID索引
  @@index([registerDate]) // 报告日期索引
  @@map("company_info") // 映射到数据库表"company_info"
}

// 股东信息表
model Shareholder {
  id                    String              @default(cuid()) // 生成的ID
  shareholderId         String              // 证件号码，作为优先主键
  registryId            String              // 关联的股东名册ID
  registry              ShareholderRegistry @relation(fields: [registryId], references: [id], onDelete: Cascade) // 关联股东名册，级联删除
  organizationId        String              // 关联的组织ID
  organization          Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  unifiedAccountNumber  String              // 一码通账户号码
  securitiesAccountName String              // 证券账户名称
  shareholderCategory   String              // 持有人类别
  numberOfShares        Decimal             @db.Decimal(17, 2) // 持股数量
  lockedUpShares        Decimal             @db.Decimal(17, 2) // 限售股数量
  shareholdingRatio     Decimal             @db.Decimal(6, 2) // 持股比例
  frozenShares          Decimal             @db.Decimal(17, 2) // 冻结股数
  cashAccount           String?             // 普通证券账户
  sharesInCashAccount   Decimal?            @db.Decimal(17, 2) // 普通账户持股数量
  marginAccount         String?             // 信用证券账户
  sharesInMarginAccount Decimal?            @db.Decimal(17, 2) // 信用账户持股数量
  contactAddress        String?             // 通讯地址
  contactNumber         String?             // 电话号码
  zipCode               String?             // 邮政编码
  relatedPartyIndicator String?             // 关联关系确认标识
  clientCategory        String?             // 客户类别
  remarks               String?             // 备注
  registerDate            DateTime            // 报告日期
  uploadedAt            DateTime            @default(now()) // 上传时间

  @@id([shareholderId, id]) // 设置双主键，shareholderId优先
  @@index([registryId]) // 名册ID索引
  @@index([organizationId]) // 组织ID索引
  @@index([unifiedAccountNumber]) // 一码通账户索引
  @@index([securitiesAccountName]) // 证券账户名称索引
  @@index([registerDate]) // 报告日期索引
  @@index([shareholderId, registerDate]) // 组合索引：优化查询相同证件号码在不同报告期的记录
  @@map("shareholder") // 映射到数据库表"shareholder"
}
```

## 5. 修改现有模型

需要在现有的 Organization 模型中添加与股东名册的关联：

```prisma
model Organization {
  // ... 现有字段 ...
  shareholderRegistries ShareholderRegistry[] // 关联的股东名册
  companyInfos         CompanyInfo[]         // 关联的公司信息
  shareholders         Shareholder[]         // 关联的股东信息
}

model User {
  // ... 现有字段 ...
 shareholderRegistries ShareholderRegistry[] // 关联的股东名册
}
```

## 6. 数据库迁移和更新步骤

完成上述模型定义后，执行以下步骤更新数据库：

1. 修改 schema.prisma 文件，添加上述新模型和关系
2. 生成迁移文件：`pnpm --filter database migrate`  //已经有文件好像可以可以跳过第一步，不用执行
3. 推送变更到数据库：`pnpm --filter database push`
4. 重新生成 prisma 客户端：`pnpm --filter database generate` 

## 7. 关于组织删除后数据的状态
如果ShareholderRegistry模型中删除了某一期的一条记录，CompanyInfo和Shareholder模型中会发生什么？
根据当前的数据库设计，如果组织被删除（organizationId不存在了），这三个模型的数据状态如下：
所有相关数据都会被级联删除，因为所有三个模型都设置了与Organization的关系为onDelete: Cascade：
这意味着当删除一个组织时：
该组织下的所有股东名册记录会被删除
所有关联的公司信息记录会被删除
所有关联的股东信息记录会被删除
这是一种强约束关系，确保数据完整性，但也意味着组织删除是一个高风险操作，会导致大量关联数据丢失。

## 8. 关于名册记录删除的影响
如果ShareholderRegistry模型中删除了某一期的一条记录，CompanyInfo和Shareholder模型中会发生什么？
相关联的CompanyInfo和Shareholder记录也会被级联删除，因为它们都设置了与ShareholderRegistry的关系为onDelete: Cascade：
这意味着：
删除一条名册记录会导致所有关联的公司信息被删除
删除一条名册记录会导致所有关联的股东信息被删除
这种级联删除确保了数据的一致性，防止出现"孤儿记录"（没有对应名册的公司或股东记录）。

## 9. 测试数据库模型更新成功
```powershell
pnpm --filter database studio

```
