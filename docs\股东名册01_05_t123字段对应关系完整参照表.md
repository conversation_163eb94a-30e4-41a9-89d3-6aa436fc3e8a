# 股东名册01/05/t1/t2/t3字段对应关系完整参照表

**文档日期:** 2025-06-03
**版本:** 1.1
**更新日期:** 2025-06-03

## 1. 概述

本文档提供了深市01/05名册和沪市t1/t2/t3名册的字段对应关系完整参照表，用于支持前端适配后端上传API的实施。文档详细列出了各类名册的原始字段、对应的数据库字段映射以及字段说明，便于开发人员理解不同名册类型之间的数据结构差异和映射关系。

## 2. 字段对应关系完整参照表

下表详细列出了所有类型名册的字段及其对应的数据库字段映射关系：

| 序号 | 数据库字段名 | 数据类型 | 01名册字段 | 05名册字段 | t1名册字段 | t2名册字段 | t3名册字段 | 字段说明 | 字段共有性 | 合并处理策略 |
|------|--------------|----------|------------|------------|------------|------------|------------|----------|------------|--------------|
| 1 | unifiedAccountNumber | String | YMTH | YMTH | YMTZHHM | YMTZHHM | YMTZHHM | 一码通账户号码，关键的共同标识 | 所有名册共有 | 保留已有值 |
| 2 | securitiesAccountName | String | ZQZHMC | XYZHMC | CYRMC | CYRMC | CYRMC | 证券账户名称/持有人名称，01名册中可能带有#符号，需过滤 | 所有名册共有 | 保留已有值 |
| 3 | shareholderId | String | ZJDM | XYZHZJDM | ZJHM | ZJHM | ZJHM | 证件号码，作为主键标识 | 所有名册共有 | 保留已有值 |
| 4 | shareholderCategory | String | CYRLBMS | CYRLBMS | CYRLB | CYRLB | CYRLB | 持有人类别，深市用字符（如"境内自然人(03)"），沪市用四位数字字符标识（如"1000"） | 所有名册共有 | 保留已有值 |
| 5 | reportDate | DateTime | 前端从文件名文件内校验解析 | 前端从文件名文件内校验解析 | QYDJR | QYDJR | QYDJR | 报告期日期/权益登记日 | 所有名册共有 | 保留已有值 |
| 6 | numberOfShares | Decimal | CGSL | 无 | 无 | 无 | ZCYSL | 总持股数量 | 01和t3名册有 | 保留已有值，t1/t2/t3合并时叠加 |
| 7 | sharesInCashAccount | Decimal | PTZHCGSL | 无 | CYSL | CYSL | PTZQZHCYSL | 普通账户持股数量 | 除05名册外都有 | 保留已有值，t1/t2/t3合并时叠加 |
| 8 | sharesInMarginAccount | Decimal | XYZHCGSL | CGSL | 无 | 无 | XYCYSL | 信用账户持股数量，注意05名册中CGSL表示信用账户持股 | 01/05/t3名册有 | 保留已有值，t1/t2/t3合并时叠加 |
| 9 | lockedUpShares | Decimal | XSGSL | 无 | 无 | 无 | 无 | 限售股数量 | 仅01名册有 | 保留01名册值 |
| 10 | shareholdingRatio | Decimal | CGBL | 无 | 无 | 无 | CYBL | 持股比例(%) | 01和t3名册有 | 保留已有值 |
| 11 | frozenShares | Decimal | DJGS | DJGS | ZYDJZS | ZYDJZS | ZYDJZS | 冻结股数/质押冻结总数 | 所有名册共有 | 保留已有值 |
| 12 | contactAddress | String | TXDZ | TXDZ | TXDZ | TXDZ | TXDZ | 通讯地址 | 所有名册共有 | 保留已有值 |
| 13 | contactNumber | String | DHHM | 无 | LXDH | LXDH | LXDH | 联系电话 | 除05名册外都有 | 保留已有值 |
| 14 | zipCode | String | YZBM | 无 | YZBM | YZBM | YZBM | 邮政编码 | 除05名册外都有 | 保留已有值 |
| 15 | marginCollateralAccountNumber | String | 无 | HZZQZH | 无 | 无 | 无 | 汇总账户号码 | 仅05名册有 | 保留05名册值 |
| 16 | marginCollateralAccountName | String | 无 | HZZHMC | 无 | 无 | 无 | 汇总账户名称 | 仅05名册有 | 保留05名册值 |
| 17 | natureOfShares | String | 无 | GFXZ | ZQLB | 无 | 无 | 股份性质/证券类别，"00"表示无限售条件流通股 | 05和t1名册有 | 保留已有值 |
| 18 | relatedPartyIndicator | String | GLGXS | 无 | GLGXSFQR | GLGXSFQR | GLGXSFQR | 关联关系确认标识 | 除05名册外都有 | 保留已有值 |
| 19 | clientCategory | String | KHLB | 无 | 无 | 无 | 无 | 客户类别 | 仅01名册有 | 保留01名册值 |
| 20 | cashAccount | String | PTZQZH | 无 | ZQZHHM | ZQZHHM | PTZQZHHM | 普通证券账户 | 除05名册外都有 | 保留已有值 |
| 21 | marginAccount | String | XYZQZH | XYZQZH | 无 | 无 | XYZQZHHM | 信用证券账户 | 01/05/t3名册有 | 保留已有值 |
| 22 | remarks | String | BZ | 无 | BZ | BZ | BZ | 备注 | 除05名册外都有 | 保留已有值 |
| 23 | shareTradingCategory | String | 无 | 无 | LTLX | 无 | 无 | 流通类型 | 仅t1名册有 | 保留t1名册值 |
| 24 | rightsCategory | String | 无 | 无 | QYLB | 无 | 无 | 权益类别 | 仅t1名册有 | 保留t1名册值 |

## 3. 公司信息表字段映射

| 序号 | 数据库字段名 | 数据类型 | 描述 | 01/05名册对应 | t1名册对应 | t2名册对应 | t3名册对应 | 说明 |
|------|--------------|----------|------|--------------|------------|------------|------------|------|
| 1 | id | String | 主键 | @id @default(cuid()) | | | | |
| 2 | registryId | String | 关联的股东名册ID | 外键，关联ShareholderRegistry表 | | | | |
| 3 | organizationId | String | 关联的组织ID | 外键，关联Organization表 | | | | |
| 4 | companyCode | String | 公司代码 | 01：XH=0/-1/-2时YMTH | ZQDM | ZQDM | ZQDM | 证券代码 |
| 5 | companyName | String | 公司名称 | | ZQJC | ZQJC | ZQJC | 证券简称 |
| 6 | totalShares | Decimal | 总股数 | 01：XH=0，CGSL下面 | ZQZSL +（SJLX=802） | ZQZSL +（SJLX=802） | ZQZSL +（SJLX=802） | @db.Decimal(17,2) |
| 7 | totalShareholders | Int | 总户数 | 01：XH=0，DJGS下面 | ZHS +（SJLX=802） | ZHS +（SJLX=802） | CYRS +（SJLX=802） | 持有人数（已合并） |
| 8 | totalInstitutions | Int | 机构总数 | 01：XH=-1，DJGS下面 | JGHSHJ +（SJLX=802） | JGHSHJ +（SJLX=802） | | 机构户数合计 |
| 9 | institutionShares | Decimal | 机构总持股 | 01：XH=-1, CGSL下面 | JGCYHJ +（SJLX=802） | JGCYHJ +（SJLX=802） | | 机构持有合计 |
| 10 | largeSharesCount | Decimal | 持有万份以上总份数 | | | | | @db.Decimal(17,2) |
| 11 | largeShareholdersCount | Int | 持有万份以上总户数 | | | | | |
| 12 | reportDate | DateTime | 报告期日期 | 与名册报告日期一致 | | | | |
| 13 | uploadedAt | DateTime | 上传时间 | @default(now()) | | | | |

## 4. 字段共有性分析

### 4.1 所有名册共有字段

以下字段在所有类型名册中都存在，是核心共享字段：

1. unifiedAccountNumber (一码通账户号码)
2. securitiesAccountName (证券账户名称/持有人名称)
3. shareholderId (证件号码)
4. shareholderCategory (持有人类别)
5. reportDate (报告期日期)
6. contactAddress (通讯地址)
7. frozenShares (冻结股数/质押冻结总数)
8. companyCode (公司代码)

### 4.2 深市名册特有字段

以下字段仅在深市名册(01和/或05)中存在：

1. lockedUpShares (限售股数量) - 仅01名册有
2. clientCategory (客户类别) - 仅01名册有
3. marginCollateralAccountNumber (汇总账户号码) - 仅05名册有
4. marginCollateralAccountName (汇总账户名称) - 仅05名册有

### 4.3 沪市名册特有字段

以下字段仅在沪市名册(t1/t2/t3)中存在：

1. shareTradingCategory (流通类型) - 仅t1名册有
2. rightsCategory (权益类别) - 仅t1名册有



