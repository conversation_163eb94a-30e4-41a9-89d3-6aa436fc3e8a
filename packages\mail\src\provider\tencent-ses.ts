import nodemailer from "nodemailer";
import type { Send<PERSON>mailHandler } from "../../types";

export const send: SendEmailHandler = async ({ to, subject, text, html }) => {
    // 获取环境变量中的配置信息
    const mailUser = process.env.MAIL_USER as string; // 腾讯云企业邮箱账号
    const mailPass = process.env.MAIL_PASS as string; // 腾讯云邮箱SMTP授权码
    const mailHost = process.env.MAIL_HOST as string; // "gz-smtp.qcloudmail.com"
    const mailPort = Number.parseInt(process.env.MAIL_PORT as string, 10); // 465

    // 创建发送邮件的transporter
    const transporter = nodemailer.createTransport({
        host: mailHost,
        port: mailPort,
        secure: true, // 腾讯云邮件服务需要启用SSL
        auth: {
            user: mailUser,
            pass: mailPass,
        },
        // 增加调试信息，帮助排查问题
        logger: true,
        debug: process.env.NODE_ENV !== "production", // 非生产环境开启调试
    });

    try {
        // 发送邮件
        const result = await transporter.sendMail({
            from: mailUser, // 发件人必须是已验证的邮箱地址
            to, // 收件人
            subject, // 邮件主题
            text, // 纯文本内容
            html, // HTML内容
            // 增加腾讯云SES特定的头信息
            headers: {
                "X-Priority": "3", // 邮件优先级
            },
        });

        console.log("邮件发送成功:", result.messageId);
    } catch (error) {
        console.error("邮件发送失败:", error);
        throw error;
    }
};
