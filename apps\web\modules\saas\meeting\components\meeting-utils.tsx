import { Input } from "@ui/components/input";
import type { DateFilterProps } from "./types";

/**
 * 格式化时间戳为可读时间
 * 
 * @param timestamp - 时间戳（秒或毫秒）
 * @returns 格式化后的时间字符串
 */
export function formatTimestamp(timestamp: number | string): string {
  if (!timestamp) return '';
  
  // 如果时间戳是字符串，先转换为数字
  const ts = typeof timestamp === 'string' ? Number.parseInt(timestamp) : timestamp;
  
  // 判断时间戳是否为毫秒级别，如果是秒级别的时间戳，则转为毫秒
  const date = new Date(ts > 10000000000 ? ts : ts * 1000);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
}

/**
 * 格式化会议时长为小时和分钟
 * 
 * @param minutes - 分钟数
 * @returns 格式化的时长字符串
 */
export function formatDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours > 0 ? `${hours}小时` : ''}${mins > 0 ? `${mins}分钟` : ''}`;
}

/**
 * 格式化API返回的会议数据
 * 
 * @param meetingData - API返回的会议数据
 * @returns 格式化后的会议对象
 */
export function formatMeetingData(meetingData: any) {
  // 转换时间戳为日期对象
  const timestamp = Number.parseInt(meetingData.start_time || "0") * 1000; // 转换为毫秒
  const dateObj = new Date(timestamp);
      
  // 获取星期
  const weekdays = ["日", "一", "二", "三", "四", "五", "六"];
  const weekday = weekdays[dateObj.getDay()];
      
  // 格式化为：年-月-日-星期-时间
  const formattedTime = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')} 星期${weekday} ${String(dateObj.getHours()).padStart(2, '0')}:${String(dateObj.getMinutes()).padStart(2, '0')}`;
  
  // 处理结束时间
  const endTimestamp = Number.parseInt(meetingData.end_time || "0") * 1000;
  const endDateObj = new Date(endTimestamp);
  const endWeekday = weekdays[endDateObj.getDay()];
  const formattedEndTime = endTimestamp ? 
    `${endDateObj.getFullYear()}-${String(endDateObj.getMonth() + 1).padStart(2, '0')}-${String(endDateObj.getDate()).padStart(2, '0')} 星期${endWeekday} ${String(endDateObj.getHours()).padStart(2, '0')}:${String(endDateObj.getMinutes()).padStart(2, '0')}` :
    "";

  // 计算会议时长（分钟）
  const durationInMinutes = endTimestamp && timestamp 
    ? Math.round((endTimestamp - timestamp) / 60000) 
    : (meetingData.duration || 60); // 默认60分钟
  
  // 使用API返回的真实状态
  const status = meetingData.status || "MEETING_STATE_INIT";
  
  // 准备joinURL
  const joinURL = meetingData.join_url || "";
  
  return {
    id: meetingData.meeting_id || String(Math.random()),
    title: meetingData.subject || "未命名会议",
    startTime: formattedTime,
    endTime: formattedEndTime,
    meetingId: meetingData.meeting_code || meetingData.meeting_id || "000 000 000",
    status: status,
    duration: durationInMinutes,
    host: typeof meetingData.current_hosts?.[0] === 'object' 
      ? JSON.stringify(meetingData.current_hosts?.[0]) 
      : (meetingData.current_hosts?.[0] || meetingData.host_user_id || "未指定"),
    description: meetingData.meeting_description || meetingData.location || "暂无描述",
    joinURL: joinURL,
    joinUrl: joinURL, // 保留两个属性名以兼容不同调用
  };
} 


export function DateFilter({
	startDate,
	endDate,
	handleStartDateChange,
	handleEndDateChange,
	disabled = false,
}: DateFilterProps & { disabled?: boolean }) {
	return (
		<div className="flex items-center gap-4">
			<div className="flex items-center gap-2">
				<div className="text-sm">开始日期:</div>
				<Input
					type="date"
					value={startDate}
					onChange={handleStartDateChange}
					className="h-9 w-33 text-sm pl-2 pr-2 text-right"
					disabled={disabled}
					placeholder="2025/01/01"
				/>
			</div>

			<div className="flex items-center gap-2">
				<div className="text-sm ">结束日期:</div>
				<Input
					type="date"
					value={endDate}
					onChange={handleEndDateChange}
					className="h-9 w-33 text-sm pl-2 pr-2 text-right"
					disabled={disabled}
				/>
			</div>
		</div>
	);
}