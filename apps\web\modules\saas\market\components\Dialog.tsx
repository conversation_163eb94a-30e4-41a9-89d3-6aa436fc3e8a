import React, { useState, useEffect } from "react";
import { LargeDialog, LargeDialogContent, LargeDialogTitle, LargeDialogBody, LargeDialogEmptyState, LargeDialogErrorState } from "@ui/components/large-dialog";
import { Ta<PERSON>, Ta<PERSON>List, Ta<PERSON>Trigger, TabsContent } from "@ui/components/tabs";
import { Button } from "@ui/components/button";
import { ShadowHtmlRenderer } from "@ui/components/shadow-html-renderer";
import { cn } from "@ui/lib";
import { Star } from "lucide-react";
import type { FundCard } from "../lib/TestData";
import { useFundDetail } from "../hooks/useFundDetail"; // 路径按实际调整
import { DialogDescription } from "@ui/components/dialog";
import { ContactsDialog } from "@saas/market/components/ContactsDialog";


/**
 * InvestorDialog 组件 - 投资人详情弹窗 获取后端 API HTML页面或 markdown数据 并渲染
 * <AUTHOR>
 * @created 2025-07-04
 */
export interface InvestorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedCard: FundCard | null;
  html?: string;
  loading: boolean;
  error: string;
  isFavorite: boolean;
  onToggleFavorite: () => void;
  children?: React.ReactNode;
  onRefresh?: () => void;
}

export function InvestorDialog({ open, onOpenChange, selectedCard, isFavorite, onToggleFavorite, children,}: InvestorDialogProps) {
  // 每个卡片都有独立的tab状态，默认为"info"（资料）
  const [tab, setTab] = useState("info");
  const { data, isLoading, error: fetchError, refetch } = useFundDetail(selectedCard);

  // 当选中的卡片变化时，重置tab为默认的"info"
  useEffect(() => {
    if (selectedCard) {
      setTab("info");
    }
  }, [selectedCard?.code]);

  // data?.html 就是HTML内容
		// isLoading 控制骨架屏
		// error?.error 控制错误提示
		// refetch() 可用于刷新按钮

		return (
			<>
				<LargeDialog open={open} onOpenChange={onOpenChange}>
					<LargeDialogContent>
						{/*不加这个Description会警告*/}
						<DialogDescription className="sr-only">
							弹窗展示投资人资料详情，包含基本信息、联系人、持股、会议等。
						</DialogDescription>
						<LargeDialogTitle className="flex items-center gap-1 text-3xl mb-0 mt-5 text-black dark:text-slate-200 px-4">
							<span>{selectedCard?.name ?? "基金详情"}</span>
							<Button
								variant="ghost"
								size="icon"
								className="h-11 w-11 p-0 relative hover:bg-transparent"
								onClick={onToggleFavorite}
							>
								<Star
									className={cn(
										"w-7 h-7 cursor-pointer transform -translate-y-0.54",
										isFavorite
											? "fill-yellow-500 text-yellow-500 dark:fill-yellow-400 dark:text-yellow-400"
											: "fill-none text-yellow-500 dark:text-stone-400",
									)}
								/>
							</Button>
						</LargeDialogTitle>
						<LargeDialogBody>
							{children ? (
								children
							) : (
								<Tabs
									defaultValue="info"
									value={tab}
									onValueChange={setTab}
									className="w-full flex flex-col h-full"
								>
									<TabsList className="relative flex w-full max-w-full bg-white dark:bg-zinc-900 rounded-full p-1 gap-1 mx-1">
										<div className="flex-1 flex items-center gap-7">
											<TabsTrigger
												value="info"
												className="flex-1 rounded-full py-2 text-sm font-medium transition-all data-[state=active]:bg-black data-[state=active]:text-white dark:data-[state=active]:bg-zinc-800 cursor-pointer"
											>
												资料
											</TabsTrigger>
											<TabsTrigger
												value="contacts"
												className="flex-1 rounded-full py-2 text-sm font-medium transition-all data-[state=active]:bg-black data-[state=active]:text-white dark:data-[state=active]:bg-zinc-800 cursor-pointer"
											>
												联系人
											</TabsTrigger>
											<TabsTrigger
												value="shares"
												className="flex-1 rounded-full py-2 text-sm font-medium transition-all data-[state=active]:bg-black data-[state=active]:text-white dark:data-[state=active]:bg-zinc-800 cursor-pointer"
											>
												持股
											</TabsTrigger>
											<TabsTrigger
												value="meetings"
												className="flex-1 rounded-full py-2 text-sm font-medium transition-all data-[state=active]:bg-black data-[state=active]:text-white dark:data-[state=active]:bg-zinc-800 cursor-pointer"
											>
												会议
											</TabsTrigger>
										</div>
										<div className="flex-none flex items-center ml-5">
											{tab === "info" && (
												<Button
													type="button"
													onClick={() => refetch()}
													variant="ghost"
													className="absolute right-2 top-1/2 -translate-y-1/2 ml-2 p-0  rounded-full h-8 w-8 md:h-10 md:w-10 hover:bg-gray-100 dark:hover:bg-zinc-800 transition cursor-pointer"
													aria-label="刷新资料"
													loading={isLoading}
													disabled={isLoading}
												>
													<svg
														className="w-4 h-5 md:w-6 md:h-6"
														fill="none"
														stroke="currentColor"
														strokeWidth="2"
														viewBox="0 0 24 24"
													>
														<title>刷新资料</title>
														<path d="M21 2v6h-6" />
														<path d="M3 12a9 9 0 0 1 15-7.7L21 8" />
														<path d="M3 12a9 9 0 0 0 15 7.7l3-2.7" />
													</svg>
												</Button>
											)}
										</div>
									</TabsList>
									{/* 可滚动的内容区域 */}
									<div className="flex-1 overflow-auto">
										<TabsContent
											value="info"
											className="focus-visible:outline-none"
										>
											{/* 加载态 */}
											<div className="min-h-[638.9px] flex flex-col">
												{isLoading ? (
													<div className="w-full h-40 relative">
														<div className="animate-pulse w-full h-full bg-gray-50 dark:bg-zinc-800 rounded-xl" />
														<div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-sm text-gray-400 pointer-events-none">
															加载中……
														</div>
													</div>
												) : fetchError ? (
													<LargeDialogErrorState
														error={
															fetchError.message ||
															"加载失败"
														}
													/>
												) : data?.html ? (
													<ShadowHtmlRenderer
														html={data.html}
														className="w-full h-full"
														injectTailwindStyles={
															true
														}
													/>
												) : (
													<LargeDialogEmptyState message="暂无资料" />
												)}
											</div>
										</TabsContent>
										<TabsContent
											value="contacts"
											className="focus-visible:outline-none"
										>
											<div className="min-h-[638.9px] flex flex-col">
												{tab === "contacts" && <ContactsDialog cardCode={selectedCard?.code} />}
											</div>
										</TabsContent>
										<TabsContent
											value="shares"
											className="focus-visible:outline-none"
										>
											<div className="min-h-[638.9px] flex flex-col">
												<div className="text-muted-foreground text-center py-8">
													持股信息开发中...
												</div>
											</div>
										</TabsContent>
										<TabsContent
											value="meetings"
											className="focus-visible:outline-none"
										>
											<div className="min-h-[638.9px] flex flex-col">
												<div className="text-muted-foreground text-center py-8">
													会议信息开发中...
												</div>
											</div>
										</TabsContent>
									</div>
								</Tabs>
							)}
						</LargeDialogBody>
					</LargeDialogContent>
				</LargeDialog>
			</>
		);
}
