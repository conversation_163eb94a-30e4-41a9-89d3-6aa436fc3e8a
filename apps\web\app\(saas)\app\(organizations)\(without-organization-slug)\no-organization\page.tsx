/* 修改块开始: 无组织提示页面 - 极简版本
 * 修改范围: 新增无组织用户提示页面，采用与CreateOrganizationForm相同的极简布局
 * 修改时间: 2025-06-28
 * 恢复方法: 删除此文件
 */

"use client";

import { config } from "@repo/config";
import { Button } from "@ui/components/button";
import { authClient } from "@repo/auth/client";
import { clearCache } from "@shared/lib/cache";

/**
 * 极简无组织提示页面 - 复用CreateOrganizationForm的布局结构
 */
export default function NoOrganizationPage() {
	/* 修改块开始: 退出登录功能
	 * 修改范围: 新增退出登录处理函数
	 * 修改时间: 2025-06-28
	 * 对应计划步骤: 步骤2
	 * 恢复方法: 删除此函数定义
	 */
	const onLogout = () => {
		authClient.signOut({
			fetchOptions: {
				onSuccess: async () => {
					await clearCache();
					window.location.href = new URL(
						config.auth.redirectAfterLogout,
						window.location.origin,
					).toString();
				},
			},
		});
	};
	/* 修改块结束: 退出登录功能
	 * 修改时间: 2025-06-28
	 */

	return (
		<div className="mx-auto w-full max-w-md">
			<h1 className="font-extrabold text-2xl md:text-3xl">
				需要加入组织
			</h1>
			<p className="mt-2 mb-6 text-foreground/60">
				您还没有加入任何组织，请联系管理员邀请您加入组织以继续使用。
			</p>
			<p className="text-sm text-muted-foreground">
				联系邮箱：{config.contactForm.to}
			</p>
			
			{/* 修改块开始: 退出登录按钮
			 * 修改范围: 在页面底部添加退出登录按钮
			 * 修改时间: 2025-06-28
			 * 对应计划步骤: 步骤3
			 * 恢复方法: 删除此按钮代码
			 */}
			<Button 
				onClick={onLogout} 
				variant="outline" 
				className="mt-4 w-full"
			>
				退出登录
			</Button>
			{/* 修改块结束: 退出登录按钮
			 * 修改时间: 2025-06-28
			 */}
		</div>
	);
}

/* 修改块结束: 无组织提示页面
 * 修改时间: 2025-06-28
 */ 