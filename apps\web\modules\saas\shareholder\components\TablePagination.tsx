"use client";

import { useMemo, useRef, useEffect } from "react";
import type { PaginationInfo } from "@saas/shareholder/lib/types";

interface TablePaginationProps {
  pagination: PaginationInfo;
  page: number;
  // onPageChange: (page: number) => void; // 原代码保留于2025-06-17 14:54:06 - 不再需要页面切换功能
  // onLimitChange?: (limit: number) => void; // 原代码保留于2025-06-17 14:54:06 - 不再需要限制条数功能
  // isScrollLoading?: boolean; // 原代码保留于2025-06-17 14:54:06 - 不再需要滚动加载状态
  // resetPagination?: boolean; // 原代码保留于2025-06-17 14:54:06 - 不再需要重置分页状态
}

/**
 * 表格分页组件
 * 简化显示，只在右下角显示页数和总数格式如"20/200"
 *
 * <AUTHOR>
 * @version 7.2.0 (2025-06-17 15:03:47) - 添加数据缓存机制，避免加载时数字闪烁，保持显示稳定性
 * @version 7.1.0 (2025-06-17 15:00:25) - 使用useMemo优化计算，解决滚动时数字闪烁问题
 * @version 7.0.0 (2025-06-17 14:54:06) - 彻底简化组件，只显示页数/总数格式，移除所有多余功能和参数
 * @version 6.0.0 (2025-06-17 14:47:51) - 重构为简化显示模式，只显示页数/总数格式，位置调整为右下角
 * @version 5.1.0 (2025-06-13 10:42:55) - 添加数据为空时不显示状态的逻辑，当total为0时不显示任何内容
 * @version 2.0.0 (2025-05-19) - 移除移动端适配逻辑，只保留桌面端布局
 */
export function TablePagination({
  pagination,
  page,
}: TablePaginationProps) {
  // 缓存上一次有效的显示信息，避免加载时闪烁 - 添加于2025-06-17 15:03:47
  const lastValidDisplayRef = useRef<{
    currentDisplayedItems: number;
    totalItems: number;
  } | null>(null);

  // 使用useMemo缓存计算结果，避免滚动时频繁重新计算导致闪烁 - 添加于2025-06-17 15:00:25
  const displayInfo = useMemo(() => {
    const totalItems = pagination?.total || 0;
    const itemsPerPage = pagination?.limit || 20;
    const currentDisplayedItems = Math.min(page * itemsPerPage, totalItems);

    return {
      totalItems,
      currentDisplayedItems,
      shouldShow: totalItems > 0
    };
  }, [pagination?.total, pagination?.limit, page]);

  // 当有有效数据时，更新缓存 - 添加于2025-06-17 15:03:47
  useEffect(() => {
    if (displayInfo.shouldShow) {
      lastValidDisplayRef.current = {
        currentDisplayedItems: displayInfo.currentDisplayedItems,
        totalItems: displayInfo.totalItems
      };
    }
  }, [displayInfo.shouldShow, displayInfo.currentDisplayedItems, displayInfo.totalItems]);

  // 如果当前没有数据但有缓存的数据，继续显示缓存的数据 - 添加于2025-06-17 15:03:47
  const shouldShowCached = !displayInfo.shouldShow && lastValidDisplayRef.current;
  const finalDisplayData = displayInfo.shouldShow
    ? displayInfo
    : (shouldShowCached ? lastValidDisplayRef.current : null);

  // 当没有任何数据（包括缓存）时，不显示组件 - 修改于2025-06-17 15:03:47
  if (!finalDisplayData) {
    return null;
  }

  // 简化显示：只显示页数/总数格式，位置在右下角，加粗显示 - 修改于2025-06-17 15:03:47
  return (
    <div className="flex justify-end px-2 py-1">
      <div className="text-sm text-foreground">
        {finalDisplayData.currentDisplayedItems}/{finalDisplayData.totalItems}
      </div>
    </div>
  );
}