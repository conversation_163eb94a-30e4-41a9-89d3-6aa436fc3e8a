import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@ui/components/dialog";
import { ShareholderRegistryImport } from "@saas/shareholder/components/registry-import";
import { useRef, useEffect } from "react";

interface ImportShareholderRegistryDialogProps {
  isOpen: boolean;
  organizationId: string;
  onOpenChange: (open: boolean) => void;
}

/**
 * 导入股东名册对话框容器组件
 * 
 * @update 2025-05-27 修复关闭按钮无法关闭弹窗的问题，通过监听点击事件来区分关闭按钮点击和外部点击
 * @update 2025-05-26 添加onEscapeKeyDown处理函数，阻止ESC键关闭弹窗
 * @update 2025-05-26 修改onOpenChange处理逻辑，完全阻止点击外部关闭弹窗的行为
 * @update 2025-05-26 添加modal属性，防止点击外部关闭对话框
 * @update 2025-05-19 更新导入逻辑，使用新的校验和导入流程
 */
export function ImportShareholderRegistryDialog({
  isOpen,
  organizationId,
  onOpenChange,
}: ImportShareholderRegistryDialogProps) {
  // 跟踪最近的点击是否来自关闭按钮
  const isCloseButtonClicked = useRef(false);

  // 监听点击事件，检测是否点击了关闭按钮
  useEffect(() => {
    const handleClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      // 检查是否点击了关闭按钮或其子元素
      if (target && (
        target.closest('[aria-label="Close"]') || 
        target.closest('button.absolute.top-4.right-4') ||
        target.tagName === 'svg' || 
        target.tagName === 'path'
      )) {
        isCloseButtonClicked.current = true;
        // 重置状态的延时需要比Dialog的关闭动画时间短
        setTimeout(() => {
          isCloseButtonClicked.current = false;
        }, 100);
      }
    };

    document.addEventListener('click', handleClick, true);
    return () => {
      document.removeEventListener('click', handleClick, true);
    };
  }, []);

  /**
   * 自定义的onOpenChange处理函数
   * 根据点击源区分处理逻辑:
   * - 当open为true时（即打开弹窗时）正常调用父组件的onOpenChange
   * - 当open为false且isCloseButtonClicked为true时，允许关闭弹窗
   * - 当open为false且isCloseButtonClicked为false时，阻止关闭行为
   */
  const handleOpenChange = (open: boolean) => {
    if (open) {
      // 打开弹窗时正常调用
      onOpenChange(open);
    } else if (isCloseButtonClicked.current) {
      // 如果是通过关闭按钮关闭，允许关闭
      onOpenChange(open);
    }
    // 如果是点击外部区域，不执行任何操作，阻止关闭
  };

  /**
   * 阻止ESC键关闭弹窗
   */
  const handleEscapeKeyDown = (event: KeyboardEvent) => {
    event.preventDefault();
  };

  return (
    <Dialog 
      open={isOpen} 
      onOpenChange={handleOpenChange} 
      modal={true}
    >
      <DialogContent 
        className="sm:max-w-[600px] p-0"
        onEscapeKeyDown={handleEscapeKeyDown}
      >
        <DialogTitle className="sr-only">导入股东名册</DialogTitle>
        <DialogDescription className="sr-only">
          支持DBF、XLS、XLSX和ZIP格式，单次可上传多个文件，系统会逐个进行校验和导入
        </DialogDescription>
        <ShareholderRegistryImport 
          organizationId={organizationId} 
          onComplete={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
} 