"use client";

import React from 'react';
import { Button } from "@ui/components/button";
import { Progress } from "@ui/components/progress";
import { FileTypeIcon, XIcon, FileUpIcon } from "lucide-react";
import type { ShareholderRegistryParseResult } from "@saas/shareholder/lib/dbf-parser";

interface FileListItemProps {
  file: File;
  index: number;
  currentFileIndex: number;
  parseResult?: ShareholderRegistryParseResult;
  importPhase: "idle" | "validating" | "importing";
  uploadProgress: number;
  onRemove: (index: number) => void;
}

/**
 * 文件列表项组件
 * 显示单个文件的信息、状态和处理进度
 * 
 * @param {Object} props - 组件属性
 * @param {File} props.file - 文件对象
 * @param {number} props.index - 文件索引
 * @param {number} props.currentFileIndex - 当前处理的文件索引
 * @param {ShareholderRegistryParseResult} props.parseResult - 文件解析结果
 * @param {string} props.importPhase - 导入阶段
 * @param {number} props.uploadProgress - 上传进度
 * @param {Function} props.onRemove - 移除文件的回调函数
 * 
 * @update 2025-06-04 16:15:02 从ShareholderRegistryImport组件中拆分出来
 */
export function FileListItem({
  file,
  index,
  currentFileIndex,
  parseResult,
  importPhase,
  uploadProgress,
  onRemove
}: FileListItemProps) {
  // 获取文件图标和描述
  const getFileTypeInfo = () => {
    const extension = file.name.split(".").pop()?.toLowerCase();
    if (extension === "dbf") {
      return {
        icon: <FileUpIcon className="size-4 text-blue-500" />,
        text: "DBF文件",
      };
    }
    if (extension === "xls" || extension === "xlsx") {
      return {
        icon: <FileTypeIcon className="size-4 text-green-500" />,
        text: "Excel文件",
      };
    }
    	if (extension === "zip") {
						return {
							icon: (
								<FileTypeIcon className="size-4 text-purple-500" />
							),
							text: "ZIP压缩包",
						};
					}
    // 对于不支持的文件，返回通用图标和文本
    return {
      icon: <FileTypeIcon className="size-4" />,
      text: "不支持的文件",
    };
  };

  const fileTypeInfo = getFileTypeInfo();
  const isProcessing = currentFileIndex === index && importPhase !== "idle";

  return (
    <div className="rounded-md bg-muted p-3 border border-border/30 hover:border-border/50 transition-colors">
      {/* 文件信息行 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {fileTypeInfo.icon}
          <div className="min-w-0 flex-1">
            <div className="font-medium truncate">
              {file.name}
            </div>
            <div className="text-muted-foreground text-xs">
              {fileTypeInfo.text}{" "}
              •{" "}
              {(file.size / 1024 / 1024).toFixed(2)}{" "}
              MB
            </div>
          </div>
        </div>
        {/* 删除文件按钮 */}
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 flex-shrink-0"
          onClick={() => onRemove(index)}
          disabled={importPhase !== "idle"}
        >
          <XIcon className="h-4 w-4" />
        </Button>
      </div>

      {/* 文件处理进度条 */}
      {isProcessing && (
        <div className="mt-2">
          <div className="mb-1 flex justify-between text-xs">
            <span>
              {importPhase === "validating"
                ? "校验中..."
                : "导入中..."}
            </span>
            {importPhase === "importing" && (
              <span>
                {uploadProgress}%
              </span>
            )}
          </div>
          <Progress
            value={importPhase === "validating" ? 0 : uploadProgress}
            className="h-2 w-full"
          />
        </div>
      )}

      {/* 文件处理结果 */}
      {parseResult && (
        <div className="mt-2 text-xs">
          {parseResult.success ? (
            <span className="text-green-500">
              已成功预处理，
              {parseResult.recordCount}{" "}
              条记录
            </span>
          ) : (
            <span className="text-destructive">
              {parseResult.error?.message || "处理失败"}
            </span>
          )}
        </div>
      )}
    </div>
  );
} 