/**
 * 删除投资人联系人路由
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建联系人删除接口
 * @description 删除投资人联系人信息，需要验证权限和存在性
 */
import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { DeleteInvestorContactSchema } from "../lib/validators";

export const contactsDeleteRouter = new Hono().post(
  "/delete",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = DeleteInvestorContactSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { contactId, organizationId } = validationResult.data;

      // 查找联系人记录
      const existingContact = await db.investorContact.findFirst({
        where: {
          contactId,
          organizationId,
        }
      });

      if (!existingContact) {
        c.set("response", {
          code: 404,
          message: "联系人不存在或无权限访问",
          data: null
        });
        return;
      }

      // 删除联系人记录
      await db.investorContact.delete({
        where: { contactId }
      });

      c.set("response", {
        code: 200,
        message: "联系人删除成功",
        data: {
          contactId: existingContact.contactId
        }
      });
      return;

    } catch (error) {
      if (error instanceof HTTPException) {
        c.set("response", {
          code: error.status,
          message: error.message,
          data: null
        });
        return;
      }
      
      console.error("投资人管理API错误:", error);
      c.set("response", {
        code: 500,
        message: "服务器内部错误",
        data: null
      });
      return;
    }
  }
);
