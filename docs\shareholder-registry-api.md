# 股东名册API文档

**文档版本**: 1.0  
**创建时间**: 2025-06-30 15:35:45  
**作者**: hayden  
**更新时间**: 2025-06-30 15:35:45  

## 概述

股东名册API提供了完整的股东名册管理功能，包括上传、查询、删除等操作。所有接口都使用POST方法，支持加密传输，并需要用户身份验证。

## 基础信息

- **基础路径**: `/api/shareholder-registry`
- **请求方法**: POST
- **认证方式**: Bearer Token (通过authMiddleware验证)
- **加密方式**: 请求和响应数据通过shareholderCryptoMiddleware进行加解密
- **响应格式**: JSON

### 通用响应结构

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {} // 具体数据内容
}
```

## API接口列表

### 1. 上传股东名册

**接口路径**: `/upload`  
**功能描述**: 上传并处理股东名册数据，支持深市01/05名册和沪市t1/t2/t3名册

#### 请求参数

```json
{
  "organizationId": "string",     // 组织ID (必填)
  "fileName": "string",           // 文件名 (必填)
  "recordCount": 1000,            // 记录数量 (必填，正整数)
  "registerDate": "2024-03-29",   // 报告日期 (必填，YYYY-MM-DD格式)
  "companyCode": "300001",        // 公司代码 (必填)
  "companyInfo": {
    "companyName": "测试公司",     // 公司名称 (可选)
    "totalShares": "********00",  // 总股数 (必填)
    "totalShareholders": 5000,    // 总户数 (必填，非负整数)
    "totalInstitutions": 100,     // 机构总数 (必填，非负整数)
    "largeSharesCount": "*********", // 持有万份以上总份数 (必填)
    "institutionShares": "*********", // 总机构股数 (必填)
    "largeShareholdersCount": 50, // 持有万份以上总户数 (必填，非负整数)
    "marginAccounts": 20,         // 信用总户数 (可选，非负整数)
    "marginShares": "********"    // 信用股数 (可选)
  },
  "shareholders": [               // 股东列表 (必填，至少一个)
    {
      // 股东数据字段根据名册类型而定
      // 01名册: ZJDM, YMTH, ZQZHMC, CYRLBMS, CGSL, XSGSL, CGBL, DJGS等
      // 05名册: XYZHZJDM, XYZHMC, CGSL, DJGS, HZZQZH, HZZHMC等
      // t1名册: ZJHM, YMTZHHM, GDLB, CYRMC, LTLX, YQLB等
      // t2名册: ZJHM, YMTZHHM, GDLB, CYRMC, TZRMC, GDMC等
      // t3名册: ZJHM, YMTZHHM, GDLB, CYRMC, ZCYSL, PTZQZHCYSL, XYCYSL等
    }
  ],
  "registryType": "01"            // 名册类型 (可选，从文件名自动解析)
}
```

#### 响应数据

```json
{
  "code": 200,
  "message": "股东名册上传成功",
  "data": {
    "id": "registry_id_123",
    "fileName": "DQMC01_300001_20240329.dbf",
    "recordCount": 1000,
    "registerDate": "2024-03-29",
    "uploadedAt": "2025-06-30T15:35:45.797Z"
  }
}
```

### 2. 查询股东名册列表

**接口路径**: `/list`  
**功能描述**: 获取特定组织下的股东名册列表，支持分页和公司代码筛选

#### 请求参数

```json
{
  "organizationId": "string",     // 组织ID (必填)
  "page": 1,                      // 当前页码 (可选，默认1)
  "limit": 10,                    // 每页记录数 (可选，默认10)
  "companyCode": "300001"         // 公司代码 (可选，用于筛选)
}
```

#### 响应数据

```json
{
  "code": 200,
  "message": "获取股东名册列表成功",
  "data": {
    "registries": [
      {
        "id": "registry_id_123",
        "fileName": "DQMC01_300001_20240329.dbf",
        "recordCount": 1000,
        "registerDate": "2024-03-29",
        "companyCode": "300001",
        "companyName": "测试公司",
        "uploadedAt": "2025-06-30T15:35:45.797Z",
        "userName": "管理员",
        "companyDetail": {
          "companyName": "测试公司",
          "totalShares": "********00",
          "totalShareholders": 5000,
          "totalInstitutions": 100,
          "largeSharesCount": "*********",
          "institutionShares": "*********",
          "largeShareholdersCount": 50,
          "controllingShareholderInfo": {
            "securitiesAccountName": "控股股东名称",
            "shareholdingRatio": "25.50",
            "numberOfShares": "*********"
          },
          "topTenShareholdersInfo": {
            "totalRatio": "65.80",
            "totalShares": "*********"
          }
        }
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 10,
      "totalPages": 10
    }
  }
}
```

### 3. 查询股东列表

**接口路径**: `/shareholders`  
**功能描述**: 获取特定股东名册下的股东信息，支持分页、搜索和排序

#### 请求参数

```json
{
  "registerDate": "2024-03-29",   // 报告日期 (可选，为空时查询最新期)
  "organizationId": "string",     // 组织ID (必填)
  "page": 1,                      // 当前页码 (可选，默认1)
  "limit": 10,                    // 每页记录数 (可选，默认10)
  "searchTerm": "关键词",         // 搜索关键词 (可选)
  "sortBy": "numberOfShares",     // 排序字段 (可选，默认numberOfShares)
  "sortOrder": "desc"             // 排序方向 (可选，默认desc)
}
```

#### 响应数据

```json
{
  "code": 200,
  "message": "获取股东列表成功",
  "data": {
    "shareholders": [
      {
        "id": "shareholder_id_123",
        "shareholderId": "SH001",
        "unifiedAccountNumber": "A123456789",
        "securitiesAccountName": "股东姓名",
        "shareholderCategory": "个人",
        "numberOfShares": "1000000",
        "lockedUpShares": "0",
        "shareholdingRatio": "0.10",
        "frozenShares": "0",
        "contactAddress": "联系地址",
        "contactNumber": "***********",
        "zipCode": "100000",
        "cashAccount": "现金账户",
        "sharesInCashAccount": "500000",
        "marginAccount": "信用账户",
        "sharesInMarginAccount": "500000",
        "relatedPartyIndicator": "否",
        "clientCategory": "普通",
        "remarks": "备注信息",
        "rank": 1
      }
    ],
    "pagination": {
      "total": 1000,
      "page": 1,
      "limit": 10,
      "totalPages": 100
    }
  }
}
```

### 4. 查询期数日期列表

**接口路径**: `/register-dates`
**功能描述**: 获取股东名册的所有报告期日期

#### 请求参数

```json
{
  "organizationId": "string",     // 组织ID (必填)
  "companyCode": "300001"         // 公司代码 (可选，用于筛选)
}
```

#### 响应数据

```json
{
  "code": 200,
  "message": "获取期数日期列表成功",
  "data": {
    "registerDates": [
      {
        "registerDate": "2024-03-29",
        "companyCode": "300001"
      },
      {
        "registerDate": "2023-12-31",
        "companyCode": "300001"
      }
    ]
  }
}
```

### 5. 查询股东类型列表

**接口路径**: `/shareholder-types`
**功能描述**: 获取指定组织下的所有股东类型

#### 请求参数

```json
{
  "organizationId": "string"      // 组织ID (必填)
}
```

#### 响应数据

```json
{
  "code": 200,
  "message": "获取股东类型列表成功",
  "data": {
    "shareholderTypes": [
      "个人",
      "机构",
      "基金",
      "保险",
      "券商"
    ]
  }
}
```

### 6. 股东持股变化分析

**接口路径**: `/shareholding-changes`
**功能描述**: 分析指定时间范围内股东持股变化情况，支持按排名、期数日期或具体日期排序

#### 请求参数

```json
{
  "organizationId": "string",     // 组织ID (必填)
  "startDate": "2024-01-01",      // 开始日期 (必填，YYYY-MM-DD格式)
  "endDate": "2024-03-29",        // 结束日期 (必填，YYYY-MM-DD格式)
  "shareholderType": "个人",      // 股东类型 (可选，"all"或空字符串查询所有类型)
  "searchTerm": "关键词",         // 搜索关键词 (可选)
  "sortType": "rank",             // 排序类型 (可选，默认rank)
                                  // rank: 按净变化排序
                                  // date: 按期数日期排序
                                  // YYYY-MM-DD: 按指定期数的持股变化排序
  "sortOrder": "desc",            // 排序方向 (可选，默认desc)
  "page": 1,                      // 当前页码 (可选，默认1)
  "limit": 30                     // 每页记录数 (可选，默认30，最大100)
}
```

#### 响应数据

```json
{
  "code": 200,
  "message": "获取股东持股变化分析数据成功",
  "data": {
    "analysisRange": {
      "startDate": "2024-01-01",
      "endDate": "2024-03-29",
      "totalPeriods": 3
    },
    "availableDates": [
      "2024-01-01",
      "2024-02-15",
      "2024-03-29"
    ],
    "shareholderChanges": [
      {
        "shareholderId": "SH001",
        "shareholderName": "股东姓名",
        "shareholderType": "个人",
        "unifiedAccountNumber": "A123456789",
        "netChange": 500000,
        "startHolding": 1000000,
        "endHolding": 1500000,
        "rank": 1,
        "periodChanges": [
          {
            "date": "2024-01-01",
            "change": 0,
            "holding": 1000000
          },
          {
            "date": "2024-02-15",
            "change": 200000,
            "holding": 1200000
          },
          {
            "date": "2024-03-29",
            "change": 300000,
            "holding": 1500000
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 30,
      "total": 100,
      "totalPages": 4,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 7. 删除股东名册

**接口路径**: `/delete`
**功能描述**: 删除指定的股东名册记录，同时会级联删除关联的公司信息和股东信息

#### 请求参数

```json
{
  "registryId": "registry_id_123" // 股东名册ID (必填)
}
```

#### 响应数据

```json
{
  "code": 200,
  "message": "股东名册已成功删除",
  "data": null
}
```

### 8. 批量删除股东名册

**接口路径**: `/batch-delete`
**功能描述**: 批量删除多个股东名册记录，同时会级联删除关联的公司信息和股东信息

#### 请求参数

```json
{
  "registryIds": [               // 股东名册ID数组 (必填，至少包含一个ID)
    "registry_id_123",
    "registry_id_456"
  ]
}
```

#### 响应数据

```json
{
  "code": 200,
  "message": "已成功删除 2 个股东名册",
  "data": {
    "success": {
      "count": 2,
      "ids": ["registry_id_123", "registry_id_456"]
    },
    "failed": {
      "count": 0,
      "ids": []
    },
    "total": 2
  }
}
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 示例 |
|--------|------|------|
| 400 | 请求参数错误 | 参数验证失败、数据格式错误 |
| 401 | 未授权 | Token无效或过期 |
| 404 | 资源不存在 | 指定的股东名册不存在 |
| 500 | 服务器内部错误 | 数据库连接失败、处理异常 |

### 错误响应格式

```json
{
  "code": 400,
  "message": "请求参数验证失败：组织ID不能为空",
  "data": null
}
```

## 业务规则

### 1. 名册类型识别

系统支持以下名册类型：
- **01名册**: 深市股东名册（DQMC01开头）
- **05名册**: 深市信用账户名册（DQMC05开头）
- **t1名册**: 沪市前200名股东名册（t1开头）
- **t2名册**: 沪市机构股东名册（t2开头）
- **t3名册**: 沪市全部股东名册（t3开头）

### 2. 合并处理规则

- 同一报告期内，01和05名册可以合并
- 同一报告期内，t1、t2、t3名册可以合并
- T3全量名册可以替换T3前200版本

### 3. 数据验证规则

- 文件名必须包含正确的类型标识
- 股东数据字段必须符合对应名册类型要求
- recordCount必须与shareholders数组长度一致
- 日期格式必须为YYYY-MM-DD

### 4. 级联删除规则

删除股东名册时会自动删除：
- 关联的公司信息记录
- 关联的股东信息记录
- 如果是组织下最后一份名册，会清除组织metadata中的绑定信息

## 注意事项

1. **加密传输**: 所有请求和响应数据都经过加密处理
2. **身份验证**: 所有接口都需要有效的用户Token
3. **数据类型**: Decimal类型字段在响应中转换为字符串格式
4. **分页查询**: 支持分页的接口都包含完整的分页信息
5. **搜索功能**: 支持模糊搜索股东姓名、证件号码、账户号码等字段
6. **排序功能**: 支持多种排序方式，默认按持股数量降序排列

---

**文档更新记录**:
- 2025-06-30 15:35:45: hayden 创建初始版本，包含所有8个API接口的完整文档
