/**
 * 细分机构股东多柱状图组件
 * @file InstitutionalShareholdersChart.tsx
 * @description 用于展示细分机构股东的多柱状图，支持户数和持股数量两种模式
 * <AUTHOR>
 * @created 2025-06-27 19:31:04
 * @modified 2025-06-27 20:38:11 - hayden - 图例色块改为小圆形，坐标轴字体调小
 * @modified 2025-06-30 10:30:57 - hayden - 添加主题适配，XAxis和YAxis颜色根据主题自动切换
 */

"use client";
import { useMemo } from "react";
import {
	BarChart,
	Bar,
	XAxis,
	YAxis,
	Tooltip,
	ResponsiveContainer,
	Legend,
} from "recharts";
import { useTheme } from "next-themes";
import type { ShareholdersTrendDataItem, ShareholderTypeDataItem } from "@saas/shareholder/lib/shareholders-trend-api";

/**
 * 图表模式类型
 * @type ChartMode
 */
export type ChartMode = "count" | "shares";

/**
 * 图表数据点接口
 * @interface ChartDataPoint
 */
interface ChartDataPoint {
	/** 期数（时间标签） */
	period: string;
	/** 动态字段，包含各个机构类型的数据 */
	[key: string]: string | number;
}



/**
 * 组件属性接口
 * @interface InstitutionalShareholdersChartProps
 */
interface InstitutionalShareholdersChartProps {
	/** 股东结构历史趋势数据 */
	trendData: ShareholdersTrendDataItem[];
	/** 图表模式：户数或持股数量 */
	mode: ChartMode;
	/** 图表标题 */
	title: string;
	/** 图表高度，默认160px */
	height?: number;
}

/**
 * 格式化日期为显示格式
 * @param dateString ISO 日期字符串
 * @returns 格式化后的日期字符串
 * <AUTHOR>
 * @created 2025-06-27 19:31:04
 */
function formatDateForChart(dateString: string): string {
	const date = new Date(dateString);
	const year = date.getFullYear();
	const month = date.getMonth() + 1;
	const day = date.getDate();
	return `${year}-${month.toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
}

/**
 * 格式化数值显示
 * @param value 数值
 * @param mode 图表模式
 * @returns 格式化后的字符串
 * <AUTHOR>
 * @created 2025-06-27 19:31:04
 */
function formatValue(value: number, mode: ChartMode): string {
	if (mode === "count") {
		// 户数格式化：大于1000显示为千位
		if (value >= 1000) {
			const formatted = (value / 1000).toFixed(1);
			return `${formatted.endsWith(".0") ? formatted.slice(0, -2) : formatted}k`;
		}
		return value.toString();
	}
	
	if (mode === "shares") {
		// 股数格式化：大于1万显示为万，大于1亿显示为亿
		if (value >= 100000000) {
			const formatted = (value / 100000000).toFixed(1);
			return `${formatted.endsWith(".0") ? formatted.slice(0, -2) : formatted}亿`;
		}
		if (value >= 10000) {
			const formatted = (value / 10000).toFixed(1);
			return `${formatted.endsWith(".0") ? formatted.slice(0, -2) : formatted}万`;
		}
		return value.toString();
	}
	
	return value.toString();
}

/**
 * 获取默认颜色配置
 * @returns 默认颜色数组
 * <AUTHOR>
 * @created 2025-06-27 19:46:24
 * @modified 2025-06-27 19:46:24 - 改为动态颜色配置，支持更多机构类型
 */
function getDefaultColors(): string[] {
	return [
		"#3b82f6", // 蓝色
		"#10b981", // 绿色
		"#f59e0b", // 橙色
		"#8b5cf6", // 紫色
		"#ef4444", // 红色
		"#06b6d4", // 青色
		"#84cc16", // 黄绿色
		"#6b7280", // 灰色
		"#ec4899", // 粉色
		"#14b8a6", // 蓝绿色
		"#f97316", // 深橙色
		"#a855f7", // 深紫色
		"#22c55e", // 亮绿色
		"#eab308", // 黄色
		"#dc2626", // 深红色
		"#0891b2", // 深青色
	];
}

/**
 * 细分机构股东多柱状图组件
 * @param props 组件属性
 * @returns 多柱状图JSX元素
 * <AUTHOR>
 * @created 2025-06-27 19:31:04
 */
export function InstitutionalShareholdersChart({
	trendData,
	mode,
	title,
	height = 160,
}: InstitutionalShareholdersChartProps): JSX.Element {
	// 获取当前主题
	const { resolvedTheme } = useTheme();

	// 根据主题设置坐标轴颜色
	const axisColor = resolvedTheme === "dark" ? "#ffffff" : "#000000";

	// 转换数据为图表格式
	const chartData = useMemo<ChartDataPoint[]>(() => {
		if (!trendData || trendData.length === 0) {
			return [];
		}

		return trendData.map((item) => {
			const dataPoint: ChartDataPoint = {
				period: formatDateForChart(item.registerDate),
			};

			// 为每个机构类型添加数据
			if (item.typeData && Array.isArray(item.typeData)) {
				item.typeData.forEach((typeItem: ShareholderTypeDataItem) => {
					const value = mode === "count" ? typeItem.count : typeItem.shares;
					dataPoint[typeItem.type] = value;
				});
			}

			return dataPoint;
		});
	}, [trendData, mode]);

	// 获取所有出现过的机构类型
	const availableTypes = useMemo(() => {
		const typeSet = new Set<string>();

		trendData.forEach((item) => {
			if (item.typeData && Array.isArray(item.typeData)) {
				item.typeData.forEach((typeItem: ShareholderTypeDataItem) => {
					typeSet.add(typeItem.type);
				});
			}
		});

		return Array.from(typeSet);
	}, [trendData]);

	// 获取对应的颜色配置
	const typeColorMap = useMemo(() => {
		const colorMap: Record<string, string> = {};
		const defaultColors = getDefaultColors();

		availableTypes.forEach((type, index) => {
			// 使用默认颜色数组，循环使用
			colorMap[type] = defaultColors[index % defaultColors.length];
		});

		return colorMap;
	}, [availableTypes]);

	// 计算动态柱状图宽度和间距
	const { barSize, margin } = useMemo(() => {
		const periodCount = chartData.length;
		const typeCount = availableTypes.length;

		// 基础宽度计算：根据期数和机构类型数量动态调整
		let baseWidth = 20;
		// 修改记录：2025-06-27 20:01:40 - hayden - 图例在底部，恢复正常边距，增加底部边距
		let chartMargin = { top: 20, right: 30, left: 20, bottom: 25 };

		if (periodCount <= 2) {
			// 期数少时，柱状图可以粗一些
			baseWidth = Math.max(15, 40 / Math.max(typeCount, 1));
		} else if (periodCount <= 5) {
			// 中等期数
			baseWidth = Math.max(12, 30 / Math.max(typeCount, 1));
		} else {
			// 期数多时，柱状图要细一些，增加左右边距
			baseWidth = Math.max(6, 20 / Math.max(typeCount, 1));
			chartMargin = { top: 20, right: 40, left: 30, bottom: 25 };
		}

		// 原逻辑：机构类型多时，增加右边距给图例留空间（现已移至顶部，不再需要）
		// if (typeCount > 6) {
		// 	chartMargin.right = Math.max(chartMargin.right, 60);
		// }

		return {
			barSize: Math.floor(baseWidth),
			margin: chartMargin,
		};
	}, [chartData.length, availableTypes.length]);

	// 如果没有数据，显示占位内容
	if (chartData.length === 0 || availableTypes.length === 0) {
		return (
			<div className="rounded-lg bg-card p-3 pb-2 shadow-sm border border-border">
				<h4 className="mb-4 font-medium text-sm text-foreground/80">
					{title}
				</h4>
				<div style={{ height: `${height}px` }}>
					<div className="flex items-center justify-center h-full">
						<div className="text-center">
							<div className="text-muted-foreground text-sm mb-2">
								暂无细分机构数据
							</div>
							<div className="text-xs text-muted-foreground/60">
								请确保数据包含typeData字段
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="rounded-lg bg-card p-3 pb-2 shadow-sm border border-border">
			<h4 className="mb-4 font-medium text-sm text-foreground/80">
				{title}
			</h4>
			<div style={{ height: `${height}px` }}>
				<ResponsiveContainer width="100%" height="100%">
					<BarChart
						data={chartData}
						margin={margin}
					>
						<XAxis
							dataKey="period"
							axisLine={false}
							tickLine={false}
							tick={{
								fontSize: 9,
								fill: axisColor,
							}}
							className="text-xs text-foreground/80"
						/>
						<YAxis
							axisLine={false}
							tickLine={false}
							tick={{
								fontSize: 9,
								fill: axisColor,
							}}
							className="text-xs text-foreground/80"
							width={60}
							tickFormatter={(value) => formatValue(value, mode)}
						/>
						<Tooltip
							cursor={false}
							contentStyle={{
								border: "1px solid hsl(var(--border))",
								borderRadius: "8px",
								boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
								fontSize: "8px",
							}}
							labelStyle={{ color: "hsl(var(--foreground))" }}
							formatter={(value: number, name: string) => [
								`${formatValue(value, mode)}${mode === "count" ? "户" : "股"}`,
								name,
							]}
						/>
						{/* 原配置：图例文字较大，动态布局
						<Legend
							wrapperStyle={{
								fontSize: availableTypes.length > 6 ? "10px" : "12px",
								lineHeight: availableTypes.length > 6 ? "1.2" : "1.5",
							}}
							iconType="rect"
							layout={availableTypes.length > 8 ? "vertical" : "horizontal"}
							align={availableTypes.length > 8 ? "right" : "center"}
							verticalAlign={availableTypes.length > 8 ? "middle" : "bottom"}
						/>
						修改记录：2025-06-27 20:01:40 - hayden - 图例放在顶部，文字调小
						修改记录：2025-06-27 20:01:40 - hayden - 修正为图例放在底部，文字调小
						修改记录：2025-06-27 20:30:21 - hayden - 进一步缩小图例文字到8px
						修改记录：2025-06-27 20:38:11 - hayden - 图例色块改为小圆形，坐标轴字体调小 */}
						<Legend
							wrapperStyle={{
								fontSize: "8px",
								lineHeight: "1.0",
							}}
							iconType="circle"
							iconSize={6}
							layout="horizontal"
							align="center"
							verticalAlign="bottom"
						/>
						{availableTypes.map((type) => (
							<Bar
								key={type}
								dataKey={type}
								fill={typeColorMap[type]}
								radius={[2, 2, 0, 0]}
								opacity={0.8}
								barSize={barSize}
							/>
						))}
					</BarChart>
				</ResponsiveContainer>
			</div>
		</div>
	);
}
