# 名册管理模块E2E测试用例（优化版）

## 测试环境准备
- 测试环境URL: http://***************:3000
- 登录凭据:
  - 邮箱输入框: #«R1ljrlpbl7»-form-item
  - 密码输入框: #«R25jrlpbl7»-form-item > input
  - 登录按钮: body > div.flex.min-h-screen.w-full.py-6 > div > div.container.flex.justify-center > main > div > form > button
  - 登录URL: http://***************:3000/auth/login?redirectTo=%2Fapp
- 测试数据: 准备不同格式的股东名册文件（DBF格式、Excel格式、ZIP压缩文件等）

## 模块概述
名册管理模块主要负责股东名册文件的上传、导入、存储和管理功能，包括单个/批量文件导入、数据校验、同期名册合并显示、删除操作等核心功能。

---

## 1. 核心功能测试

### 1.1 页面初始化和文件导入流程
**测试用例ID**: TC_NM_001
**测试描述**: 验证名册管理页面初始状态和完整的文件导入流程
**测试步骤**:
1. 登录系统并导航到名册管理页面
2. 观察页面初始状态
3. 点击"名册导入"按钮，选择一个有效的股东名册文件
4. 确认上传，观察校验过程弹窗
5. 等待导入完成，观察列表更新

**预期结果**:
- 页面初始状态：如果有数据显示名册列表（按期数降序排列），无数据显示"无数据"
- 列表包含字段：期数、总股本、总户数、信用户数、控股股东、前十股东、信用股东名册、导入日期、上传用户、操作
- 页面包含"名册导入"按钮
- 导入过程：弹窗显示文件计数、文件名称、进度条、百分比
- 校验成功后进度条开始显示导入过程
- 导入完成后弹窗自动关闭，名册列表自动刷新并按期数重新排序

### 1.2 批量文件导入和多格式支持
**测试用例ID**: TC_NM_002
**测试描述**: 验证批量文件导入和不同格式文件的支持
**测试步骤**:
1. 点击"名册导入"按钮，选择多个有效的股东名册文件（包含DBF、Excel格式）
2. 确认上传，观察整个批量导入过程
3. 测试沪市t123格式文件的导入
4. 测试ZIP压缩文件的导入

**预期结果**:
- 批量导入：弹窗按文件顺序依次显示校验和导入过程
- 每个文件显示当前文件序号、文件名称、进度条、百分比
- 校验成功的文件依次进行导入
- 最后一个文件导入完成后弹窗自动关闭
- 多格式支持：系统能够正确识别DBF、Excel、t123格式和ZIP压缩文件
- 所有成功导入的名册都显示在列表中，按期数排序

## 2. 异常处理和文件校验测试

### 2.1 文件校验失败处理
**测试用例ID**: TC_NM_003
**测试描述**: 验证文件校验失败时的跳过和返回功能
**测试步骤**:
1. 点击"名册导入"按钮，选择1个无效文件和1个有效文件
2. 确认上传，等待第一个文件校验失败
3. 在错误提示弹窗中点击"跳过"按钮，观察后续流程
4. 重复步骤1-2，在错误提示弹窗中点击"返回"按钮

**预期结果**:
- 第一个文件校验失败时显示具体错误信息
- 点击"跳过"：系统开始校验和导入下一个有效文件，有效文件成功导入后列表刷新
- 点击"返回"：取消所有文件的导入，弹窗关闭，返回名册列表页面
- 只有成功导入的文件显示在列表中

### 2.2 无效文件格式和数据校验
**测试用例ID**: TC_NM_004
**测试描述**: 验证系统对无效文件格式和数据格式的校验能力
**测试步骤**:
1. 尝试上传非支持格式文件（如txt、jpg等）
2. 尝试上传损坏的文件或空文件
3. 上传缺少必要字段的名册文件
4. 上传字段格式不正确的名册文件
5. 观察系统反应和错误提示

**预期结果**:
- 系统能够识别并拒绝无效文件格式
- 显示明确的错误提示信息（如"关键数据缺失"、"数据不符合规范"等）
- 提供"跳过"和"返回"选项
- 错误提示信息清晰明了，不会导致系统崩溃或异常

## 3. 同期名册合并功能测试

### 3.1 同期名册合并显示和期数识别
**测试用例ID**: TC_NM_005
**测试描述**: 验证同期不同类型名册的合并显示功能和期数识别准确性
**测试步骤**:
1. 导入同一期数的第一个名册文件
2. 导入同一期数的第二个不同类型名册文件
3. 观察列表显示效果和合并情况
4. 上传不同期数命名格式的文件，观察系统期数识别结果

**预期结果**:
- 同期名册自动合并在一行显示
- 合并字段：期数、总股本、总户数、新户户数、控股股东、前十股东、信用股东、操作
- 分开显示字段：名册、导入、用户（显示多个值）
- 系统能够准确识别文件名中的期数信息，通过文件名称中的期数进行识别和合并
- 相同期数的文件自动合并显示，不同期数的文件分别显示
- 期数识别错误时有适当提示

## 4. 删除功能测试

### 4.1 单个名册删除功能
**测试用例ID**: TC_NM_006
**测试描述**: 验证单个名册的删除功能和取消操作
**测试步骤**:
1. 在名册列表中找到目标名册
2. 点击操作列的"删除"按钮，观察按钮变化
3. 再次点击红色垃圾桶图标，观察删除结果
4. 重复步骤1-2，然后点击垃圾桶以外的其他地方，观察取消效果

**预期结果**:
- 第一次点击"删除"后，按钮变成红色垃圾桶线框图
- 第二次点击垃圾桶后，系统执行删除操作
- 删除成功后列表自动刷新，列表重新按期数顺序排列，被删除的名册不再显示
- 取消操作：点击垃圾桶以外的地方后，红色垃圾桶变回"删除"按钮，名册未被删除

### 4.2 批量删除和同期名册删除
**测试用例ID**: TC_NM_007
**测试描述**: 验证批量删除名册功能和同期名册删除处理
**测试步骤**:
1. 通过点击列表选择框选择多个名册
2. 点击"批量删除"按钮，观察按钮变化
3. 再次点击红色垃圾桶图标，观察删除结果
4. 测试同期名册删除：找到合并显示的同期名册行，执行删除操作
5. 测试批量删除中的同期名册处理

**预期结果**:
- 能够通过选择框选择多个名册
- 第一次点击"批量删除"后，按钮变成红色垃圾桶
- 第二次点击垃圾桶后，系统执行批量删除操作
- 删除成功后列表自动刷新，所有被选中的名册都不再显示
- 同期名册删除：删除操作会同时删除该期数的所有名册
- 同期名册使用一个共用的勾选框，批量删除时同期的所有名册都被删除

## 5. 边界条件和性能测试

### 5.1 边界条件和基础性能
**测试用例ID**: TC_NM_008
**测试描述**: 验证边界条件处理和基础性能要求
**测试步骤**:
1. 进入无名册数据的页面，观察空数据状态显示
2. 测试分页功能（如果有超过20条记录）
3. 观察列表排序功能（导入新名册后的排序变化）
4. 尝试导入大文件，观察导入过程的性能表现
5. 导入包含特殊字符的名册文件，检查显示效果

**预期结果**:
- 空数据状态：页面中间显示"无数据"，"名册导入"按钮正常显示
- 分页功能：默认每页显示20条数据，可以通过前后按钮切换页数
- 列表排序：初始按期数降序排列，新导入名册后自动重新排序
- 大文件处理：系统能够正常处理大文件，进度条正确显示导入进度
- 特殊字符：系统能够正确导入和显示特殊字符，数据完整性得到保证

## 6. 安全性和兼容性测试

### 6.1 文件上传安全和兼容性
**测试用例ID**: TC_NM_009
**测试描述**: 验证文件上传安全性和浏览器兼容性
**测试步骤**:
1. 尝试上传可执行文件和超大文件
2. 测试文件名特殊字符处理
3. 在Chrome、Firefox、Edge浏览器中测试核心功能
4. 使用开发者工具模拟移动端测试

**预期结果**:
- 系统能够正确识别和拒绝危险文件类型
- 文件大小限制生效，文件名特殊字符得到正确处理
- 主流浏览器中功能正常，UI展示基本一致
- 移动端布局适配良好，关键功能可正常使用

## 测试执行建议

### 测试优先级
1. **P0 (高优先级)**: TC_NM_001-TC_NM_007 (核心功能流程)
2. **P1 (中优先级)**: TC_NM_008 (边界条件和性能)
3. **P2 (低优先级)**: TC_NM_009 (安全性和兼容性)

### 测试数据要求
- **标准格式名册文件**: 包含完整股东信息的DBF和Excel文件
- **多格式文件**: 沪市t123格式文件、ZIP压缩文件
- **异常测试数据**: 格式错误文件、数据缺失文件、特殊字符文件
- **同期不同类型文件**: 相同期数的不同类型名册文件
- **安全测试数据**: 可执行文件、超大文件

### 自动化建议
- 核心功能测试（TC_NM_001-TC_NM_007）建议实现自动化
- 使用Playwright进行E2E自动化，配置特定的登录凭据
- 重点关注文件上传、导入流程和删除功能的自动化测试

### 测试环境
- 使用指定的测试环境URL和登录凭据
- 准备独立的测试数据库
- 确保测试数据的一致性和可重复性

