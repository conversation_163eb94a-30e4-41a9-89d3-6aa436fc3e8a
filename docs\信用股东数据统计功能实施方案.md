# 信用股东数据统计功能实施方案

## 文档信息
- **创建时间**: 2025-06-13 11:37:11
- **版本**: V.1.1
- **修订日期**: 2025-06-13
- **作者**: Augment Agent
- **最新修订**: 2025-06-13 11:45:00 - 更新为前端计算模式

## 1. 需求概述

### 1.1 核心目标
- 在股东分析中准确统计并展示公司的"信用总户数"和"信用总持股"数据
- 明确沪市使用T3名册（前200股东版本）时，信用股东相关数据的计算逻辑
- 确保后端数据模型（CompanyInfo）能存储这些统计结果
- 实现T3名册全量数据替换逻辑

### 1.2 功能需求
1. **新增数据字段**：
   - `marginAccounts`（信用总户数）：公司所有信用账户的总数量
   - `marginShares`（信用总持股）：公司所有信用账户合计持有的股份总数量

2. **数据计算模式**：
   - **前端计算**：前端在上传前预处理全量名册数据，计算`marginAccounts`和`marginShares`
   - **后端接收**：后端直接接收前端计算好的统计数据，无需重新计算
   - **数据说明**：由于前端有预处理逻辑（取前200个股东数据），后端收到的股东明细为前200条，但统计数据基于全量数据

3. **T3名册特殊处理**：
   - 识别文件名中的"all"标识（如：t36053380320241231tall.c31.xls）
   - 全量数据覆盖替换前200股东版本的信用数据

## 2. 技术实施方案

### 2.1 数据库变更

#### 2.1.1 CompanyInfo模型字段新增
**文件位置**: `packages/database/prisma/schema.prisma`

**变更内容**:
```prisma
model CompanyInfo {
  // ... 现有字段 ...
  
  // 新增信用股东统计字段
  marginAccounts         Int?                @default(0) // 信用总户数，默认为0
  marginShares           Decimal?            @default(0) @db.Decimal(17, 2) // 信用总持股，默认为0
  
  // ... 其他字段 ...
}
```

**字段说明**:
- `marginAccounts`: 存储信用账户总数，使用Int类型，允许为空，默认值为0
- `marginShares`: 存储信用账户总持股数，使用Decimal类型，精度17位小数2位，允许为空，默认值为0

### 2.2 文件名识别逻辑优化

#### 2.2.1 T3名册全量标识识别
**文件位置**: `packages/api/src/routes/shareholder-registry/lib/utils.ts`

**功能增强**: 在`detectRegistryTypeByFileName`函数中增加全量T3名册识别逻辑

**实现逻辑**:
```typescript
/**
 * 检测T3名册是否为全量数据
 * @param fileName 文件名
 * @returns 是否为全量T3名册
 */
export function isT3FullRegistry(fileName: string): boolean {
  const lowerFileName = fileName.toLowerCase();
  return lowerFileName.startsWith('t3') && lowerFileName.includes('all');
}
```

### 2.3 信用股东数据处理逻辑

#### 2.3.1 前端数据预处理
**处理位置**: 前端上传组件

**处理逻辑**:
1. **全量数据分析**: 前端读取完整的名册文件（05名册或T3名册）
2. **信用数据统计**:
   - 深市05名册：统计所有标记为信用账户的股东人数和持股数量
   - 沪市T3名册：统计XYCYSL字段大于0的股东户数和持股总量
3. **数据截取**: 取前200个股东数据用于上传
4. **统计数据携带**: 将计算好的`marginAccounts`和`marginShares`包含在上传请求中

#### 2.3.2 后端数据接收处理
**实现位置**: `packages/api/src/routes/shareholder-registry/handlers/upload-05.ts` 和 `upload-t3.ts`

**处理逻辑**:
1. **直接接收**: 从请求数据中获取前端计算的`marginAccounts`和`marginShares`
2. **数据验证**: 验证统计数据的合理性（非负数、数据类型等）
3. **存储入库**: 将统计数据存储到CompanyInfo表中

### 2.4 T3名册替换逻辑

#### 2.4.1 替换场景识别
**条件**:
- 同一组织（organizationId）
- 同一公司（companyCode）
- 同一报告期（registerDate）
- 先上传T3前200版本，后上传T3全量版本（文件名包含"all"）

#### 2.4.2 替换处理逻辑
**实现位置**: `packages/api/src/routes/shareholder-registry/upload.ts`

**处理流程**:
1. 检测当前上传文件是否为T3全量名册（文件名包含"all"）
2. 查询是否存在同期T3前200版本名册
3. 如存在，执行数据替换逻辑：
   - 删除原有T3前200版本的股东数据
   - 使用前端计算的全量统计数据更新CompanyInfo
   - 更新CompanyInfo中的marginAccounts和marginShares为全量数据
   - 保存新的前200股东明细数据

## 3. 实施步骤

### 3.1 第一阶段：数据库变更
1. **修改Prisma Schema**
   - 在CompanyInfo模型中添加marginAccounts和marginShares字段
   - 生成并执行数据库迁移

2. **验证数据库变更**
   - 确认字段正确添加
   - 验证默认值设置

### 3.2 第二阶段：后端逻辑实现
1. **文件名识别逻辑增强**
   - 实现T3全量名册识别函数
   - 更新文件名检测逻辑

2. **请求数据结构扩展**
   - 扩展上传请求接口，支持接收marginAccounts和marginShares字段
   - 添加数据验证逻辑

3. **信用数据接收逻辑实现**
   - 在upload-05.ts中实现接收并验证前端计算的信用数据
   - 在upload-t3.ts中实现接收并验证前端计算的信用数据

4. **T3替换逻辑实现**
   - 在upload.ts中实现T3全量替换检测
   - 实现数据覆盖替换逻辑

### 3.3 第三阶段：数据入库逻辑
1. **CompanyInfo数据更新**
   - 在名册处理完成后直接存储前端计算的信用数据
   - 确保事务一致性

2. **数据验证与错误处理**
   - 添加前端传入信用数据的合理性验证
   - 添加数据类型和范围检查
   - 完善日志记录

### 3.4 第四阶段：前端接口适配
1. **上传请求数据结构调整**
   - 在companyInfo对象中添加marginAccounts和marginShares字段
   - 确保前端计算逻辑正确实现
   深市05名册(CGSL字段取数值大的前200股东版本) - 信用数据计算逻辑
- 信用总户数 (marginAccounts):
  - 直接通过统计深市05名册中所有标记为信用账户的股东人数获得。
  - 例如，若名册中有1188个信用股东，则 marginAccounts = 1188。
- 信用总持股 (marginShares):
  - 通过累加深市05名册中所有信用账户的持股数量（对应字段，如 CGSL）获得。
沪市T3名册 (前200股东版本) - 信用数据计算逻辑（替换/明确）
- 信用总户数 (marginAccounts):
  - 统计上传的沪市T3名册中，字段 XYCYSL (投资者信用证券账户持有数量) 大于 0 的股东户数。
  - 重要： 由于T3名册只包含前200名股东，此户数代表的是这前200名股东中的信用账户数量，而非公司所有信用账户数量。
- 信用总持股 (marginShares):
  - 通过累加沪市T3名册中所有股东的 XYCYSL (投资者信用证券账户持有数量) 字段的值获得。
  - 重要： 此持股总数代表的是这前200名股东通过信用账户持有的股份数量，而非公司所有信用账户的总持股。

2. **前端计算逻辑实现**
   - 实现05名册信用数据统计算法
   - 实现T3名册信用数据统计算法
   - 添加计算结果验证

## 4. 数据流程说明

### 4.1 前端数据处理流程
1. **文件读取**: 前端读取完整的名册文件（05或T3）
2. **全量分析**: 分析所有股东数据，识别信用账户
3. **统计计算**:
   - 计算信用总户数（marginAccounts）
   - 计算信用总持股（marginShares）
4. **数据截取**: 取前200个股东数据用于上传
5. **请求构建**: 将统计数据和前200股东数据一起打包上传

### 4.2 后端数据处理流程
1. **请求接收**: 接收包含统计数据的上传请求
2. **数据验证**: 验证marginAccounts和marginShares的合理性
3. **存储处理**:
   - 存储前200股东明细数据
   - 存储前端计算的统计数据到CompanyInfo
4. **替换逻辑**: 如为T3全量数据，执行替换逻辑

