/**
 * 通用股东名册文件解析器
 * 支持多种文件格式的解析，包括DBF、XLS、XLSX和ZIP
 * 支持01和05名册的解析和合并
 * 支持t1/t2/t3名册的解析和合并
 *
 * 主要功能：
 * 1. 根据文件类型选择适当的解析方法
 * 2. 对不同格式的文件进行标准化处理，统一输出格式
 * 3. 支持ZIP文件的解压和内部文件解析
 * 4. 支持Excel文件(XLS/XLSX)的表格数据提取
 * 5. 支持01和05名册的字段映射和数据合并
 * 6. 支持t1/t2/t3名册的字段映射和数据合并
 * 
 * 使用场景：
 * - 用于解析多种格式的股东名册文件
 * - 支持证券公司提供的不同格式的股东名册
 * - 兼容现有系统的数据导入流程
 * 
 * @update 2025年06月05日 - 添加对t1/t2/t3名册的支持，实现Excel文件解析和预处理
 * @update 2025年06月03日 - 确保解析结果保留原始字段名，不转换为数据库字段名。
 * 后端已实现字段映射机制，会根据名册类型自动将原始字段名映射到数据库字段。
 * @update 2025年06月03日 - 优化t1名册解析流程，确保选择文件后立即进行预处理
 */

import { parseDBFFile } from "./dbf-parser";
import type { ShareholderRegistryParseResult } from "./dbf-parser/common";
import { validateDBFFileName } from "./dbf-parser/common";
import * as ExcelParser from "./excel-parser";
import { validateExcelFileName } from "./excel-parser/common";
import { RegistryType, STRICT_FILENAME_VALIDATION, FILENAME_ERROR_MESSAGES } from "./config";
import { preprocessT1Records } from "./excel-parser/type-t1";
import { preprocessT2Records } from "./excel-parser/type-t2";
import { preprocessT3Records } from "./excel-parser/type-t3";
import { detectExcelRegistryType } from "./excel-parser/common";
import { parseZipFile as zipParseZipFile, isZipFile as zipIsZipFile } from "./zip-parser";


/**
 * 检查文件类型是否为支持的格式
 * 
 * @param fileName 文件名
 * @returns 是否为支持的文件格式
 */
export function isSupportedFileType(fileName: string): boolean {
  const lowercaseName = fileName.toLowerCase();
  return lowercaseName.endsWith(".dbf") || 
         lowercaseName.endsWith(".xls") || 
         lowercaseName.endsWith(".xlsx") || 
         lowercaseName.endsWith(".zip");
}

// 注释：isZipFile函数已移至zip-parser.ts中

/**
 * 检查文件类型是否为DBF文件
 *
 * @param fileName 文件名
 * @returns 是否为DBF文件
 */
function isDbfFile(fileName: string): boolean {
  const lowercaseName = fileName.toLowerCase();
  return lowercaseName.endsWith(".dbf");
}

// 注释：parseZipFile函数已移至zip-parser.ts中

/**
 * 统一文件名验证入口
 * 根据文件类型调用相应的验证函数
 *
 * @param file 要验证的文件
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 */
export function validateFileName(file: File): {
  isValid: boolean;
  registryType?: string;
  error?: string;
  suggestion?: string;
} {
  const fileName = file.name;

  if (!STRICT_FILENAME_VALIDATION) {
    return { isValid: true };
  }

  // 根据文件扩展名选择验证方式
  if (isDbfFile(fileName)) {
    return validateDBFFileName(fileName);
  }

  if (ExcelParser.isExcelFile(fileName)) {
    const result = validateExcelFileName(fileName);
    return {
      isValid: result.isValid,
      registryType: result.registryType,
      error: result.error,
      suggestion: result.suggestion
    };
  }

  if (zipIsZipFile(fileName)) {
    // 导入ZIP验证函数
    const { validateZipFileName } = require('./zip-parser');
    const result = validateZipFileName(fileName);
    return {
      isValid: result.isValid,
      registryType: result.registryType,
      error: result.error,
      suggestion: result.expectedInternalFileName ? `期望的内部文件：${result.expectedInternalFileName}` : undefined
    };
  }

  return {
    isValid: false,
    error: FILENAME_ERROR_MESSAGES.UNSUPPORTED_TYPE,
    suggestion: '请上传c01、c05、t1、t2、t3格式的文件或对应的ZIP压缩包'
  };
}

/**
 * 从文件名解析公司代码和报告日期
 * 从文件名中提取公司代码和报告日期信息
 *
 * @param fileName 文件名，标准格式为DQMC01_001339_20240930.DBF或DQMC05_001339_20240930.DBF或t1XXXXXXxxyyyymmddall.mdd.xls
 * @returns 解析结果对象，包含公司代码和报告日期
 */
export function parseFileName(fileName: string): {
	companyCode?: string;
	registerDate?: string;
} {
	try {
		// 检查是否为t1/t2/t3名册
		if (ExcelParser.isTSeriesRegistry(fileName)) {
			// t1/t2/t3名册文件名格式为t1XXXXXXxxyyyymmddall.mdd.xls
			// 提取公司代码（第3到8个字符）
			const nameWithoutExt = fileName.split('.')[0];
			let companyCode: string | undefined;
			if (nameWithoutExt.length >= 8) {
				companyCode = nameWithoutExt.substring(2, 8);
			}
			
			// 提取日期（第10到18个字符）
			let registerDate: string | undefined;
			if (nameWithoutExt.length >= 18) {
				const dateStr = nameWithoutExt.substring(10, 18);
				
				// 解析日期
				const year = dateStr.substring(0, 4);
				const month = dateStr.substring(4, 6);
				const day = dateStr.substring(6, 8);
				
				// 验证日期有效性
				const date = new Date(Number(year), Number(month) - 1, Number(day));
				if (
					date.getFullYear() === Number(year) &&
					date.getMonth() === Number(month) - 1 &&
					date.getDate() === Number(day)
				) {
					registerDate = `${year}-${month}-${day}`;
				}
			}
			
			return { companyCode, registerDate };
		}
		
		// 01/05名册文件名格式为DQMC01_001339_20240930.DBF
		const nameParts = fileName.split("_");

		if (nameParts.length >= 3) {
			// 提取公司代码，通常是文件名中的第二部分
			const companyCode = nameParts[1]; // 例如：001339

			// 提取日期部分，通常是文件名中的第三部分（去掉扩展名）
			let dateStr = nameParts[2]; // 例如：20240930.DBF
			// 移除可能的文件扩展名
			dateStr = dateStr.split(".")[0]; // 例如：20240930

			// 尝试从不同格式中解析日期
			let year: string;
			let month: string;
			let day: string;

			// 标准8位格式 YYYYMMDD
			if (dateStr.length === 8) {
				year = dateStr.substring(0, 4); // 2024
				month = dateStr.substring(4, 6); // 09
				day = dateStr.substring(6, 8); // 30
			}
			// 处理异常格式，例如9位或其他格式
			else if (dateStr.length > 8) {
				// 尝试仍然提取前8位作为YYYYMMDD
				const firstEight = dateStr.substring(0, 8);
				year = firstEight.substring(0, 4); // 2024
				month = firstEight.substring(4, 6); // 09
				day = firstEight.substring(6, 8); // 30
			} else {
				return { companyCode };
			}

			// 验证提取的日期是否有效
			const date = new Date(Number(year), Number(month) - 1, Number(day));
			if (
				date.getFullYear() !== Number(year) ||
				date.getMonth() !== Number(month) - 1 ||
				date.getDate() !== Number(day)
			) {
				// 日期无效
				return { companyCode };
			}

			// 转换为YYYY-MM-DD格式，与DBF文件中DHHM字段的格式一致
			const registerDate = `${year}-${month}-${day}`;

			return { companyCode, registerDate };
		}

		// 文件名格式不符合预期
		return {};
	} catch (error) {
		// 解析文件名失败
		return {};
	}
}

/**
 * 解析股东名册文件
 * 根据文件类型选择适当的解析方法
 *
 * @param file 要解析的文件
 * @returns 解析结果
 * <AUTHOR>
 * @update 2025-06-23 16:01:13 - 添加严格文件名验证逻辑
 * @update 2025年06月04日 13:13:49 修复名册类型检测逻辑，确保正确识别t3名册
 * @update 2025年06月03日 19:58:30 添加对t2和t3名册的预处理支持，解决中文乱码问题
 */
export async function parseShareholderRegistryFile(file: File): Promise<ShareholderRegistryParseResult> {
  try {
    // 在严格模式下，首先进行文件名验证
    if (STRICT_FILENAME_VALIDATION) {
      const validation = validateFileName(file);
      if (!validation.isValid) {
        return {
          success: false,
          fileName: file.name,
          error: {
            type: "FILE_ERROR",
            message: validation.error || "文件名格式不正确",
            details: validation.suggestion ? [validation.suggestion] : undefined,
          },
        };
      }
    }

    // 根据文件类型选择解析方法
    if (isDbfFile(file.name)) {
      return await parseDBFFile(file);
    }
    
    if (ExcelParser.isExcelFile(file.name)) {
      // 检测Excel文件的名册类型
      const registryType = detectExcelRegistryType(file.name);

      // 对于t1名册，使用特定的解析和预处理流程
      if (registryType === RegistryType.TYPE_T1) {
        const result = await ExcelParser.parseExcelFile(file);

        // 如果解析成功且有记录，进行预处理（合并重复记录、排序）
        if (result.success && result.records && result.records.length > 0) {
          // 预处理：合并重复记录
          const mergedRecords = preprocessT1Records(result.records);

          // 更新结果中的记录
          result.records = mergedRecords;
          result.recordCount = mergedRecords.length;
        }

        return result;
      }
      
      // 对于t2名册，添加特定的解析和预处理流程
      if (registryType === RegistryType.TYPE_T2) {
        const result = await ExcelParser.parseExcelFile(file);

        // 如果解析成功且有记录，进行预处理（合并重复记录、排序）
        if (result.success && result.records && result.records.length > 0) {
          // 预处理：合并重复记录
          const mergedRecords = preprocessT2Records(result.records);

          // 更新结果中的记录
          result.records = mergedRecords;
          result.recordCount = mergedRecords.length;
        }

        return result;
      }

      // 对于t3名册，添加特定的解析和预处理流程
      if (registryType === RegistryType.TYPE_T3) {
        const result = await ExcelParser.parseExcelFile(file);

        // 如果解析成功且有记录，进行预处理（合并重复记录、排序）
        if (result.success && result.records && result.records.length > 0) {
          // 预处理：合并重复记录
          const mergedRecords = preprocessT3Records(result.records);

          // 更新结果中的记录
          result.records = mergedRecords;
          result.recordCount = mergedRecords.length;
        }

        return result;
      }
      
      // 其他类型的Excel文件使用通用解析方法
      return await ExcelParser.parseExcelFile(file);
    }
    
    if (zipIsZipFile(file.name)) {
      return await zipParseZipFile(file);
    }
    
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: FILENAME_ERROR_MESSAGES.UNSUPPORTED_TYPE,
      },
    };
  } catch (error) {
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: error instanceof Error ? error.message : "解析文件失败",
      },
    };
  }
} 

/**
 * 通过文件名检测名册类型
 *
 * 检测逻辑:
 * 1. 统一转换为小写进行判断
 * 2. 使用正则表达式匹配文件名开头的t1、t2、t3模式
 * 3. 如果正则匹配失败，则使用includes方法作为备选方案
 *
 * <AUTHOR>
 * @created 2025-06-25 17:42:20
 * @param fileName 文件名
 * @return 名册类型: "01" | "05" | "t1" | "t2" | "t3" | "unknown"
 */
export function detectRegistryType(fileName: string): "01" | "05" | "t1" | "t2" | "t3" | "unknown" {
  // 统一转换为小写进行判断
  const lowerFileName = fileName.toLowerCase();

  // 使用更严格的正则表达式匹配文件名开头的t1、t2、t3模式
  // 确保t3开头的文件不会被错误地识别为t2
  if (lowerFileName.startsWith('t3')) {
    return "t3";
  }

  if (lowerFileName.startsWith('t2')) {
    return "t2";
  }

  if (lowerFileName.startsWith('t1')) {
    return "t1";
  }

  // 如果上面的精确匹配失败，使用传统的关键字匹配作为备选方案
  if (lowerFileName.includes("dqmc01")) {
    return "01";
  }

  if (lowerFileName.includes("dqmc05")) {
    return "05";
  }

  return "unknown";
}