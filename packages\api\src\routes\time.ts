import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../middleware/auth";

/**
 * Base64编码函数
 * 将时间戳数据编码为Base64字符串，去除等号填充
 * 支持服务器端和客户端环境
 * @param data 要编码的数据对象
 * @returns 不含填充等号的Base64字符串
 */
function encodeTimeData(data: { timestamp: number; datetime: string }): string {
	// 将数据转为JSON字符串
	const jsonString = JSON.stringify(data);
	// 判断运行环境并选择合适的Base64编码方法
	let base64String: string;

	// 检查是否在Node.js环境
	if (typeof Buffer !== 'undefined') {
		// Node.js环境使用Buffer
		base64String = Buffer.from(jsonString).toString('base64');
	} else {
		// 浏览器环境使用btoa
		base64String = btoa(jsonString);
	}
	
	// 移除填充等号并返回
	return base64String.replace(/=/g, '');
}

/**
 * 服务器时间API路由
 * 提供服务器的当前时间戳，用于客户端与服务器时间同步
 * 返回的时间戳和日期时间字符串经过Base64编码处理，不直接暴露原始值
 * 
 * @security
 * - 需要用户登录才能访问
 * - 用于确保客户端和服务器时间同步，防止时间戳攻击
 * - 主要用于加密通信中的时间戳验证
 * 
 * @returns
 * - data: Base64编码的时间数据（不含填充等号）
 */
export const timeRouter = new Hono().get(
	"/server-time",
	authMiddleware,
	describeRoute({
		tags: ["Time"],
		summary: "Server time",
		description: "Returns the current server timestamp encoded in base64",
		security: [{ bearerAuth: [] }],
		responses: {
			200: {
				description: "OK",
				content: {
					"application/json": {
						schema: {
							type: "object",
							properties: {
								data: {
									type: "string",
									description:
										"Base64 encoded server time data, without padding equals signs",
								},
							},
						},
					},
				},
			},
			401: {
				description: "Unauthorized - User not logged in",
			},
		},
	}),
	(c) => {
		const now = new Date();
		// 创建时间数据对象
		const timeData = {
			timestamp: now.getTime(),
			datetime: now.toISOString(),
		};
		// 进行Base64编码
		const encodedData = encodeTimeData(timeData);
		
		// 返回编码后的数据
		return c.json({
			data: encodedData,
		});
	}
);