import { getSession } from "@saas/auth/lib/server"; // 用于获取用户会话信息
// import { FileIcon } from "lucide-react"; // 图标组件
import { getTranslations } from "next-intl/server"; // 获取翻译
import { redirect } from "next/navigation"; // 页面重定向功能
// import { MeetingMenu } from "@saas/meeting/components/MeetingMenu"; // 股东菜单组件
import type { meetingMenuTranslationKey } from "@saas/meeting/components/MeetingMenu"; // 股东菜单类型
// import { HorizontalSidebarContentLayout } from "@saas/shared/components/HorizontalSidebarContentLayout";
import { MeetingBreadcrumb } from "@saas/meeting/components/MeetingBreadcrumb";
// 定义路由参数类型
type Params = Promise<{
	organizationSlug: string; // 组织标识符
}>;

// 定义布局组件的Props接口
interface MeetingLayoutProps {
	children: React.ReactNode; // 子组件
	params: Params; // 路由参数
}

/**
 * 会议模块的布局组件
 * 提供水平导航栏和内容区域的布局结构
 */
export default async function MeetingLayout({ 
	children, 
	params 
}: MeetingLayoutProps) {
	// 获取用户会话信息
	const session = await getSession(); 
	// 获取翻译
	const t = await getTranslations("meeting.menu");

	// 如果用户未登录，重定向到登录页面
	if (!session) {
		return redirect("/auth/login");
	}
	
	// 解构获取组织标识符
	const { organizationSlug } = await params;
	
	// 在organizationSlug下简化路径
	const basePath = `/app/${organizationSlug}/meeting`;

	// 定义菜单项配置
	const menuItems = [
		{
			// titleKey: "title" as meetingMenuTranslationKey, // 菜单组标题 - 使用国际化
			items: [
				{
					titleKey: "meeting-list" as meetingMenuTranslationKey, // 会议列表 - 使用国际化
					href: `${basePath}/list`, 
				},
				{
					titleKey: "meeting-personnel" as meetingMenuTranslationKey, // 会议管理 - 使用国际化
					href: `${basePath}/personnel`,
				},
				{
					titleKey: "meeting-report" as meetingMenuTranslationKey, // 会议报告 - 使用国际化
					href: `${basePath}/report`,
				},
			],
		},
	];

	// 渲染布局结构，使用水平导航布局
	return (
		<>
			{/* 添加顶部面包屑导航，启用返回上一页功能 */}
			{/* 注意：showBackLink在服务端渲染时不会显示返回按钮，直到客户端渲染完成 */}
			<MeetingBreadcrumb organizationSlug={organizationSlug} />
			{children}
			{/* <HorizontalSidebarContentLayout
				sidebar={<MeetingMenu menuItems={menuItems} />}
			>
				{children}
			</HorizontalSidebarContentLayout> */}
		</>
	);
} 