/**
 * 工具函数集合
 */

/**
 * 格式化日期
 * @param dateString ISO格式的日期字符串或Date对象
 * @param options 格式化选项
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  dateString: string | Date,
  options: {
    format?: 'full' | 'date' | 'time';
    locale?: string;
  } = {}
): string {
  const { format = 'full', locale = 'zh-CN' } = options;
  
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  
  // 检查日期是否有效
  if (Number.isNaN(date.getTime())) {
    return '无效日期';
  }
  
  try {
    switch (format) {
      case 'date':
        return date.toLocaleDateString(locale, {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        });
      case 'time':
        return date.toLocaleTimeString(locale, {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
      default:
        return date.toLocaleString(locale, {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
    }
  } catch (error) {
    return String(date);
  }
}

/**
 * 格式化数字
 * @param value 要格式化的数字
 * @param options 格式化选项
 * @returns 格式化后的数字字符串
 */
export function formatNumber(
  value: string | number,
  options: {
    decimals?: number;
    locale?: string;
  } = {}
): string {
  const { decimals = 2, locale = 'zh-CN' } = options;
  
  // 将字符串转换为数字
  const number = typeof value === 'string' ? Number.parseFloat(value) : value;
  
  // 检查是否为有效数字
  if (Number.isNaN(number)) {
    return '0';
  }
  
  try {
    return number.toLocaleString(locale, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    });
  } catch (error) {
    return String(number);
  }
}

/**
 * 生成伪随机ID
 * @returns 随机ID字符串
 */
export function generateId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

/**
 * 深度克隆对象
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj));
} 