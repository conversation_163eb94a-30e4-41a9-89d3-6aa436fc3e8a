/**
 * 更新投资人联系人路由
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建联系人更新接口
 * @description 更新投资人联系人信息，支持部分字段更新，自动记录更新人和更新时间
 */
import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { UpdateInvestorContactSchema } from "../lib/validators";

export const contactsUpdateRouter = new Hono().post(
  "/update",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = UpdateInvestorContactSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { contactId, organizationId, name, phoneNumber, email, address, remarks } = validationResult.data;

      // 验证联系人是否存在且属于指定组织
      const existingContact = await db.investorContact.findFirst({
        where: { contactId, organizationId }
      });

      if (!existingContact) {
        throw new HTTPException(404, { message: "联系人不存在" });
      }

      // 更新联系人信息
      const updatedContact = await db.investorContact.update({
        where: { contactId },
        data: {
          ...(name !== undefined ? { name } : {}),
          ...(phoneNumber !== undefined ? { phoneNumber } : {}),
          ...(email !== undefined ? { email } : {}),
          ...(address !== undefined ? { address } : {}),
          ...(remarks !== undefined ? { remarks } : {}),
          updatedBy: user.id,
        }
      });

      c.set("response", {
        code: 200,
        message: "联系人更新成功",
        data: {
          contactId: updatedContact.contactId
        }
      });
      return;

    } catch (error) {
      if (error instanceof HTTPException) {
        c.set("response", {
          code: error.status,
          message: error.message,
          data: null
        });
        return;
      }
      
      console.error("投资人管理API错误:", error);
      c.set("response", {
        code: 500,
        message: "服务器内部错误",
        data: null
      });
      return;
    }
  }
);
