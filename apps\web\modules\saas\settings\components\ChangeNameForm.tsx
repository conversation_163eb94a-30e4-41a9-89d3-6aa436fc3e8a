"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { useSession } from "@saas/auth/hooks/use-session";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

// 定义表单验证规则
const formSchema = z.object({
	name: z.string().min(2), // 名称最少2个字符
});

// 从验证规则中推导出表单类型
type FormSchema = z.infer<typeof formSchema>;

export function ChangeNameForm() {
	// 获取用户信息和重新加载会话的方法
	const { user, reloadSession } = useSession();
	// 提交状态
	const [submitting, setSubmitting] = useState(false);
	// 获取国际化翻译函数
	const t = useTranslations();

	// 初始化表单
	const form = useForm<FormSchema>({
		resolver: zodResolver(formSchema), // 使用zod验证
		defaultValues: {
			name: user?.name ?? "", // 默认值为当前用户名
		},
	});

	// 表单提交处理
	const onSubmit = form.handleSubmit(async ({ name }) => {
		setSubmitting(true);

		// 调用API更新用户名
		await authClient.updateUser(
			{ name },
			{
				// 成功回调
				onSuccess: () => {
					// 显示成功提示
					toast.success(
						t("settings.account.changeName.notifications.success"),
					);

					// 重新加载会话并重置表单
					reloadSession();
					form.reset({
						name,
					});
				},
				// 错误回调
				onError: () => {
					// 显示错误提示
					toast.error(
						t("settings.account.changeName.notifications.error"),
					);
				},
				// 请求完成回调
				onResponse: () => {
					setSubmitting(false);
				},
			},
		);
	});

	return (
		<SettingsItem title={t("settings.account.changeName.title")}>
			<form onSubmit={onSubmit}>
				{/* 名称输入框 */}
				<Input type="text" {...form.register("name")} />

				<div className="mt-4 flex justify-end">
					{/* 提交按钮 */}
					<Button
						type="submit"
						loading={submitting}
						// 当表单未修改或验证未通过时禁用按钮
						disabled={
							!(
								form.formState.isValid &&
								form.formState.dirtyFields.name
							)
						}
					>
						{t("settings.save")}
					</Button>
				</div>
			</form>
		</SettingsItem>
	);
}
