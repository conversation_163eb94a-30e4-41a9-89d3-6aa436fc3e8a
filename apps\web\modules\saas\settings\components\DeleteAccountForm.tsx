"use client";

import { authClient } from "@repo/auth/client";
import { useSession } from "@saas/auth/hooks/use-session";

import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { useRouter } from "@shared/hooks/router";
import {
	AlertDialog,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Button } from "@ui/components/button";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { toast } from "sonner";

export function DeleteAccountForm() {
	const t = useTranslations();
	const { reloadSession } = useSession();
	const [deleting, setDeleting] = useState(false);
	const router = useRouter();
	const [showConfirmation, setShowConfirmation] = useState(false);
	const { user } = useSession();


	const onDelete = async () => {
		setDeleting(true);
		// 删除会议系统用户
		if (user?.id) {
			try {
				// 调用后端API删除腾讯会议系统中的用户
				const res = await fetch(`/api/meetings/users/${user.id}`, {
					method: "DELETE",
					headers: {
						"Content-Type": "application/json",
					},
				});
				// 检查删除结果
				if (!res.ok) {
					// 解析错误响应
					const data = await res.json();
					// 显示具体的错误信息
					toast.error(data?.error.error_info?.message || "Unknown error");
				}
				// 显示删除成功提示
				toast.success("会议系统用户删除成功");
			} catch (error) {
				// 处理网络或其他异常
				toast.error(error as string);
				setDeleting(false);
				return;
			}
		}
		// 删除系统账户
		await authClient.deleteUser(
			{},
			{
				onSuccess: () => {
					toast.success(
						t(
							"settings.account.deleteAccount.notifications.success",
						),
					);
					reloadSession();
					router.replace("/");
				},
				onError: () => {
					toast.error(
						t("settings.account.deleteAccount.notifications.error"),
					);
				},
				onResponse: () => {
					setDeleting(false);
				},
			},
		);

		
	};

	return (
		<>
			<SettingsItem
				danger
				title={t("settings.account.deleteAccount.title")}
				description={t("settings.account.deleteAccount.description")}
			>
				<div className="mt-4 flex justify-end">
					<Button
						variant="error"
						onClick={() => setShowConfirmation(true)}
					>
						{t("settings.account.deleteAccount.submit")}
					</Button>
				</div>
			</SettingsItem>

			<AlertDialog
				open={showConfirmation}
				onOpenChange={(open) => setShowConfirmation(open)}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle className="text-destructive">
							{t("settings.account.deleteAccount.title")}
						</AlertDialogTitle>
						<AlertDialogDescription>
							{t("settings.account.deleteAccount.confirmation")}
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>
							{t("common.confirmation.cancel")}
						</AlertDialogCancel>
						<Button
							variant="error"
							loading={deleting}
							onClick={async () => {
								await onDelete();
								setShowConfirmation(false);
							}}
						>
							{t("settings.account.deleteAccount.submit")}
						</Button>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
