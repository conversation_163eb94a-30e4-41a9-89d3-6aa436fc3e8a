import * as React from "react";

/**
 * StarIcon 组件
 * 用于展示"收藏投资人"图标，支持自定义 className。
 * @param {string} className - 额外的样式类名
 * @returns {JSX.Element}
 * <AUTHOR>
 * 创建时间: 2025-07-07
 */
export function StarIcon({ className = "" }: { className?: string }) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      aria-label="星链投资人"
      width="1em"
      height="1em"
    >
      <title>星链投资人</title>
      {/* 圆形头部 - 人物部分使用当前颜色，粗细与其他图标一致 */}
      <circle cx="11" cy="8" r="4" strokeWidth="2.2" />
      {/* 半圆形身体 - 人物部分使用当前颜色，粗细与其他图标一致 */}
      <path d="M19 21v-1.5a4 4 0 0 0-4-4H8a4 4 0 0 0-3.5 4v5" strokeWidth="2.2" />
      {/* 右下角的大星星 - 标准五角星整体左移2.5个单位，上移1.1个单位，放大1.25倍，内部空白适应主题 */}
      <path
        className="fill-white dark:fill-black stroke-current"
        d="M18.3 12.7l1.86 3.72 4.092 0.62-2.976 2.852 0.744 3.968-3.72-1.984-3.72 1.984 0.744-3.968-2.976-2.852 4.092-0.62L18.3 12.7z"
        strokeWidth="1.9" //1.75
      />
    </svg>
  );
}
