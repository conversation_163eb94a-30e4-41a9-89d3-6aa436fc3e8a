# 模块API和Hook使用规范说明

**文档作者**: hayden
**创建时间**: 2025-07-04 15:49:40
**更新时间**: 2025-07-04 15:53:16
**文档版本**: v2.0

## 概述

本文档说明项目中所有模块的API封装和Hook使用的统一规范和理论知识，包括加解密机制、文件结构规范以及使用示例。适用于所有业务模块的开发。

## 文件结构规范

### API文件位置
- **lib目录**: 所有API相关代码放在 `lib/` 目录下
- **hooks目录**: 所有Hook相关代码放在 `hooks/` 目录下

### 核心文件说明（以股东模块为例）
1. `lib/base-[module]-api.ts` - 基础API客户端（使用fetch）
2. `lib/[specific]-api.ts` - 特定功能API（使用apiClient）
3. `hooks/use[ModuleName].ts` - 模块数据Hook

## API加解密机制

### 加密流程
项目中所有模块API采用统一的加解密机制确保数据安全：

```typescript
// 1. 创建加密请求参数
const requestData = await createEncryptedRequestAsync({
  id: organizationId,
  page,
  pageSize,
  // 其他业务参数...
});

// 2. 发送加密请求
const response = await fetch(`${getBaseUrl()}/api/n8n_proxy/${endpoint}`, {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  credentials: "include",
  body: JSON.stringify(requestData)
});
```

### 解密流程
API响应数据自动解密处理：

```typescript
// 1. 检查响应数据类型
if (typeof result.data === "string") {
  // 2. 解密字符串数据
  const decryptedString = decryptData(result.data);
  // 3. 解析JSON对象
  return JSON.parse(decryptedString);
} else {
  // 数据未加密，直接使用
  return result.data;
}
```

### 加解密工具函数
- `createEncryptedRequestAsync()` - 创建加密请求参数
- `decryptData()` - 解密响应数据
- `decryptApiResponseData<T>()` - 类型安全的解密函数

## API客户端类型

### 1. Fetch API客户端 (base-[module]-api.ts)
使用原生fetch进行HTTP请求，适用于需要精细控制的场景：

```typescript
// 创建API客户端工厂函数
const apiClient = create[ModuleName]ApiClient();

// 发送基础请求
const response = await apiClient.sendBaseRequest<ResponseType>(
  'endpoint-name',
  organizationId,
  '错误信息前缀'
);

// 发送分页请求
const response = await apiClient.sendPaginatedRequest<ResponseType>(
  'endpoint-name',
  organizationId,
  page,
  pageSize,
  '错误信息前缀'
);

// 发送带排序的分页请求
const response = await apiClient.sendSortablePaginatedRequest<ResponseType>(
  'endpoint-name',
  organizationId,
  page,
  pageSize,
  '错误信息前缀',
  'Desc', // 排序方向
  'fieldName' // 排序字段
);
```

### 2. ApiClient客户端 ([specific]-api.ts)
使用统一的apiClient进行请求，适用于标准化API调用：

```typescript
// 示例：模块特定API调用
const result = await moduleApi.specificOperation(
  organizationId,
  {
    // 业务参数
    param1: "value1",
    param2: "value2",
    // 分页参数
    page: 1,
    limit: 20
  }
);

// 示例：获取列表数据
const list = await moduleApi.getDataList(
  organizationId,
  { page: 1, limit: 20, filterParam: "value" }
);
```

## Hook使用规范

### React Query集成
所有模块Hook基于React Query实现，提供缓存、重试、状态管理等功能：

```typescript
// 通用Hook使用模式
const {
  data,               // 业务数据
  pagination,         // 分页信息
  isLoading,          // 加载状态
  error,              // 错误信息
  page,               // 当前页码
  setPage,            // 设置页码
  refetch             // 重新获取数据
} = useModuleData(organizationId, additionalParams);
```

### 缓存策略
- **staleTime**: 30秒内不重新获取数据
- **queryKey**: 基于参数自动生成缓存键
- **invalidateQueries**: 数据变更后自动刷新相关缓存

### 状态管理
Hook内部管理多种状态：
- **分页状态**: `page`, `limit`
- **筛选状态**: `searchTerm`, `sortBy`, `sortOrder`
- **业务状态**: 根据具体模块需求定义

### Hook命名规范
- 查询类Hook: `use[ModuleName]` 或 `use[ModuleName]List`
- 操作类Hook: `use[ModuleName][Operation]` (如 `useShareholderRegistry`)
- 复合功能Hook: `use[ModuleName][Feature]`

### QueryKey关联机制
Hook使用React Query的queryKey进行缓存管理和数据关联：

```typescript
// 查询类Hook的queryKey结构
const queryKey = ['module-data', organizationId, page, limit, ...filters];

// 相关数据的queryKey关联
const relatedQueryKeys = [
  ['module-list', organizationId],
  ['module-details', organizationId, itemId],
  ['module-summary', organizationId]
];

// 数据变更时刷新相关缓存
queryClient.invalidateQueries({ queryKey: ['module-list', organizationId] });
queryClient.invalidateQueries({ queryKey: ['module-details', organizationId] });
```

### Mutation Hook用法
操作类Hook使用useMutation处理数据变更：

```typescript
// 创建/更新/删除操作Hook
const createMutation = useMutation<ResultType, Error, CreateParams>({
  mutationFn: async (data) => {
    const result = await moduleApi.createData(organizationId, data);

    if (!result.success) {
      throw new Error(result.error?.message || '操作失败');
    }

    return result;
  },
  onSuccess: (data) => {
    // 成功后刷新相关缓存
    queryClient.invalidateQueries({ queryKey: ['module-list', organizationId] });
    queryClient.invalidateQueries({ queryKey: ['module-summary', organizationId] });

    // 显示成功提示
    toast.success('操作成功');
  },
  onError: (error) => {
    // 错误处理
    toast.error('操作失败', {
      description: error.message || '请重试'
    });
  }
});

// Hook返回值
return {
  // 操作函数
  createData: createMutation.mutateAsync,
  // 状态
  isCreating: createMutation.isPending,
  createError: createMutation.error
};
```

## 使用示例

### 基础API调用
```typescript
import { create[ModuleName]ApiClient } from '@saas/[module]/lib/base-[module]-api';

const apiClient = create[ModuleName]ApiClient();

// 获取数据列表
try {
  const response = await apiClient.sendPaginatedRequest(
    'data-endpoint',
    'org-123',
    1,
    20,
    '获取数据失败'
  );
  console.log(response.data);
} catch (error) {
  console.error('API调用失败:', error.message);
}
```

### 查询Hook使用
```typescript
import { use[ModuleName] } from '@saas/[module]/hooks/use[ModuleName]';

function DataList({ organizationId }: { organizationId: string }) {
  const {
    data,
    isLoading,
    error,
    page,
    setPage,
    pagination,
    refetch
  } = use[ModuleName](organizationId);

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  return (
    <div>
      {data.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
      <Pagination
        current={page}
        total={pagination?.total}
        onChange={setPage}
      />
      <button onClick={() => refetch()}>刷新数据</button>
    </div>
  );
}
```

### 操作Hook使用
```typescript
import { use[ModuleName]Operations } from '@saas/[module]/hooks/use[ModuleName]Operations';

function DataManager({ organizationId }: { organizationId: string }) {
  const {
    // 操作函数
    createData,
    updateData,
    deleteData,
    // 状态
    isCreating,
    isUpdating,
    isDeleting,
    // 错误
    createError,
    updateError,
    deleteError
  } = use[ModuleName]Operations(organizationId);

  const handleCreate = async (formData: CreateDataParams) => {
    try {
      await createData(formData);
      // 成功处理已在Hook内部完成
    } catch (error) {
      // 错误处理已在Hook内部完成
      console.error('创建失败:', error);
    }
  };

  const handleUpdate = async (id: string, formData: UpdateDataParams) => {
    try {
      await updateData({ id, ...formData });
    } catch (error) {
      console.error('更新失败:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm('确认删除？')) {
      try {
        await deleteData({ id });
      } catch (error) {
        console.error('删除失败:', error);
      }
    }
  };

  return (
    <div>
      <button
        onClick={() => handleCreate(newData)}
        disabled={isCreating}
      >
        {isCreating ? '创建中...' : '创建'}
      </button>

      <button
        onClick={() => handleUpdate(itemId, updateData)}
        disabled={isUpdating}
      >
        {isUpdating ? '更新中...' : '更新'}
      </button>

      <button
        onClick={() => handleDelete(itemId)}
        disabled={isDeleting}
      >
        {isDeleting ? '删除中...' : '删除'}
      </button>
    </div>
  );
}
```

### Hook成功失败处理模式
```typescript
// Hook内部成功失败处理
const uploadMutation = useMutation<UploadResult, Error, UploadParams>({
  mutationFn: async (data) => {
    const result = await moduleApi.uploadData(organizationId, data);

    if (!result.success) {
      throw new Error(result.error?.message || '上传失败');
    }

    return result;
  },
  onSuccess: (data) => {
    // 1. 刷新相关缓存
    queryClient.invalidateQueries({ queryKey: ['module-list', organizationId] });
    queryClient.invalidateQueries({ queryKey: ['module-summary', organizationId] });

    // 2. 显示成功提示
    toast.success('上传成功', {
      description: `成功处理 ${data.recordCount} 条记录`
    });

    // 3. 返回数据供组件使用
    return data;
  },
  onError: (error) => {
    // 1. 显示错误提示
    toast.error('上传失败', {
      description: error.message || '请检查文件格式后重试'
    });

    // 2. 错误日志记录
    console.error('Upload failed:', error);
  }
});

// 组件中的使用
const handleUpload = async (file: File) => {
  try {
    const result = await uploadData({ file, metadata });
    // 成功后的额外处理
    onUploadSuccess?.(result);
  } catch (error) {
    // 错误已在Hook内部处理，这里可以做额外处理
    onUploadError?.(error);
  }
};
```

### Hook关联数据刷新策略
```typescript
// 数据变更后的缓存刷新策略
const refreshRelatedData = (organizationId: string) => {
  // 1. 主数据列表
  queryClient.invalidateQueries({
    queryKey: ['module-list', organizationId]
  });

  // 2. 统计数据
  queryClient.invalidateQueries({
    queryKey: ['module-summary', organizationId]
  });

  // 3. 关联模块数据
  queryClient.invalidateQueries({
    queryKey: ['related-module-data', organizationId]
  });

  // 4. 分析报告数据
  queryClient.invalidateQueries({
    queryKey: ['module-analysis', organizationId]
  });
};

// 在mutation的onSuccess中调用
onSuccess: (data) => {
  refreshRelatedData(organizationId);
  toast.success('操作成功');
}
```

## 错误处理

### API层错误处理
- 参数验证错误
- HTTP状态码错误
- 业务逻辑错误
- 解密失败错误

### Hook层错误处理
- 自动错误捕获
- Toast错误提示
- 错误状态暴露
- 降级处理机制

## 开发规范

### API开发规范
1. **文件命名**:
   - 基础API: `base-[module]-api.ts`
   - 特定API: `[feature]-api.ts`
2. **函数命名**:
   - 工厂函数: `create[ModuleName]ApiClient()`
   - API方法: `get[DataName]`, `create[DataName]`, `update[DataName]`, `delete[DataName]`
3. **参数验证**: 使用统一的验证函数
4. **错误处理**: 统一的错误处理机制

### Hook开发规范
1. **文件命名**: `use[ModuleName].ts` 或 `use[ModuleName][Feature].ts`
2. **返回值结构**: 保持一致的返回值结构
3. **状态管理**: 合理管理内部状态
4. **缓存策略**: 统一的缓存配置
5. **QueryKey设计**:
   - 查询类: `['module-name', organizationId, ...params]`
   - 详情类: `['module-detail', organizationId, itemId]`
   - 统计类: `['module-summary', organizationId]`
6. **Mutation处理**:
   - 统一的成功失败处理
   - 自动缓存刷新机制
   - Toast提示集成
7. **错误处理**:
   - Hook内部错误捕获
   - 用户友好的错误提示
   - 错误状态暴露

## 最佳实践

1. **参数验证**: 使用内置验证函数确保参数正确性
2. **错误处理**: 统一错误处理机制，提供友好错误信息
3. **类型安全**: 使用TypeScript接口确保类型安全
4. **缓存管理**: 合理使用React Query缓存机制
5. **状态同步**: Hook内部状态与UI状态保持同步
6. **代码复用**: 抽取通用逻辑到基础API客户端
7. **文档注释**: 使用JSDoc注释，作者标记为hayden
8. **QueryKey设计**:
   - 使用一致的命名规范
   - 包含必要的参数用于缓存区分
   - 考虑数据关联性设计层级结构
9. **Mutation优化**:
   - 乐观更新提升用户体验
   - 失败时自动回滚
   - 批量操作的原子性处理
10. **Toast集成**:
    - 成功操作给予明确反馈
    - 错误信息具体且可操作
    - 避免重复提示

## 注意事项

- 所有API请求都经过加密处理
- Hook依赖React Query，需要在QueryClient Provider内使用
- 分页参数变更时会自动重置相关状态
- 数据变更操作会自动刷新相关缓存
- 遵循项目的TypeScript代码规范
- 使用pnpm monorepo架构的优势进行模块化开发
- QueryKey设计要考虑数据关联性，避免过度刷新
- Mutation操作要处理并发情况，避免数据竞争
- Toast提示要适度，避免用户体验干扰
- 错误处理要区分用户错误和系统错误
- 缓存失效策略要精确，避免不必要的网络请求

## Hook使用模式总结

### 查询模式
```typescript
// 基础查询Hook
const { data, isLoading, error, refetch } = useModuleData(params);

// 分页查询Hook
const { data, pagination, page, setPage, limit, setLimit } = useModuleList(params);

// 条件查询Hook
const { data, filters, setFilters, resetFilters } = useModuleSearch(params);
```

### 操作模式
```typescript
// 单一操作Hook
const { mutateAsync: createData, isPending: isCreating } = useCreateModule();

// 多操作Hook
const {
  createData, updateData, deleteData,
  isCreating, isUpdating, isDeleting
} = useModuleOperations();

// 批量操作Hook
const { batchDelete, batchUpdate, isBatchProcessing } = useModuleBatch();
```

### 复合模式
```typescript
// 查询+操作组合Hook
const {
  // 查询相关
  data, isLoading, refetch,
  // 操作相关
  createData, updateData, deleteData,
  // 状态相关
  isCreating, isUpdating, isDeleting
} = useModuleManager(organizationId);
```
