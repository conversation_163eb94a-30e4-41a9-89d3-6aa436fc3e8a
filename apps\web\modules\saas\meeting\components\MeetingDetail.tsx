"use client";

import { But<PERSON> } from "@ui/components/button";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@ui/components/card";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";

interface MeetingDetailProps {
  organizationSlug: string;
  meetingId?: string;
}

export function MeetingDetail({ organizationSlug, meetingId: propMeetingId }: MeetingDetailProps) {
  return (
    <div className="space-y-6">
      {/* 返回按钮 */}
      <div className="flex justify-start mb-4">
        <Button 
          asChild 
          variant="outline" 
          size="sm" 
          className="w-auto text-sm font-medium h-10"
        >
          <Link href={`/app/${organizationSlug}/meeting/list`}>
            <ChevronLeft className="mr-1 h-4 w-4" />
            返回会议列表
          </Link>
        </Button>
      </div>

      {/* 会议详情卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">会议详情</CardTitle>
          <CardDescription>
            会议号：{propMeetingId}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            会议详情功能开发中...
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/* 原始复杂实现已注释
import { useState, useEffect } from "react";
import { Button } from "@ui/components/button";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@ui/components/card";
import { Clock, Calendar, Link as LinkIcon, Users, Video, Laptop, ChevronLeft } from "lucide-react";
import { Meeting as BaseMeeting } from "./MeetingList";
import Link from "next/link";
import { useParams } from "next/navigation";

// 删除模拟数据，使用真实API
// const mockMeetings: Meeting[] = [ ... ];

// 扩展Meeting接口添加额外字段
interface MeetingDetail extends BaseMeeting {
  joinUrl?: string;
  meetingType?: string;
  enableLive?: string;
  enableDocUpload?: string;
  hasVote?: string;
  location?: string;
  timeZone?: string;
  endTime?: string;
  settings?: {
    muteEnableJoin?: boolean;
    allowUnmuteSelf?: boolean;
    allowInBeforeHost?: boolean;
    autoInWaitingRoom?: boolean;
    allowScreenSharedWatermark?: boolean;
    onlyAllowEnterpriseUserJoin?: boolean;
    autoRecordType?: string;
    autoAsr?: boolean;
  };
}

interface MeetingDetailProps {
  organizationSlug: string;
  meetingId?: string;
}

export function MeetingDetail({ organizationSlug, meetingId: propMeetingId }: MeetingDetailProps) {
  const params = useParams();
  // 从URL路径中提取会议ID，格式可能是: "123456-meeting-title"
  const urlMeetingParam = params?.meetingId as string;
  // 提取纯ID部分（取连字符前的部分）
  const urlMeetingId = urlMeetingParam?.split('-')[0];
  const meetingId = propMeetingId || urlMeetingId;
  
  const [meeting, setMeeting] = useState<MeetingDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cancelingMeeting, setCancelingMeeting] = useState(false);
  const [cancelError, setCancelError] = useState<string | null>(null);
  const [cancelSuccess, setCancelSuccess] = useState(false);

  useEffect(() => {
    if (!meetingId) {
      setError("会议ID不能为空");
      setLoading(false);
      return;
    }

    setLoading(true);
    
    // 使用新的API接口获取会议详情
    fetch(`/api/meetings/details/${meetingId}`)
      .then(async (res) => {
        if (!res.ok) {
          const errorText = await res.text();
          throw new Error(`状态 ${res.status}: ${errorText}`);
        }
        return res.json();
      })
      .then((data) => {
        if (!data.success) {
          throw new Error(JSON.stringify(data.error));
        }
        
        // 将API返回的会议数据转换为前端Meeting类型
        const meetingData = data.data.meeting_info_list?.[0];
        
        if (!meetingData) {
          throw new Error("未找到会议数据");
        }
        
        // 转换时间戳为日期对象
        const timestamp = parseInt(meetingData.start_time) * 1000; // 转换为毫秒
        const dateObj = new Date(timestamp);
            
        // 获取星期
        const weekdays = ["日", "一", "二", "三", "四", "五", "六"];
        const weekday = weekdays[dateObj.getDay()];
            
        // 格式化为：年-月-日-星期-时间
        const formattedTime = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')} 星期${weekday} ${String(dateObj.getHours()).padStart(2, '0')}:${String(dateObj.getMinutes()).padStart(2, '0')}`;
        
        // 处理结束时间
        const endTimestamp = parseInt(meetingData.end_time || "0") * 1000;
        const endDateObj = new Date(endTimestamp);
        const endWeekday = weekdays[endDateObj.getDay()];
        const formattedEndTime = `${endDateObj.getFullYear()}-${String(endDateObj.getMonth() + 1).padStart(2, '0')}-${String(endDateObj.getDate()).padStart(2, '0')} 星期${endWeekday} ${String(endDateObj.getHours()).padStart(2, '0')}:${String(endDateObj.getMinutes()).padStart(2, '0')}`;
        // 计算会议时长（秒）
        const startTimestamp = parseInt(meetingData.start_time || "0");
        const durationInMinutes = endTimestamp && timestamp 
          ? Math.round((endTimestamp - timestamp) / 60000) 
          : 60; // 默认60分钟
        
        // 构建Meeting对象
        const meetingDetail: MeetingDetail = {
          id: meetingData.meeting_id || "",
          title: meetingData.subject || "未命名会议",
          startTime: formattedTime,
          endTime: formattedEndTime,
          meetingId: meetingData.meeting_code || meetingData.meeting_id || "",
          status: meetingData.status,
          duration: durationInMinutes,
          host: typeof meetingData.current_hosts?.[0] === 'object' 
            ? JSON.stringify(meetingData.current_hosts?.[0]) 
            : (meetingData.current_hosts?.[0] || "未指定"),
          description: meetingData.location || "暂无描述", // 使用location字段作为描述
          participants: [], // API中没有明确的参会人员列表
          // 添加额外数据
          joinUrl: meetingData.join_url || "",
          joinURL: meetingData.join_url || "", // 添加joinURL属性映射到相同的值
          meetingType: meetingData.meeting_type === 0 ? "一次性会议" : "其他类型",
          enableLive: meetingData.enable_live ? "是" : "否",
          enableDocUpload: meetingData.enable_doc_upload_permission ? "是" : "否",
          hasVote: meetingData.has_vote ? "是" : "否",
          location: meetingData.location || "未指定地点",
          timeZone: typeof meetingData.time_zone === 'object' 
            ? 'Asia/Shanghai' 
            : (meetingData.time_zone || "Asia/Shanghai"),
          // 添加会议设置信息
          settings: {
            muteEnableJoin: meetingData.settings?.mute_enable_join,
            allowUnmuteSelf: meetingData.settings?.allow_unmute_self,
            allowInBeforeHost: meetingData.settings?.allow_in_before_host,
            autoInWaitingRoom: meetingData.settings?.auto_in_waiting_room,
            allowScreenSharedWatermark: meetingData.settings?.allow_screen_shared_watermark,
            onlyAllowEnterpriseUserJoin: meetingData.settings?.only_allow_enterprise_user_join,
            autoRecordType: meetingData.settings?.auto_record_type,
            autoAsr: meetingData.settings?.auto_asr
          }
        };
        
        setMeeting(meetingDetail);
      })
      .catch((err) => {
        console.error("获取会议详情失败:", err);
        setError(err.message || "获取会议信息失败");
      })
      .finally(() => {
        setLoading(false);
      });
  }, [meetingId]);

  // 取消会议
  const handleCancelMeeting = async () => {
    if (!meeting?.id) return;
    
    if (!window.confirm("确定要取消这个会议吗？")) {
      return;
    }
    
    setCancelingMeeting(true);
    setCancelError(null);
    setCancelSuccess(false);
    
    try {
      const response = await fetch(`/api/meetings/cancel/${meeting.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason_code: 1 })
      });
      
      const data = await response.json();
      
      if (!response.ok || !data.success) {
        throw new Error(data.error || "取消会议失败");
      }
      
      setCancelSuccess(true);
      
      // 延迟1秒后跳转回会议列表页面
      setTimeout(() => {
        window.location.href = `/app/${organizationSlug}/meeting/list`;
      }, 1500);
      
    } catch (err: any) {
      setCancelError(err.message || "取消会议时发生错误");
      console.error("取消会议错误:", err);
    } finally {
      setCancelingMeeting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !meeting) {
    return (
      <div className="text-center py-8">
        <div className="text-red-500 mb-4">{error || "会议不存在"}</div>
        <Button asChild variant="outline" size="sm" className="w-auto">
          <Link href={`/app/${organizationSlug}/meeting/list`}>
            <ChevronLeft className="mr-1 h-4 w-4" />
            返回会议列表
          </Link>
        </Button>
      </div>
    );
  }

  // 格式化会议时长为小时和分钟
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours > 0 ? `${hours}小时` : ''}${mins > 0 ? `${mins}分钟` : ''}`;
  };

  return (
    <div className="space-y-6">
//       <div className="flex justify-start mb-4">
//         <Button 
//           asChild 
//           variant="outline" 
//           size="sm" 
//           className="w-auto text-sm font-medium h-10"
//         >
//           <Link href={`/app/${organizationSlug}/meeting/list`}>
//             <ChevronLeft className="mr-1 h-4 w-4" />
//             返回会议列表
//           </Link>
//         </Button>
//       </div>

//       {/* 取消成功提示 */
//       {cancelSuccess && (
//         <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-4">
//           会议已成功取消，正在返回会议列表页面...
//         </div>
//       )}

//       {/* 取消错误提示 */}
//       {cancelError && (
//         <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4">
//           取消会议失败: {cancelError}
//         </div>
//       )}

//       {/* 会议详情卡片 */}
//       <Card>
//         <CardHeader>
//           <CardTitle className="text-xl">{meeting.title}</CardTitle>
//           <CardDescription>
//             会议号：{meeting.meetingId}
//           </CardDescription>
//         </CardHeader>
//         <CardContent className="space-y-6">
//           {/* 会议基本信息 */}
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//             <div className="flex items-center">
//               <Clock className="mr-3 h-5 w-5 text-gray-400" />
//               <div>
//                 <div className="text-sm text-gray-500">开始时间</div>
//                 <div>{meeting.startTime}</div>
//               </div>
//             </div>
            
//             <div className="flex items-center">
//               <Clock className="mr-3 h-5 w-5 text-gray-400" />
//               <div>
//                 <div className="text-sm text-gray-500">结束时间</div>
//                 <div>{meeting.endTime}</div>
//               </div>
//             </div>
            
//             <div className="flex items-center">
//               <Users className="mr-3 h-5 w-5 text-gray-400" />
//               <div>
//                 <div className="text-sm text-gray-500">主持人</div>
//                 {/* <div>{meeting.host?.[0] || '未指定'}</div> */}
//                 <div>喻浩明</div>
//               </div>
//             </div>
            
//             <div className="flex items-center">
//               <LinkIcon className="mr-3 h-5 w-5 text-gray-400" />
//               <div>
//                 <div className="text-sm text-gray-500">会议状态</div>
//                 <div>{meeting.status === 'MEETING_STATE_INIT' ? '即将召开' : '已结束'}</div>
//               </div>
//             </div>

//             {/* 添加额外信息 */}
//             {meeting.joinUrl && (
//               <div className="flex items-center">
//                 <Video className="mr-3 h-5 w-5 text-gray-400" />
//                 <div>
//                   <div className="text-sm text-gray-500">加入链接</div>
//                   <div className="text-blue-500 hover:underline">
//                     <a href={meeting.joinUrl} target="_blank" rel="noopener noreferrer">
//                       点击加入会议
//                     </a>
//                   </div>
//                 </div>
//               </div>
//             )}

//             <div className="flex items-center">
//               <Calendar className="mr-3 h-5 w-5 text-gray-400" />
//               <div>
//                 <div className="text-sm text-gray-500">会议时长</div>
//                 <div>{formatDuration(meeting.duration || 60)}</div>
//               </div>
//             </div>

//             {meeting.meetingType && (
//               <div className="flex items-center">
//                 <LinkIcon className="mr-3 h-5 w-5 text-gray-400" />
//                 <div>
//                   <div className="text-sm text-gray-500">会议类型</div>
//                   <div>{meeting.meetingType}</div>
//                 </div>
//               </div>
//             )}

//             {meeting.location && meeting.location !== "未指定地点" && (
//               <div className="flex items-center">
//                 <LinkIcon className="mr-3 h-5 w-5 text-gray-400" />
//                 <div>
//                   <div className="text-sm text-gray-500">会议地点</div>
//                   <div>{meeting.location}</div>
//                 </div>
//               </div>
//             )}
//           </div>
          
//           {/* 会议描述 */}
//           {meeting.description && meeting.description !== "暂无描述" && (
//             <div>
//               <div className="text-sm font-medium mb-2">会议描述</div>
//               <div className="text-gray-700 bg-gray-50 p-3 rounded-md">
//                 {meeting.description}
//               </div>
//             </div>
//           )}
          
//           {/* 会议功能和设置信息 - 横向布局 */}
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-2">
//             {/* 会议功能信息 */}
//             <div className="bg-gray-50 p-4 rounded-lg">
//               <div className="text-sm font-medium mb-3 text-gray-700">会议功能</div>
//               <div className="space-y-3">
//                 <div className="flex items-center">
//                   <div className="w-24 text-xs text-gray-500">直播功能:</div>
//                   <div className="text-sm">{meeting.enableLive}</div>
//                 </div>
//                 <div className="flex items-center">
//                   <div className="w-24 text-xs text-gray-500">文档上传:</div>
//                   <div className="text-sm">{meeting.enableDocUpload}</div>
//                 </div>
//                 <div className="flex items-center">
//                   <div className="w-24 text-xs text-gray-500">投票功能:</div>
//                   <div className="text-sm">{meeting.hasVote}</div>
//                 </div>
//                 <div className="flex items-center">
//                   <div className="w-24 text-xs text-gray-500">时区:</div>
//                   <div className="text-sm">{typeof meeting.timeZone === 'string' 
//                     ? meeting.timeZone.replace("QXNpYS9TaGFuZ2hhaQ==", "Asia/Shanghai")
//                     : "Asia/Shanghai"}</div>
//                 </div>
//               </div>
//             </div>

//             {/* 会议设置信息 */}
//             <div className="bg-gray-50 p-4 rounded-lg">
//               <div className="text-sm font-medium mb-3 text-gray-700">会议设置</div>
//               <div className="grid grid-cols-1 gap-y-3 gap-x-2">
//                 <div className="flex items-center">
//                   <div className="w-full text-xs text-gray-500">允许提前入会:</div>
//                   <div className="text-sm">{meeting.settings?.allowInBeforeHost ? "是" : "否"}</div>
//                 </div>
//                 <div className="flex items-center">
//                   <div className="w-full text-xs text-gray-500">入会时静音:</div>
//                   <div className="text-sm">{meeting.settings?.muteEnableJoin ? "是" : "否"}</div>
//                 </div>
//                 <div className="flex items-center">
//                   <div className="w-full text-xs text-gray-500">允许自行解除静音:</div>
//                   <div className="text-sm">{meeting.settings?.allowUnmuteSelf ? "是" : "否"}</div>
//                 </div>
//                 <div className="flex items-center">
//                   <div className="w-full text-xs text-gray-500">自动进入等候室:</div>
//                   <div className="text-sm">{meeting.settings?.autoInWaitingRoom ? "是" : "否"}</div>
//                 </div>
//                 <div className="flex items-center">
//                   <div className="w-full text-xs text-gray-500">屏幕共享水印:</div>
//                   <div className="text-sm">{meeting.settings?.allowScreenSharedWatermark ? "是" : "否"}</div>
//                 </div>
//                 <div className="flex items-center">
//                   <div className="w-full text-xs text-gray-500">自动录制:</div>
//                   <div className="text-sm">{meeting.settings?.autoRecordType === "none" ? "否" : "是"}</div>
//                 </div>
//               </div>
//             </div>
//           </div>
          
//           {/* 操作按钮 */}
//           <div className="flex flex-wrap gap-3 pt-4 border-t">
//             {meeting.status === 'MEETING_STATE_INIT' ? (
//               <>
//                 <Button className="flex-1" variant="primary" asChild>
//                   <a href={meeting.joinUrl || "#"} target="_blank" rel="noopener noreferrer">
//                     <Video className="mr-2 h-4 w-4" />
//                     加入会议
//                   </a>
//                 </Button>
//                 <Button className="flex-1" variant="outline">
//                   <Laptop className="mr-2 h-4 w-4" />
//                   共享屏幕
//                 </Button>
//                 <Button 
//                   className="flex-1" 
//                   variant="outline"
//                   onClick={handleCancelMeeting}
//                   disabled={cancelingMeeting}
//                 >
//                   {cancelingMeeting ? (
//                     <>
//                       <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
//                       取消中...
//                     </>
//                   ) : (
//                     <>取消会议</>
//                   )}
//                 </Button>
//               </>
//             ) : (
//               <>
//                 <Button className="flex-1" variant="outline">
//                   查看会议记录
//                 </Button>
//                 <Button className="flex-1" variant="outline">
//                   下载会议资料
//                 </Button>
//               </>
//             )}
//           </div>
//         </CardContent>
//       </Card>
//     </div>
//   );
// } 