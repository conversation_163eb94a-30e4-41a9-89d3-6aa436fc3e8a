import { operatorId, meetingApiClient } from "./config";
import type { CreateMeetingParams} from "./create";

/* 修改块开始: 修改会议参数接口
 * 修改范围: 扩展CreateMeetingParams接口，添加meeting_id字段
 * 修改时间: 2025-06-12
 * 修改人: LLM
 * 关联需求: 支持PUT请求修改会议功能
 * 恢复方法: 删除meeting_id相关的扩展类型定义
 */
export interface ModifyMeetingParams extends CreateMeetingParams {
	meeting_id: string;
}
/* 修改块结束: 修改会议参数接口
 * 修改时间: 2025-06-12
 */

/* 原代码:
export const modifyMeeting = {
	method: "PUT",
	path: "/meetings/modify",
	handler: async (params: CreateMeetingParams) => {
 * 修改原因: 更新参数类型为ModifyMeetingParams以支持meeting_id，修改API调用方式
 * 修改时间: 2025-06-12
 * 修改人: LLM
 * 关联需求: 实现PUT方式修改会议
 * 恢复方法: 恢复上方注释中的原代码
 */
export const modifyMeeting = {
	method: "PUT",
	path: "/meetings/modify/:meetingId",
	handler: async (params: ModifyMeetingParams) => {
		try {
			// 使用传入的用户ID，如果没有则使用默认的operatorId
			const targetUserId = params.userid || operatorId;

			// 构建请求体
			const requestBody = {
				userid: targetUserId,
				instanceid: 1,
				subject: params.subject,
				start_time: params.start_time,
				end_time: params.end_time,
				// 修改 settings 的处理方式
				settings: {
					...params.settings,
					// 确保水印相关的设置被正确处理
					allow_screen_shared_watermark:
						params.settings?.allow_screen_shared_watermark ?? false,
					water_mark_type: params.settings
						?.allow_screen_shared_watermark
						? (params.settings?.water_mark_type ?? 0)
						: undefined,
				},
				// 可选参数，如果存在则添加到请求体
				...(params.password && { password: params.password }),
				...(params.hosts && { hosts: params.hosts }),
				...(params.meeting_type !== undefined && {
					meeting_type: params.meeting_type,
				}),
				...(params.recurring_rule && {
					recurring_rule: params.recurring_rule,
				}),
				...(params.enable_live !== undefined && {
					enable_live: params.enable_live,
				}),
				...(params.live_config && { live_config: params.live_config }),
				...(params.enable_doc_upload_permission !== undefined && {
					enable_doc_upload_permission:
						params.enable_doc_upload_permission,
				}),
				...(params.media_set_type !== undefined && {
					media_set_type: params.media_set_type,
				}),
				...(params.enable_interpreter !== undefined && {
					enable_interpreter: params.enable_interpreter,
				}),
				...(params.enable_enroll !== undefined && {
					enable_enroll: params.enable_enroll,
				}),
				...(params.enable_host_key !== undefined && {
					enable_host_key: params.enable_host_key,
				}),
				...(params.host_key && { host_key: params.host_key }),
				...(params.sync_to_wework !== undefined && {
					sync_to_wework: params.sync_to_wework,
				}),
				...(params.time_zone && { time_zone: params.time_zone }),
				...(params.location && { location: params.location }),
				...(params.allow_enterprise_intranet_only !== undefined && {
					allow_enterprise_intranet_only:
						params.allow_enterprise_intranet_only,
				}),
			};

			/* 原代码:
			// console.log("Meeting API request:", JSON.stringify(requestBody));
			const response = await meetingApiClient.post(
				"/v1/meetings",
				requestBody,
			);

			// console.log("Meeting created:", response.data);
			 * 修改原因: 改为PUT请求修改会议，使用meeting_id作为路径参数
			 * 修改时间: 2025-06-12
			 * 修改人: LLM
			 * 关联需求: 按照腾讯会议API规范使用PUT方法修改会议
			 * 恢复方法: 恢复上方注释中的POST请求代码
			 */
			// console.log("Meeting API request:", JSON.stringify(requestBody));
			const response = await meetingApiClient.put(
				`/v1/meetings/${params.meeting_id}`,
				requestBody,
			);

			// console.log("Meeting updated:", response.data);
			return response.data;
		} catch (error: any) {
			/* 原代码:
			console.log("Error creating meeting:", error);
			 * 修改原因: 更新错误日志信息为修改会议
			 * 修改时间: 2025-06-12
			 * 修改人: LLM
			 * 关联需求: 使日志信息准确反映修改会议操作
			 * 恢复方法: 恢复上方注释中的原错误日志
			 */
			console.log("Error updating meeting:", error);
			throw error;
		}
	},
};
