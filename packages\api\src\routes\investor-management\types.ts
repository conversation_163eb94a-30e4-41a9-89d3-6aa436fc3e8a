/**
 * 投资人管理模块类型定义
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建类型定义
 * @description 投资人管理模块的所有TypeScript类型定义，包括请求和响应类型
 */
import type { z } from "zod";
import type {
  CreateCompanyFilterSchema,
  GetCompanyFilterSchema,
  CreateInvestorTagSchema,
  DeleteInvestorTagSchema,
  ListInvestorTagsSchema,
  SyncInvestorTagsSchema,
  CreateInvestorContactSchema,
  UpdateInvestorContactSchema,
  DeleteInvestorContactSchema,
  ListInvestorContactsSchema,
} from "./lib/validators";

// 公司筛选配置请求类型
export type CreateCompanyFilterRequest = z.infer<typeof CreateCompanyFilterSchema>;
export type GetCompanyFilterRequest = z.infer<typeof GetCompanyFilterSchema>;

// 投资人标签请求类型
export type CreateInvestorTagRequest = z.infer<typeof CreateInvestorTagSchema>;
export type DeleteInvestorTagRequest = z.infer<typeof DeleteInvestorTagSchema>;
export type ListInvestorTagsRequest = z.infer<typeof ListInvestorTagsSchema>;
export type SyncInvestorTagsRequest = z.infer<typeof SyncInvestorTagsSchema>;

// 投资人联系人请求类型
export type CreateInvestorContactRequest = z.infer<typeof CreateInvestorContactSchema>;
export type UpdateInvestorContactRequest = z.infer<typeof UpdateInvestorContactSchema>;
export type DeleteInvestorContactRequest = z.infer<typeof DeleteInvestorContactSchema>;
export type ListInvestorContactsRequest = z.infer<typeof ListInvestorContactsSchema>;

// 公司筛选配置响应类型
export interface CreateCompanyFilterResponse {
  id: string;
}

export interface GetCompanyFilterResponse {
  companyFilter: {
    id: string;
    organizationId: string;
    companyCode: string;
    benchmarkCompanyCodes: string;
    modifiedAt: string;
  };
  investorTags: InvestorTagItem[];
}

// 投资人标签响应类型
export interface CreateInvestorTagResponse {
  id: string;
}

export interface DeleteInvestorTagResponse {
  id: string;
}

export interface InvestorTagItem {
  id: string;
  investorCode: string;
  tagName: string;
  tagCategory: string;
  tagMetadata: Record<string, any>;
  modifiedAt: string;
  organizationName: string;
  companyFilter?: {
    companyCode: string;
    benchmarkCompanyCodes: string;
  } | null;
}

export interface ListInvestorTagsResponse {
  companyFilter: {
    id: string;
    companyCode: string;
    benchmarkCompanyCodes: string;
    modifiedAt: string;
  };
  tags: InvestorTagItem[];
  pagination: Pagination;
}

export interface SyncInvestorTagsResponse {
  syncedCount: number;
  companyCode: string;
  benchmarkCompanyCodes: string;
  syncTime: string;
}

// 投资人联系人响应类型
export interface CreateInvestorContactResponse {
  contactId: string;
}

export interface UpdateInvestorContactResponse {
  contactId: string;
}

export interface DeleteInvestorContactResponse {
  contactId: string;
}

export interface InvestorContactItem {
  contactId: string;
  name: string;
  phoneNumber: string;
  email: string;
  address: string;
  remarks: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string | null;
}

export interface ListInvestorContactsResponse {
  contacts: InvestorContactItem[];
  pagination: Pagination;
}

// 通用分页类型
export interface Pagination {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
