import { db } from "@repo/database";
import { logger } from "@repo/logs";

/**
 * 根据标识符查找组织，支持多个匹配结果的选择
 * @param identifier 组织名称、slug或ID
 * @returns 选择的组织对象或null
 */
async function findOrganizationByIdentifier(identifier: string) {
	// 首先尝试精确匹配ID
	let organization = await db.organization.findUnique({
		where: { id: identifier },
		include: {
			_count: {
				select: {
					members: true,
				},
			},
			members: {
				where: { role: "owner" },
				include: {
					user: {
						select: {
							name: true,
							email: true,
						},
					},
				},
			},
		},
	});

	if (organization) {
		return organization;
	}

	// 然后尝试精确匹配slug
	organization = await db.organization.findUnique({
		where: { slug: identifier },
		include: {
			_count: {
				select: {
					members: true,
				},
			},
			members: {
				where: { role: "owner" },
				include: {
					user: {
						select: {
							name: true,
							email: true,
						},
					},
				},
			},
		},
	});

	if (organization) {
		return organization;
	}

	// 最后尝试模糊匹配名称，可能返回多个结果
	const organizations = await db.organization.findMany({
		where: {
			name: {
				contains: identifier,
				mode: "insensitive",
			},
		},
		include: {
			_count: {
				select: {
					members: true,
				},
			},
			members: {
				where: { role: "owner" },
				include: {
					user: {
						select: {
							name: true,
							email: true,
						},
					},
				},
			},
		},
	});

	if (organizations.length === 0) {
		return null;
	}

	if (organizations.length === 1) {
		return organizations[0];
	}

	// 如果有多个组织，让用户选择
	logger.info(`找到 ${organizations.length} 个匹配 "${identifier}" 的组织:`);
	logger.info("");

	organizations.forEach((org, index) => {
		const owners = org.members.map(member => `${member.user.name} (${member.user.email})`).join(", ");
		logger.info(`${index + 1}. 组织信息:`);
		logger.info(`   名称: ${org.name}`);
		logger.info(`   Slug: ${org.slug || "无"}`);
		logger.info(`   ID: ${org.id}`);
		logger.info(`   所有者: ${owners || "无"}`);
		logger.info(`   成员数量: ${org._count.members}`);
		logger.info("");
	});

	const choice = await logger.prompt(
		`请选择要删除的组织 (1-${organizations.length}):`,
		{
			required: true,
			type: "text",
			placeholder: "1",
		},
	);

	const choiceIndex = Number.parseInt(choice.trim()) - 1;
	if (choiceIndex < 0 || choiceIndex >= organizations.length) {
		logger.error("❌ 无效的选择");
		return null;
	}

	return organizations[choiceIndex];
}

async function main() {
	logger.info("🗑️  Delete Organization Script - 删除组织脚本");
	logger.warn("⚠️  警告：此操作将永久删除组织及其所有相关数据，无法恢复！");

	// 获取组织标识 (支持组织名称、slug或ID)
	const orgIdentifier = await logger.prompt("请输入要删除的组织名称、slug或ID:", {
		required: true,
		placeholder: "组织名称 或 org-slug 或 org_id_123",
		type: "text",
	});

	// 查找组织
	logger.info("🔍 正在查找组织...");

	const selectedOrg = await findOrganizationByIdentifier(orgIdentifier);

	if (!selectedOrg) {
		logger.error("❌ 未找到指定的组织！");
		logger.info("💡 提示：请检查组织名称、slug或ID是否正确");
		return;
	}

	// 获取完整的组织信息（包括所有关联数据）
	const organization = await db.organization.findUnique({
		where: { id: selectedOrg.id },
		include: {
			members: {
				include: {
					user: {
						select: {
							id: true,
							name: true,
							email: true,
							role: true,
						},
					},
				},
			},
			invitations: true,
			purchases: true,
			aiChats: true,
			shareholderRegistries: {
				include: {
					companyInfo: true,
					shareholders: true,
				},
			},
		},
	});

	if (!organization) {
		logger.error("❌ 获取组织详细信息失败！");
		return;
	}

	// 显示选择的组织信息
	logger.info("✅ 选择的组织信息:");
	logger.info(`   ID: ${organization.id}`);
	logger.info(`   名称: ${organization.name}`);
	logger.info(`   Slug: ${organization.slug || "未设置"}`);
	logger.info(`   创建时间: ${organization.createdAt.toLocaleString()}`);
	logger.info(`   Logo: ${organization.logo || "未设置"}`);
	logger.info(`   支付客户ID: ${organization.paymentsCustomerId || "未设置"}`);

	// 显示关联数据统计
	logger.info("\n📊 关联数据统计:");
	logger.info(`   组织成员: ${organization.members.length} 个`);
	logger.info(`   待处理邀请: ${organization.invitations.length} 个`);
	logger.info(`   购买记录: ${organization.purchases.length} 个`);
	logger.info(`   AI聊天记录: ${organization.aiChats.length} 个`);
	logger.info(`   股东名册: ${organization.shareholderRegistries.length} 个`);

	// 计算股东名册相关数据
	const totalCompanyInfo = organization.shareholderRegistries.reduce(
		(sum, registry) => sum + registry.companyInfo.length, 0
	);
	const totalShareholders = organization.shareholderRegistries.reduce(
		(sum, registry) => sum + registry.shareholders.length, 0
	);
	
	if (organization.shareholderRegistries.length > 0) {
		logger.info(`   └─ 公司信息记录: ${totalCompanyInfo} 条`);
		logger.info(`   └─ 股东信息记录: ${totalShareholders} 条`);
	}

	// 显示组织成员详情
	if (organization.members.length > 0) {
		logger.info("\n👥 组织成员详情:");
		const owners = organization.members.filter(m => m.role === "owner");
		const admins = organization.members.filter(m => m.role === "admin");
		const members = organization.members.filter(m => m.role === "member");

		if (owners.length > 0) {
			logger.info("   🔑 所有者:");
			owners.forEach(member => {
				logger.info(`     - ${member.user.name} (${member.user.email}) ${member.user.role === "admin" ? "[系统管理员]" : ""}`);
			});
		}

		if (admins.length > 0) {
			logger.info("   👑 管理员:");
			admins.forEach(member => {
				logger.info(`     - ${member.user.name} (${member.user.email}) ${member.user.role === "admin" ? "[系统管理员]" : ""}`);
			});
		}

		if (members.length > 0) {
			logger.info("   👤 普通成员:");
			members.forEach(member => {
				logger.info(`     - ${member.user.name} (${member.user.email}) ${member.user.role === "admin" ? "[系统管理员]" : ""}`);
			});
		}
	}

	// 显示待处理邀请
	if (organization.invitations.length > 0) {
		logger.info("\n📧 待处理邀请:");
		organization.invitations.forEach(invitation => {
			logger.info(`   - ${invitation.email} (${invitation.role}) - ${invitation.status}`);
		});
	}

	// 显示股东名册详情
	if (organization.shareholderRegistries.length > 0) {
		logger.info("\n📊 股东名册详情:");
		organization.shareholderRegistries.forEach(registry => {
			logger.info(`   - ${registry.fileName} (${registry.companyCode})`);
			logger.info(`     └─ 记录数: ${registry.recordCount}, 日期: ${registry.registerDate.toLocaleDateString()}`);
		});
	}

	// 警告信息
	logger.warn("\n⚠️  删除警告:");
	logger.warn("   • 此操作将永久删除组织及其所有关联数据");
	logger.warn("   • 所有组织成员将失去对该组织的访问权限");
	logger.warn("   • 所有股东名册数据将被永久删除");
	logger.warn("   • 所有AI聊天记录将被永久删除");
	logger.warn("   • 所有购买记录将被永久删除");
	logger.warn("   • 此操作无法撤销！");

	// 最终确认
	const confirmDelete = await logger.prompt(
		"\n❓ 确认删除此组织及其所有相关数据吗？此操作不可逆！",
		{
			required: true,
			type: "confirm",
			default: false,
		}
	);

	if (!confirmDelete) {
		logger.info("✅ 操作已取消，组织未被删除。");
		return;
	}

	// 二次确认 (对于有大量数据的组织)
	const hasSignificantData = organization.members.length > 1 || 
		organization.shareholderRegistries.length > 0 || 
		totalShareholders > 0;

	if (hasSignificantData) {
		const doubleConfirm = await logger.prompt(
			"🚨 该组织包含重要数据！请再次确认删除:",
			{
				required: true,
				type: "confirm",
				default: false,
			}
		);

		if (!doubleConfirm) {
			logger.info("✅ 操作已取消，组织未被删除。");
			return;
		}
	}

	// 执行删除操作
	logger.info("🔄 开始删除组织数据...");
	logger.info("💡 由于配置了数据库级联删除，相关数据将自动删除");

	try {
		await db.$transaction(async (tx) => {
			// 由于数据库配置了级联删除 (onDelete: Cascade)，
			// 删除组织记录会自动删除所有关联数据：
			// - 成员关系 (member)
			// - 邀请记录 (invitation) 
			// - 购买记录 (Purchase)
			// - AI聊天记录 (AiChat)
			// - 股东名册 (shareholderRegistry)
			// - 公司信息 (companyInfo)
			// - 股东信息 (shareholder)

			logger.info("   🏢 删除组织主记录...");
			await tx.organization.delete({
				where: { id: organization.id },
			});

			logger.info("   ✨ 级联删除已自动处理所有关联数据");
		});

		logger.success("✅ 组织删除成功！");
		logger.info("📋 删除操作摘要:");
		logger.info(`   - 组织: ${organization.name} (${organization.id})`);
		logger.info(`   - 成员: ${organization.members.length} 个`);
		logger.info(`   - 邀请: ${organization.invitations.length} 个`);
		logger.info(`   - 购买记录: ${organization.purchases.length} 个`);
		logger.info(`   - AI聊天: ${organization.aiChats.length} 个`);
		logger.info(`   - 股东名册: ${organization.shareholderRegistries.length} 个`);
		if (organization.shareholderRegistries.length > 0) {
			logger.info(`   - 公司信息: ${totalCompanyInfo} 条`);
			logger.info(`   - 股东信息: ${totalShareholders} 条`);
		}
		logger.info(`   - 删除时间: ${new Date().toLocaleString()}`);

	} catch (error) {
		logger.error("❌ 删除组织时发生错误:");
		logger.error(error);
		logger.error("组织数据未被删除，请检查错误信息后重试。");
		
		// 提供一些常见错误的解决建议
		if (error instanceof Error) {
			if (error.message.includes("foreign key constraint")) {
				logger.info("💡 可能的解决方案：");
				logger.info("   - 检查是否有其他数据引用了此组织");
				logger.info("   - 确保数据库外键约束配置正确");
			}
		}
	}
}

main().catch((error) => {
	logger.error("脚本执行失败:");
	logger.error(error);
	process.exit(1);
});
