# 数据库操作手册

## 1. 数据库配置

### 1.1 数据库连接

数据库连接信息配置在项目根目录的`.env.local`文件中，使用以下格式：

```
DATABASE_URL="postgresql://postgres:hayden2020@127.0.0.1:5432/starlink_db"
```

此配置包含以下信息：
- 数据库类型：PostgreSQL
- 用户名：postgres
- 密码：hayden2020
- 主机地址：127.0.0.1
- 端口：5432
- 数据库名：starlink_db

### 1.2 Prisma配置

项目使用Prisma作为ORM工具，主要配置文件位于`packages/database/prisma/schema.prisma`。此文件定义了数据库模型、关系和生成器配置。

## 2. 数据库命令

项目使用pnpm作为包管理工具，并配置了几个关键的数据库操作命令：

### 2.1 生成Prisma客户端

```powershell
pnpm --filter database generate
```

此命令会根据schema.prisma文件生成：
- Prisma客户端代码
- Zod验证模式
- Prisma JSON类型定义

**注意**：每次修改schema.prisma后，都应该运行此命令更新生成的类型和客户端。

### 2.2 数据库迁移

```powershell
pnpm --filter database migrate
```

此命令会：
1. 检查schema.prisma与数据库的差异
2. 创建新的迁移文件（如果有变更）
3. 应用迁移到数据库
4. 生成Prisma客户端和相关类型

**重要提示**：
- 运行此命令时，系统会提示输入迁移名称，请使用有意义的名称描述此次变更
- 确保产生迁移文件，否则后续迁移可能产生"漂流"问题，引发迁移文件不可用的报错

### 2.3 数据库推送

```powershell
pnpm --filter database push
```

此命令会将schema.prisma的变更直接推送到数据库，而不创建迁移文件。适用于开发环境快速测试，**不推荐**在生产环境使用。

### 2.4 启动Prisma Studio

```powershell
pnpm --filter database studio
```

此命令会启动Prisma Studio，这是一个可视化的数据库管理工具，可以通过浏览器查看和编辑数据库内容。

## 3. 数据库迁移最佳实践

### 3.1 迁移流程

正确的数据库迁移流程应当遵循以下步骤：

1. 修改`schema.prisma`文件，添加或修改模型定义
2. 运行迁移命令：`pnpm --filter database migrate`
3. 为迁移提供一个有意义的名称（如：add_user_profile、update_shareholder_fields等）
4. 确认迁移文件生成在`prisma/migrations`目录下
5. 提交所有变更到版本控制系统，包括schema文件和迁移文件

### 3.2 避免迁移问题

为避免常见的迁移问题，请注意以下事项：

1. **始终创建迁移文件**：不要在生产环境使用`db push`，应当总是使用`migrate`命令生成迁移文件
2. **不要直接修改已提交的迁移文件**：如需更改，应创建新的迁移
3. **在多人开发环境中协调迁移**：确保团队成员按照一致的顺序应用迁移
4. **谨慎处理数据删除操作**：删除字段或表前，确认没有依赖关系
5. **备份数据**：在执行重要迁移前，务必备份数据库

### 3.3 迁移漂流问题解决方案

迁移漂流（Drift）是指数据库实际状态与Prisma迁移历史不一致的情况，常见原因有：

1. 使用`db push`而非`migrate`
2. 直接在数据库中修改表结构
3. 迁移文件丢失或应用顺序错误

解决方法：

**轻微漂流**：
```powershell
pnpm --filter database migrate reset
```
此命令会重置数据库并重新应用所有迁移（**警告：会删除所有数据**）

**严重漂流**：
1. 导出当前数据库结构
2. 创建新的基准迁移（baseline migration）
3. 删除冲突的旧迁移文件
4. 恢复数据

## 4. 数据库模型概览

项目使用的主要数据模型包括：

- User：用户信息
- Organization：组织信息
- ShareholderRegistry：股东名册
- CompanyInfo：公司信息
- Shareholder：股东信息

这些模型之间存在多种关联关系，详细定义可查看`schema.prisma`文件。

## 5. 常见问题排查

### 5.1 迁移失败

**症状**：运行迁移命令时出现错误，无法创建或应用迁移文件。

**解决方案**：
1. 检查数据库连接是否正常
2. 检查schema.prisma文件语法是否正确
3. 检查是否有冲突的模型定义或字段类型
4. 查看详细错误信息，针对性解决

### 5.2 类型生成问题

**症状**：生成的类型与schema定义不一致，或出现类型错误。

**解决方案**：
1. 确保运行了最新的`generate`命令
2. 检查`node_modules`下的生成文件是否更新
3. 重新安装依赖：`pnpm install`

### 5.3 数据库连接问题

**症状**：无法连接数据库，出现连接超时或认证失败。

**解决方案**：
1. 确认`.env.local`中的连接字符串正确
2. 验证PostgreSQL服务是否运行
3. 检查网络和防火墙设置
4. 确认用户名密码正确

## 6. 开发建议

1. **频繁备份**：在重要操作前备份数据库
2. **小步迁移**：每次只做少量相关的模型变更，便于管理和回滚
3. **保持迁移历史整洁**：使用有意义的迁移名称，避免不必要的迁移
4. **测试验证**：迁移后验证应用功能是否正常
5. **使用事务**：对于复杂操作，使用数据库事务保证一致性

## 7. 附录

### 7.1 完整的迁移历史

当前项目迁移历史：
- 20250520065751_starlink_db：初始数据库设置
- 20250520070352_test：测试迁移
- 20250520070732_recover：恢复迁移（删除shareholder表中的test列）

### 7.2 有用的PostgreSQL命令

```sql
-- 列出所有数据库
\l

-- 连接到数据库
\c starlink_db

-- 列出所有表
\dt

-- 查看表结构
\d+ shareholder
```

### 7.3 推荐的数据库工具

- DBeaver：跨平台数据库管理工具
- pgAdmin：PostgreSQL专用管理工具
- Prisma Studio：通过`pnpm --filter database studio`启动的可视化工具

---

文档创建日期：2025年05月20日
最后更新日期：2025年05月20日 