"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@ui/components/button";
import { XCircle} from "lucide-react";
import { DocsDialog, SignInDialog, ParticipantsDialog, RecordDialog, TranscriptDialog, AISummaryDialog } from "./MeetingDialogs";
import { formatTimestamp} from "./meeting-utils";

import type { MeetingTableProps } from "./types";
import { useSession } from "../../auth/hooks/use-session";
import { toast } from "sonner";
// import { cn } from "@ui/lib";
import { AntMeetingTable } from "./ant-meeting-table";
import type { TableColumnType } from "antd";
import Link from "next/link";
import { ChevronDown, ChevronUp } from "lucide-react";

// 签到信息类型定义
interface SignInInfo {
  sign_in_id: string;
  start_time: string;
  sign_in_specification?: string;
  sign_in_status: number; // 0：未开始 1：进行中 2：已结束
}

interface SignInListData {
  sign_in_list: SignInInfo[];
  total_count: number;
  total_page: number;
  current_page: number;
  current_size: number;
}



export function MeetingTable({
  meetings,
  organizationSlug,
  sortDirection,
  toggleSortDirection,
  isLoading,
//   cancelingMeetingId,
  handleCancelMeeting,
//   handleGetMeetingDocs,
//   handleGetMeetingRecord,
  showDocAndRecordButtons,
  hasCheckedUserStatus = true,
  isUserActive = true,
  isActivating = false,
  handleActivation,
  showActivationDialog = false,
  clearActivationCheck,
}: MeetingTableProps) {
const { user } = useSession();

// 签到记录相关状态
const [signInDialogOpen, setSignInDialogOpen] = useState(false);
const [currentMeetingSignIn, setCurrentMeetingSignIn] = useState<SignInListData | null>(null);
const [loadingSignIn, setLoadingSignIn] = useState(false);
const [currentMeetingTitle, setCurrentMeetingTitle] = useState("");
const [recordUrl, setRecordUrl] = useState<string>("");

// 分页相关状态
const [currentPage, setCurrentPage] = useState(1);
const [itemsPerPage, setItemsPerPage] = useState(10); // 默认每页显示10条
const [pageInput, setPageInput] = useState(""); // 用于页码输入框
const [itemsPerPageInput, setItemsPerPageInput] = useState(itemsPerPage.toString()); // 用于每页条数输入框

//会议文档记录相关状态
const [docsDialogOpen, setDocsDialogOpen] = useState(false);
const [currentMeetingDocs, setCurrentMeetingDocs] = useState<any>(null);
const [loadingDocs, setLoadingDocs] = useState(false);

// 参会人员相关状态
const [participantsDialogOpen, setParticipantsDialogOpen] = useState(false);
const [currentMeetingId, setCurrentMeetingId] = useState("");
const [loadingParticipants, setLoadingParticipants] = useState(false);
/* 修改块开始: 参会人员导出数据状态
 * 修改范围: 新增导出url和sheetData的state
 * 修改时间: [TIME]
 * 对应计划步骤: 1
 */
const [participantsExportUrl, setParticipantsExportUrl] = useState<string | null>(null);
const [participantsSheetData, setParticipantsSheetData] = useState<any>(null);
/* 修改块结束: 参会人员导出数据状态
 * 修改时间: [TIME]
 */

// 会议录制相关状态
const [recordDialogOpen, setRecordDialogOpen] = useState(false);
const [loadingRecord, setLoadingRecord] = useState(false);

// 会议转写相关状态
const [transcriptDialogOpen, setTranscriptDialogOpen] = useState(false);
const [loadingTranscript, setLoadingTranscript] = useState(false);
/* 修改块开始: 会议转写数据状态
 * 修改范围: 新增 transcriptDownloadUrl 和 transcriptTxtContent 状态
 * 修改时间: 2025-06-17
 * 对应计划步骤: 1
 * 恢复方法: 删除 transcriptDownloadUrl 和 transcriptTxtContent 相关代码
 */
const [transcriptDownloadUrl, setTranscriptDownloadUrl] = useState<string>("");
const [transcriptTxtContent, setTranscriptTxtContent] = useState<string>("");
/* 修改块结束: 会议转写数据状态
 * 修改时间: 2025-06-17
 */

// AI会议纪要相关状态
const [aiSummaryDialogOpen, setAiSummaryDialogOpen] = useState(false);
const [loadingAISummary, setLoadingAISummary] = useState(false);
const [aiSummaryDownloadUrl, setAISummaryDownloadUrl] = useState<string>("");
const [aiSummaryTxtContent, setAISummaryTxtContent] = useState<string>("");
  
  // 计算总页数
  const totalPages = Math.ceil(meetings.length / itemsPerPage);
  
  // 获取当前页的数据
  const getCurrentPageData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return meetings.slice(startIndex, endIndex);
  };

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      setPageInput(""); // 清空页码输入框
    }
  };

  // 处理页码输入跳转
  const handlePageJump = () => {
    const pageNumber = Number.parseInt(pageInput, 10);
    if (pageNumber && pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      setPageInput(""); // 清空输入框
    } else {
      toast.error(`请输入有效的页码 (1-${totalPages})`);
    }
  };

  // 处理每页条数变化
  const handleItemsPerPageChange = () => {
    const newItemsPerPage = Number.parseInt(itemsPerPageInput, 10);
    if (newItemsPerPage && newItemsPerPage >= 1) {
      setItemsPerPage(newItemsPerPage);
      setCurrentPage(1); // 重置到第一页
      setItemsPerPageInput(newItemsPerPage.toString()); // 更新输入框值
    } else {
      toast.error("请输入有效的每页显示条数");
      setItemsPerPageInput(itemsPerPage.toString()); // 恢复原值
    }
  };

  // 处理输入框回车事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, type: 'page' | 'itemsPerPage') => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (type === 'page') {
        handlePageJump();
      } else {
        handleItemsPerPageChange();
      }
    }
  };

  

  /* 修改块开始: 新增签到记录获取功能
   * 修改范围: 添加获取会议签到记录的函数
   * 对应需求: 调用后端API获取签到列表并在模态框中展示
   * 恢复方法: 删除此函数和相关状态管理
   */
  // 获取会议签到记录
  const handleGetSignInRecord = async (meetingId: string, meetingTitle: string) => {
    if (!user?.id) {
      return;
    }
    setLoadingSignIn(true);
    setCurrentMeetingTitle(meetingTitle);
    setSignInDialogOpen(true);
    
    try {
      const response = await fetch(`/api/meetings/sign-in/list/${meetingId}?userId=${user.id}`);
      
      if (!response.ok) {
        throw new Error(`获取签到记录失败 (状态码: ${response.status})`);
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || "获取签到记录失败");
      }
      
      
      // 验证返回的数据结构
      if (data.data && typeof data.data === 'object') {
        // 确保 sign_in_list 存在，如果不存在则设置为空数组
        const signInData = {
          sign_in_list: data.data.sign_in_list || [],
          total_count: data.data.total_count || 0,
          total_page: data.data.total_page || 1,
          current_page: data.data.current_page || 1,
          current_size: data.data.current_size || 0,
        };
        setCurrentMeetingSignIn(signInData);
      } else {
        // 如果数据结构不正确，设置默认值
        setCurrentMeetingSignIn({
          sign_in_list: [],
          total_count: 0,
          total_page: 1,
          current_page: 1,
          current_size: 0,
        });
      }
      
    } catch (error: any) {
      toast.error("获取会议签到记录错误:", error);
      
      // 设置空的签到数据以避免undefined错误
      setCurrentMeetingSignIn({
        sign_in_list: [],
        total_count: 0,
        total_page: 1,
        current_page: 1,
        current_size: 0,
      });
      
    } finally {
      setLoadingSignIn(false);
    }
  };

  // 处理显示参会人员弹窗
  /* 修改块开始: 参会人员弹窗自动导出逻辑
   * 修改范围: handleShowParticipants合并导出逻辑，自动请求导出接口并存储数据
   * 修改时间: [TIME]
   * 对应计划步骤: 2
   */
  const handleShowParticipants = async (meetingId: string, meetingTitle: string) => {
    setCurrentMeetingTitle(meetingTitle);
    setCurrentMeetingId(meetingId);
    setParticipantsDialogOpen(true);
    setLoadingParticipants(true);
    setParticipantsExportUrl(null);
    setParticipantsSheetData(null);
    try {
      const { url, sheetData } = await handleExportParticipants(meetingId);
      setParticipantsExportUrl(url);
      setParticipantsSheetData(sheetData);
    } catch (e) {
      setParticipantsExportUrl(null);
      setParticipantsSheetData(null);
    } finally {
      setLoadingParticipants(false);
    }
  };
  /* 修改块结束: 参会人员弹窗自动导出逻辑
   * 修改时间: [TIME]
   */

  // 处理导出参会成员 (在弹窗中使用)
  /* 修改块开始: 导出参会成员返回数据
   * 修改范围: handleExportParticipants返回{url, sheetData}，不再直接toast
   * 修改时间: [TIME]
   * 对应计划步骤: 3
   */
  const handleExportParticipants = async (meetingId: string) => {
    const response = await fetch(`/api/meetings/export-participants/${meetingId}`, { method: 'POST' });
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || "导出失败");
    }
    if (!result.data?.url) {
      throw new Error("未获取到导出文件URL");
    }
    return { url: result.data.url, sheetData: result.sheetData };
  };
  /* 修改块结束: 导出参会成员返回数据
   * 修改时间: [TIME]
   */

  // 处理显示会议录制弹窗
  const handleShowRecord = async (meetingId: string, meetingTitle: string) => {
    setCurrentMeetingTitle(meetingTitle);
    setCurrentMeetingId(meetingId);
	setRecordDialogOpen(true);
	setLoadingRecord(true);
	const recordData: any = await handleGetMeetingRecord?.(meetingId);
	if(recordData){
		setRecordUrl(recordData.view_address);
	}
	else{
		setRecordUrl("");
	}
	setLoadingRecord(false);
  };

  // 处理打开会议录制 (在弹窗中使用)
  const handleOpenRecord = async (url: string) => {
    try {
      setLoadingRecord(true);
      if (url) {
        window.open(url, "_blank");
      } else {
        toast.error("暂无录制信息");
      }
    } catch (error: any) {
      toast.error("打开录制失败:", error);
    } finally {
      setLoadingRecord(false);
    }
  };

  // 处理显示会议转写弹窗
  /* 修改块开始: 会议转写弹窗立即打开与 loading 态立即展示
   * 修改范围: handleShowTranscript 函数体调整，setTranscriptDialogOpen(true) 和 setLoadingTranscript(true) 移到最前
   * 修改时间: 2025-06-17
   * 对应计划步骤: 1
   * 恢复方法: 将 setTranscriptDialogOpen(true) 移回 try/catch 内部
   */
  const handleShowTranscript = async (meetingId: string, meetingTitle: string) => {
    setCurrentMeetingTitle(meetingTitle);
    setCurrentMeetingId(meetingId);
    setTranscriptDialogOpen(true); // 立即弹窗
    setLoadingTranscript(true);    // 立即 loading
    try {
      const recordData: any = await handleGetMeetingRecord?.(meetingId);
      const txtSummary = recordData.meeting_summary.find(
        (item: any) => item.file_type === "txt",
      );
      const url = txtSummary?.download_address;
      setTranscriptDownloadUrl(url ?? "");
      setTranscriptTxtContent(recordData?.txtContent ?? "");
    } catch (error: any) {
      setTranscriptDownloadUrl("");
      setTranscriptTxtContent("");
    } finally {
      setLoadingTranscript(false);
    }
  };
  /* 修改块结束: 会议转写弹窗立即打开与 loading 态立即展示
   * 修改时间: 2025-06-17
   */

  // 处理显示AI会议纪要弹窗
  const handleShowAISummary = async (meetingId: string, meetingTitle: string) => {
    setCurrentMeetingTitle(meetingTitle);
    setCurrentMeetingId(meetingId);
    setAiSummaryDialogOpen(true);
	  setLoadingAISummary(true);
	try {
      const recordData: any = await handleGetMeetingRecord?.(meetingId);
      const aiSummary = recordData.ai_minutes.find(
        (item: any) => item.file_type === "txt",
      );
      const url = aiSummary?.download_address;
      setAISummaryDownloadUrl(url ?? "");
      setAISummaryTxtContent(recordData?.aiSummaryContent ?? "");
    } catch (error: any) {
      setAISummaryDownloadUrl("");
      setAISummaryTxtContent("");
    } finally {
      setLoadingAISummary(false);
    }
  };

  

  const handleGetMeetingDocs = async (
			meetingId: string,
			meetingTitle: string,
		) => {
			// 检查用户信息是否存在
			if (!user?.id) {
				toast.error("用户信息不存在，无法获取会议文档");
				toast.error("用户信息不存在，请重新登录");
				return;
			}

			setLoadingDocs(true);
			setCurrentMeetingTitle(meetingTitle);
			setDocsDialogOpen(true);

			try {
				const response = await fetch(
					`/api/meetings/docs/${meetingId}/?userId=${user.id}`,
				);

				if (!response.ok) {
					throw new Error(`获取文档失败: ${response.status}`);
				}

				const data = await response.json();

				if (!data.success) {
					throw new Error(data.error || "获取文档失败");
				}

				// console.log("会议文档列表:", data.data);

				// 根据实际返回的数据结构进行处理
				const docsData = data.data;
				if (
					docsData &&
					(docsData.doc_info_list?.length > 0 ||
						docsData.doc_list?.length > 0)
				) {
					setCurrentMeetingDocs({
						total_count: docsData.total_count || 0,
						// API可能返回doc_info_list或doc_list，我们都做兼容处理
						doc_info_list:
							docsData.doc_info_list || docsData.doc_list || [],
					});
				} else {
					// 如果没有文档，设置为null
					setCurrentMeetingDocs(null);
				}

				return data.data;
			} catch (error: any) {
				toast.error("获取会议文档错误:", error);
				setCurrentMeetingDocs(null);
				toast.error(error.message || "获取会议文档失败");
			} finally {
				setLoadingDocs(false);
			}
		};

		// 获取会议录制文件
		const handleGetMeetingRecord = async (
			meetingId: string,
		): Promise<any | null> => {
			// setCurrentMeetingTitle(meetingTitle);

			try {
				const response = await fetch(
					`/api/meetings/records/detail/${meetingId}`,
				);

				// 如果响应不是200，直接设置为null
				if (!response.ok) {
					console.log("获取录制文件响应不是200:", response.status);
					toast.error(`获取录制文件失败: ${response.status}`);
					return;
				}

				const data = await response.json();

				// 如果API返回不成功，直接设置为null
				if (!data.success) {
					console.log("获取录制文件API返回不成功:", data.error);
					toast.error(data.error || "获取录制文件失败");
					return;
				}
				return data.data;
			} catch (error: any) {
				toast.error("获取会议录制错误:", error);
				toast.error(error.message || "获取会议录制失败");
				return null;
			}
		};

  // 加载状态
//   if (isLoading && meetings.length === 0) {
//     return (
//       <div className="flex justify-center items-center h-40">
//         <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
//       </div>
//     );
//   }

  

  // 如果用户未激活，显示激活UI
  if (hasCheckedUserStatus && !isUserActive) {
    return (
					<div className="flex flex-col items-center justify-center py-12 text-center">
						<div className="bg-blue-50 p-4 rounded-full mb-4">
							<XCircle className="size-12 text-blue-400" />
						</div>
						<h2 className="text-gray-500 mb-2">用户未激活</h2>
						<div className="flex justify-center items-center gap-4">
							<Button
								variant="outline"
								onClick={handleActivation}
								className="flex items-center gap-2"
								disabled={isActivating}
							>
								<>
									<svg
										className="size-4"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										aria-hidden="true"
									>
										<path
											d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
											strokeLinecap="round"
											strokeLinejoin="round"
										/>
									</svg>
									激活腾讯会议账号
								</>
							</Button>
							{isActivating && (
								<div className="text-sm text-blue-500 flex items-center gap-2">
									<div className="inline-block animate-spin rounded-full h-3 w-3 border-b-2 border-current" />
									正在处理激活请求...
								</div>
							)}
						</div>

						{/* 激活对话框 */}
						{showActivationDialog && (
							<div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
								<div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
									<h3 className="text-lg font-semibold mb-4">
										请完成激活
									</h3>
									<p className="text-gray-600 mb-4">
										请在新打开的窗口中完成账号激活。激活完成后，请点击下方按钮刷新页面。
									</p>
									<div className="flex justify-end gap-4">
										<Button
											variant="primary"
											onClick={async () => {
												clearActivationCheck?.();
												window.location.reload();
											}}
										>
											我已完成激活
										</Button>
									</div>
								</div>
							</div>
						)}
					</div>
				);
  }

  

  /* columns 结构定义，render 里直接用父组件状态和回调 */
  const columns: TableColumnType<any>[] = [
    {
      title: (
        <button
          type="button"
          className="flex items-center cursor-pointer text-left hover:text-gray-700 text-sm font-medium"
          onClick={toggleSortDirection}
          onKeyDown={(e) => e.key === "Enter" && toggleSortDirection?.()}
        >
          开始时间
          <span className="ml-1 focus:outline-none" title={sortDirection === "asc" ? "点击降序排列" : "点击升序排列"}>
            {sortDirection === "asc" ? <ChevronUp className="size-4" /> : <ChevronDown className="size-4" />}
          </span>
        </button>
      ),
      dataIndex: "startTime",
      key: "startTime",
      width: 200,
      render: (startTime: string) => <div className="flex items-center text-black-700 text-sm">{startTime}</div>,
    },
    {
      title: "会议主题",
      dataIndex: "title",
      key: "title",
      width: 300,
      render: (title: string) => <div className="text-black-700 text-sm">{title}</div>,
    },
    {
      title: "会议号",
      dataIndex: "meetingId",
      key: "meetingId",
      width: 150,
      render: (meetingId: string) => <div className="text-black-700 text-sm">{meetingId}</div>,
    },
    showDocAndRecordButtons
      ? {
          title: "会议内容",
          key: "meetingRecord",
          width: 300,
          render: (_: any, record: any) => (
            <div className="flex items-center justify-center gap-2">
              <button type="button" onClick={() => handleGetSignInRecord(record.id, record.title)} className="text-blue-500 hover:underline truncate">签到记录</button>
              <button type="button" onClick={() => handleShowParticipants(record.id, record.title)} className="text-blue-500 hover:underline truncate">参会人员</button>
              <button type="button" onClick={() => handleGetMeetingDocs(record.id, record.title)} className="text-blue-500 hover:underline truncate">会议文档</button>
              <button type="button" onClick={() => handleShowRecord(record.id, record.title)} className="text-blue-500 hover:underline truncate">会议录制</button>
              <button type="button" onClick={() => handleShowTranscript(record.id, record.title)} className="text-blue-500 hover:underline truncate">会议记录</button>
              <button type="button" onClick={() => handleShowAISummary(record.id, record.title)} className="text-blue-500 hover:underline truncate">AI会议纪要</button>
            </div>
          ),
        }
      : {
          title: "操作",
          key: "actions",
          width: 200,
          render: (_: any, record: any) => (
            <div className="flex items-center justify-center gap-2">
              {record.status !== "MEETING_STATE_ENDED" && (
                <a className="h-7 px-2 flex items-center gap-1 text-black-700 text-sm hover:text-black-900 text-sm" href={record.joinURL || "#"} target="_blank" rel="noopener noreferrer">进入</a>
              )}
              {record.status !== "MEETING_STATE_ENDED" && (
                <Link className="h-7 px-2 flex items-center gap-1 text-black-700 text-sm hover:text-black-900 text-sm" href={`/app/${organizationSlug}/meeting/list/change_meeting/${record.id}`}>修改</Link>
              )}
              {handleCancelMeeting && record.status !== "MEETING_STATE_ENDED" && (
                <button 
                type="button"
                className="h-7 px-2 flex items-center gap-1 text-black-700 text-sm hover:text-black-900 text-sm" 
                onClick={() => handleCancelMeeting(record.id)}>
                  取消
                </button>
              )}
            </div>
          ),
        },
  ];
  /* 修改块结束: columns 外提到 MeetingTable 顶部
   * 修改时间: 2025-06-17
   */

  return (
			<>
				{/* 签到记录模态框 */}
				<SignInDialog
					open={signInDialogOpen}
					onOpenChange={setSignInDialogOpen}
					currentMeetingTitle={currentMeetingTitle}
					currentMeetingSignIn={currentMeetingSignIn}
					loadingSignIn={loadingSignIn}
					formatTimestamp={formatTimestamp}
				/>

				<DocsDialog
					open={docsDialogOpen}
					onOpenChange={setDocsDialogOpen}
					currentMeetingTitle={currentMeetingTitle}
					currentMeetingDocs={currentMeetingDocs}
					loadingDocs={loadingDocs}
					formatTimestamp={formatTimestamp}
				/>

				{/* 参会人员模态框 */}
				<ParticipantsDialog
					open={participantsDialogOpen}
					onOpenChange={setParticipantsDialogOpen}
					currentMeetingTitle={currentMeetingTitle}
					currentMeetingId={currentMeetingId}
					loadingParticipants={loadingParticipants}
					exportUrl={participantsExportUrl}
					sheetData={participantsSheetData}
				/>

				{/* 会议录制模态框 */}
				<RecordDialog
					open={recordDialogOpen}
					onOpenChange={setRecordDialogOpen}
					currentMeetingTitle={currentMeetingTitle}
					currentMeetingId={currentMeetingId}
					loadingRecord={loadingRecord}
					handleOpenRecord={handleOpenRecord}
					recordUrl={recordUrl}
				/>

				{/* 会议转写模态框 */}
				<TranscriptDialog
					open={transcriptDialogOpen}
					onOpenChange={setTranscriptDialogOpen}
					currentMeetingTitle={currentMeetingTitle}
					currentMeetingId={currentMeetingId}
					loadingTranscript={loadingTranscript}
					downloadUrl={transcriptDownloadUrl}
					txtContent={transcriptTxtContent}
				/>

				{/* AI会议纪要模态框 */}
				<AISummaryDialog
					open={aiSummaryDialogOpen}
					onOpenChange={setAiSummaryDialogOpen}
					currentMeetingTitle={currentMeetingTitle}
					currentMeetingId={currentMeetingId}
					loadingAISummary={loadingAISummary}
					downloadUrl={aiSummaryDownloadUrl}
					txtContent={aiSummaryTxtContent}
				/>

				<div>
					<AntMeetingTable
						data={meetings}
						columns={columns}
						loading={isLoading}
					/>
				</div>

				{/* 分页器 暂时不使用*/}
				{/* {meetings.length > 0 && (
					<div className="flex items-center justify-between px-4 py-3 border-t">
						<div className="flex items-center gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={() =>
									handlePageChange(currentPage - 1)
								}
								disabled={currentPage === 1}
							>
								<ChevronLeft className="h-4 w-4" />
								上一页
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={() =>
									handlePageChange(currentPage + 1)
								}
								disabled={currentPage === totalPages}
							>
								下一页
								<ChevronRight className="h-4 w-4" />
							</Button>
						</div>

						<div className="flex items-center gap-4">
							<div className="flex items-center gap-2">
								<span className="text-sm text-gray-500">
									第
								</span>
								<Input
									type="number"
									min={1}
									max={totalPages}
									value={pageInput || currentPage}
									onChange={(e) =>
										setPageInput(e.target.value)
									}
									onKeyDown={(e) => handleKeyDown(e, "page")}
									className="w-12 h-8 text-sm text-center"
									onFocus={() => setPageInput("")}
									onBlur={() => setPageInput("")}
								/>
								<span className="text-sm text-gray-500">
									页，共 {totalPages} 页
								</span>
							</div>

							
							<div className="flex items-center gap-2">
								<span className="text-sm text-gray-500">
									每页
								</span>
								<Input
									type="number"
									min={1}
									value={itemsPerPageInput}
									onChange={(e) =>
										setItemsPerPageInput(e.target.value)
									}
									onKeyDown={(e) =>
										handleKeyDown(e, "itemsPerPage")
									}
									className="w-16 h-8 text-sm"
								/>
								<span className="text-sm text-gray-500">
									条
								</span>
							</div>
						</div>
					</div>
				)} */}
			</>
		);
} 