# 股东名册API安全加密方案

## 1. 概述

本文档描述了股东名册系统API的安全加密方案，旨在保护API请求和响应中的敏感数据。方案基于 crypto-js 库实现，确保前后端使用统一的加解密逻辑。

## 2. 加密策略

### 2.1 请求加密

所有请求体采用以下格式：

```json
{
  "content": "加密后的请求参数字符串",
  "sign": "请求签名"
}
```

- **content**: 使用AES加密的原始请求参数JSON字符串（包含时间戳）
- **sign**: 请求签名，用于验证请求的合法性

原始请求参数在加密前的格式：

```json
{
  "timestamp": 1621234567890,
  "data": {
    // 实际业务请求参数
  }
}
```

- **timestamp**: 请求时间戳，用于验证请求的时效性（毫秒级时间戳）
- **data**: 实际业务请求参数

### 2.2 响应加密

#### 2.2.1 成功响应格式

成功响应采用以下格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": "加密后的响应数据字符串"
}
```

- **code**: 状态码，200表示成功
- **message**: 状态描述
- **data**: 使用AES加密的原始响应数据JSON字符串

#### 2.2.2 失败响应格式

失败响应采用以下格式：

```json
{
  "code": 400,  // 或其他错误状态码
  "message": "错误信息描述",
  "error": {
    "code": "ERROR_CODE",
    "message": "详细错误信息"
  }
}
```

- **code**: 错误状态码，如400、401、403、404、500等
- **message**: 简短错误描述
- **error**: 错误详情对象
  - **code**: 错误代码，用于前端识别具体错误类型
  - **message**: 详细错误信息，可直接展示给用户

**注意**：失败响应不进行加密，便于前端快速识别和处理错误。常见的错误状态码和含义：

| 状态码 | 描述 | 说明 |
|--------|------|------|
| 400 | 请求错误 | 请求参数无效、格式错误或签名无效 |
| 401 | 未授权 | 身份验证失败或已过期 |
| 403 | 禁止访问 | 无权访问请求的资源 |
| 404 | 资源不存在 | 请求的资源未找到 |
| 409 | 资源冲突 | 请求操作与当前资源状态冲突 |
| 500 | 服务器错误 | 服务器内部错误 |

## 3. 加密流程

### 3.1 请求加密流程

1. 构造包含时间戳和实际业务参数的原始请求对象
2. 将原始请求对象转换为JSON字符串
3. 使用AES-CBC算法对JSON字符串进行加密
4. 使用密钥和加密后的内容生成签名（HMAC-SHA256）
5. 构造最终请求体，包含加密后的内容和签名

### 3.2 响应加密流程

1. 将原始响应数据转换为JSON字符串
2. 使用AES-CBC算法对JSON字符串进行加密
3. 构造最终响应体，包含状态码、状态描述和加密后的数据

### 3.3 签名算法

签名采用HMAC-SHA256算法，这是一种结合哈希函数和密钥的消息认证码算法，提供数据完整性和身份验证。

**工作原理**：
1. 将加密后的content作为输入消息
2. 使用NEXT_PUBLIC_SHAREHOLDER_API_SECRET作为密钥
3. 通过HMAC-SHA256算法生成固定长度(256位/32字节)的哈希值，以十六进制字符串形式表示

**签名生成过程**：
```
sign = HMAC-SHA256(NEXT_PUBLIC_SHAREHOLDER_API_SECRET, content)
```

**验证过程**：
1. 服务端接收请求，提取content和sign
2. 使用相同的密钥和算法，基于接收到的content重新计算签名
3. 比较计算的签名与接收到的sign是否完全一致
4. 如不一致，表明数据被篡改或使用了错误的密钥

**签名不可逆性**：
HMAC-SHA256生成的签名是不可逆的，攻击者无法通过签名反推出密钥，保证了系统的安全性。

**实现示例**：
```typescript
// 使用crypto-js生成签名
import CryptoJS from 'crypto-js';

function generateSign(content: string, secret: string): string {
  return CryptoJS.HmacSHA256(content, secret).toString();
}

// 验证签名
function verifySign(content: string, receivedSign: string, secret: string): boolean {
  const calculatedSign = generateSign(content, secret);
  return calculatedSign === receivedSign;
}
```

## 4. 加密安全措施

### 4.1 时效性验证

- 每个请求必须包含生成时的时间戳（包含在加密的content中）
- 服务器解密内容后验证时间戳与服务器时间的差值，超过指定时间范围（如60秒）则拒绝请求
- 支持一定的时钟偏差容忍度（默认5分钟），以解决客户端与服务器之间可能存在的时间差异问题

### 4.2 服务器时间同步机制

为解决不同设备间时钟不一致的问题，系统实现了服务器时间同步机制：

#### 4.2.1 时间同步策略

系统采用以下策略获取准确的时间戳：

1. **服务器时间API**：通过内部API端点获取服务器标准时间，确保前后端时间一致性
2. **时间偏移量缓存**：计算并存储本地时间与服务器时间的偏移量，减少API请求
3. **降级策略**：当服务器时间API不可用时，根据缓存偏移量调整本地时间，或直接使用本地时间
4. **定期刷新**：定期更新时间偏移量，默认每小时刷新一次

#### 4.2.2 服务器时间API

系统使用内部API端点提供服务器时间：

- **服务器时间API**：`/api/server-time`

API返回格式：
```json
{
  "timestamp": 1621234567890,
  "datetime": "2021-05-17T12:34:56.789Z"
}
```

#### 4.2.3 时间缓存与偏移量

系统维护以下时间信息缓存：

- **标准时间戳**：从服务器API获取的标准时间
- **本地时间戳**：获取标准时间时的本地时间
- **获取时间**：最后获取标准时间的本地时间
- **偏移量**：服务器时间与本地时间的差值

通过计算偏移量，系统可以基于本地时钟推算出当前的服务器时间，无需频繁请求时间服务。

#### 4.2.4 请求加密方式

系统提供两种方式获取带时间戳的加密请求：

1. **异步方式**（推荐）：`createEncryptedRequestAsync`
   - 使用服务器时间API获取准确时间戳
   - 提供最高的时间准确性
   - 返回Promise，需要异步处理

2. **同步方式**（兼容旧代码）：`createEncryptedRequest`
   - 使用缓存的时间偏移量校准本地时间
   - 无需网络请求，执行更快
   - 准确性依赖于缓存的偏移量
   - 在服务端环境直接使用服务器当前时间

**示例代码**：

```typescript
// 异步方式（推荐）
async function sendRequest() {
  const data = { userId: "12345" };
  const encryptedRequest = await createEncryptedRequestAsync(data);
  await fetch("/api/endpoint", {
    method: "POST",
    body: JSON.stringify(encryptedRequest)
  });
}

// 同步方式（兼容旧代码）
function sendRequestSync() {
  const data = { userId: "12345" };
  const encryptedRequest = createEncryptedRequest(data);
  fetch("/api/endpoint", {
    method: "POST",
    body: JSON.stringify(encryptedRequest)
  });
}
```

### 4.3 密钥管理

系统使用以下三个环境变量来管理加密密钥：

- **NEXT_PUBLIC_SHAREHOLDER_API_KEY**: 用于AES加密/解密的密钥。必须在前后端保持完全一致，否则无法互相解密数据。
- **NEXT_PUBLIC_SHAREHOLDER_API_SECRET**: 用于生成和验证HMAC签名的密钥。必须在前后端保持完全一致，否则签名验证将失败。
- **NEXT_PUBLIC_SHAREHOLDER_API_IV**: AES-CBC模式使用的初始化向量。必须在前后端保持完全一致，长度必须为16字节。前后端共用此环境变量。

**密钥格式要求**：
- **加密密钥(API_KEY)**: 必须是强密码，至少32个字符的随机字符串，可包含字母、数字和特殊字符。
- **签名密钥(API_SECRET)**: 必须是强密码，少32个字符的随机字符串，可包含字母、数字和特殊字符。
- **初始化向量(API_IV)**: 必须是16字节长度的字符串（128位），一般以Base64或十六进制字符串的形式提供。

**重要说明**：
1. 前缀NEXT_PUBLIC_表示该变量在前端可访问，因此在生产环境中应考虑额外的安全措施。
2. 在开发环境中，可以使用简单的密钥进行测试，但在生产环境必须使用强密钥。
3. 尽管NEXT_PUBLIC_前缀的环境变量可在前端访问，但这些值在前后端必须保持一致以确保加密和解密成功。

**密钥示例**：
```
# 开发环境示例（不要在生产环境使用）
NEXT_PUBLIC_SHAREHOLDER_API_KEY="dev-api-************************************"
NEXT_PUBLIC_SHAREHOLDER_API_SECRET="dev-api-secret-12345678901234567890123456789012"
NEXT_PUBLIC_SHAREHOLDER_API_IV="1234567890123456" # 必须是16字节
```

## 5. 文件结构设计

### 5.1 工具包结构

```
packages/utils/
├── lib/
│   ├── crypto/
│   │   ├── constants.ts      # 加密常量定义
│   │   ├── encrypt.ts        # 加密工具函数
│   │   ├── decrypt.ts        # 解密工具函数
│   │   ├── sign.ts           # 签名生成与验证
│   │   └── index.ts          # 导出所有加密工具
│   └── ...
└── index.ts                  # 导出工具函数
```

### 5.2 API中间件结构

```
packages/api/src/
├── middleware/
│   ├── crypto.ts             # 加解密中间件
│   └── ...
├── lib/
│   ├── crypto-helper.ts      # 服务端加解密辅助函数
│   └── ...
├── routes/
│   ├── time.ts               # 服务器时间API
│   ├── shareholder-registry/ # 股东名册相关路由
│   │   ├── router.ts         # 主路由文件
│   │   └── ...
│   └── ...
└── app.ts                    # API应用程序入口
```

## 6. 代码实现方向

### 6.1 加密工具核心功能

加密工具需要实现以下核心功能：

1. **常量定义**：定义加密算法、密钥长度、请求有效期等常量
2. **服务器时间获取**：获取服务器标准时间，确保前后端时间一致
3. **加密函数**：实现AES-CBC加密，包装业务数据和时间戳
4. **解密函数**：实现AES-CBC解密，解析出业务数据和时间戳
5. **签名函数**：使用HMAC-SHA256算法生成和验证签名
6. **时效性验证**：检查解密后的时间戳是否在有效期内

### 6.2 服务器时间同步的实现

服务器时间获取机制主要包含以下功能：

1. **服务器时间API**：提供当前服务器时间的API端点
2. **时间偏移量计算**：计算本地时间与服务器时间的偏差值
3. **时间缓存管理**：缓存时间信息，减少API请求频率
4. **降级策略**：当时间API不可用时，使用本地时间加偏移量或直接使用本地时间
5. **时间校准**：根据缓存的偏移量校准本地时间

### 6.3 中间件核心功能

加密中间件需要实现以下功能：

1. **请求解密**：解密请求中的content字段，获取原始请求数据
2. **签名验证**：验证请求签名是否有效
3. **时效性检查**：检查请求是否在有效期内，允许一定的时钟偏差
4. **响应加密**：加密响应数据，返回统一格式的响应

### 6.4 路由集成

加密中间件应用于股东名册所有API路由，提供统一的加解密处理。路由处理函数只需关注业务逻辑，不需要处理加解密细节。

## 7. 环境变量配置

在项目的`.env.local.example`文件中添加以下环境变量：

```
# 股东名册API加密密钥
NEXT_PUBLIC_SHAREHOLDER_API_KEY="YOUR_PUBLIC_API_KEY_HERE"  # 前端和后端共用的AES加密/解密密钥
NEXT_PUBLIC_SHAREHOLDER_API_SECRET="YOUR_SERVER_SIDE_SECRET_HERE"  # 用于生成和验证HMAC签名的密钥
NEXT_PUBLIC_SHAREHOLDER_API_IV="YOUR_PUBLIC_IV_HERE"  # AES-CBC加密的初始化向量，前后端共用
```

## 8. 使用指南

### 8.1 基本使用

```typescript
// 导入加密工具
import { createEncryptedRequestAsync, decryptRequestData } from '@/packages/utils/lib/crypto';

// 异步创建加密请求
async function createRequest() {
  const data = { userId: "123", action: "getData" };
  const encryptedRequest = await createEncryptedRequestAsync(data);
  return encryptedRequest;
}

// 解密响应数据
function handleResponse(encryptedData: string) {
  const { data } = decryptRequestData(encryptedData);
  return data;
}
```

### 8.2 时间同步机制使用

```typescript
import { getStandardTime } from '@/packages/utils/lib/crypto/encrypt';

// 获取当前服务器时间
async function getCurrentTime() {
  // 获取服务器时间（自动使用缓存，减少API调用）
  const serverTime = await getStandardTime();
  console.log('服务器时间：', new Date(serverTime).toISOString());
  
  // 强制刷新缓存，获取最新服务器时间
  const freshTime = await getStandardTime(true);
  console.log('刷新后的服务器时间：', new Date(freshTime).toISOString());
  
  return serverTime;
}
```

### 8.3 兼容模式（同步API）

对于不支持异步操作的场景，可以使用同步API：

```typescript
import { createEncryptedRequest } from '@/packages/utils/lib/crypto';

// 同步方式创建加密请求（使用缓存的时间偏移量）
function createRequestSync() {
  const data = { userId: "123", action: "getData" };
  const encryptedRequest = createEncryptedRequest(data);
  return encryptedRequest;
}
```

## 9. 安全建议

1. 生产环境中使用足够强度的密钥和IV（至少16字节）
2. 定期轮换密钥和IV
3. 使用HTTPS传输层加密
4. 实施IP白名单或速率限制
5. 记录加密失败和验证失败事件
6. 确保服务器时间准确，服务器应定期与NTP服务同步

## 10. 测试方案
使用apipost测试工具来验证接口：加密请求、加密返回、60秒过期、无效签名
1. 测试前端加密和后端解密流程
2. 测试过期请求处理
3. 测试无效签名处理
4. 测试响应加密和解密
5. 测试服务器时间API的可用性
6. 测试时间API不可用时的降级策略

