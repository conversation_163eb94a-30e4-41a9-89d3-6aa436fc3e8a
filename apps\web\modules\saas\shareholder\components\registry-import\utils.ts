import { toast } from "sonner";
import { isSupportedFileType, validateFileName } from "@saas/shareholder/lib/file-parser";
import { FILENAME_ERROR_MESSAGES } from "@saas/shareholder/lib/config";
import type { ShareholderRegistryParseResult } from "@saas/shareholder/lib/dbf-parser";

/**
 * 过滤和验证上传的文件
 * 
 * @param {File[]} files - 待处理的文件列表
 * @returns {File[]} 过滤后的有效文件列表
 * 
 * @update 2025-06-12 11:29:46 更新为允许所有文件类型上传，移除过滤逻辑
 * @update 2025-06-04 16:15:02 从ShareholderRegistryImport组件中提取
 */
export function filterFiles(files: File[]): File[] {
  if (!files || files.length === 0) {
    return [];
  }
  
  // 允许所有文件类型上传，仅过滤超过大小限制的文件
  return files.filter(
    (file) => file.size <= 20 * 1024 * 1024
  );
}

/**
 * 验证文件类型和大小
 * 在点击开始导入按钮时调用，检查文件格式是否符合要求
 *
 * @param {File[]} files - 待验证的文件列表
 * @returns {Object} 验证结果，包含有效文件列表和错误信息
 *
 * <AUTHOR>
 * @update 2025-06-23 16:01:13 - 添加严格文件名验证逻辑
 * @update 2025-06-12 11:29:46 新增函数，用于在点击导入按钮时校验文件
 */
export function validateFiles(files: File[]): {
  validFiles: File[];
  errorFiles: Array<{file: File, reason: string}>;
  allValid: boolean;
} {
  if (!files || files.length === 0) {
    return {
      validFiles: [],
      errorFiles: [],
      allValid: false
    };
  }

  const validFiles: File[] = [];
  const errorFiles: Array<{file: File, reason: string}> = [];

  // 校验每个文件
  for (const file of files) {
    // 检查文件大小
    if (file.size > 20 * 1024 * 1024) {
      errorFiles.push({
        file,
        reason: "文件大小超过20MB限制"
      });
      continue;
    }

    // 检查文件类型
    if (!isSupportedFileType(file.name)) {
      errorFiles.push({
        file,
        reason: FILENAME_ERROR_MESSAGES.UNSUPPORTED_TYPE
      });
      continue;
    }

    // 严格文件名验证
    const fileNameValidation = validateFileName(file);
    if (!fileNameValidation.isValid) {
      let errorMessage = fileNameValidation.error || "文件名格式不正确";
      if (fileNameValidation.suggestion) {
        errorMessage += `。${fileNameValidation.suggestion}`;
      }
      errorFiles.push({
        file,
        reason: errorMessage
      });
      continue;
    }

    // 通过验证
    validFiles.push(file);
  }

  return {
    validFiles,
    errorFiles,
    allValid: errorFiles.length === 0 && validFiles.length > 0
  };
}

/**
 * 检查文件是否为Excel格式的名册文件
 * 
 * @param {File} file - 待检查的文件
 * @returns {boolean} 是否为Excel格式的名册文件
 * 
 * @update 2025-06-04 16:15:02 从ShareholderRegistryImport组件中提取
 */
export function isRegistryExcelFile(file: File): boolean {
  // 检查是否是Excel文件（XLS或XLSX）
  const isExcelFile = file.name.toLowerCase().endsWith('.xls') || file.name.toLowerCase().endsWith('.xlsx');
  
  // 检查是否是名册文件（通过文件名判断，包括t1、t2、t3等）
  return isExcelFile && 
    (file.name.toLowerCase().includes('t1') || 
     file.name.toLowerCase().includes('t2') || 
     file.name.toLowerCase().includes('t3') ||
     file.name.toLowerCase().includes('名册'));
}

/**
 * 检查是否有重复文件
 * 
 * @param {File[]} existingFiles - 现有文件列表 
 * @param {File[]} newFiles - 新文件列表
 * @returns {File[]} 不重复的新文件列表
 * 
 * @update 2025-06-04 16:15:02 从ShareholderRegistryImport组件中提取
 */
export function filterDuplicateFiles(existingFiles: File[], newFiles: File[]): File[] {
  // 检查是否有重复文件（根据文件名和大小判断）
  const existingFileNames = existingFiles.map(f => `${f.name}-${f.size}`);
  
  return newFiles.filter(
    file => !existingFileNames.includes(`${file.name}-${file.size}`)
  );
}

/**
 * 显示导入结果的汇总信息
 * 
 * @param {File[]} files - 文件列表
 * @param {ShareholderRegistryParseResult[]} results - 解析结果列表
 * 
 * @update 2025-06-04 16:15:02 从ShareholderRegistryImport组件中提取
 */
export function showImportSummary(files: File[], results: ShareholderRegistryParseResult[]): void {
  const successCount = results.filter(r => r?.success === true).length;
  const totalRecords = results.reduce((total, result) => {
    return total + (result?.success ? (result?.recordCount ?? 0) : 0);
  }, 0);

  // 只显示一次汇总成功信息，仅当成功数大于0时
  if (successCount > 0) {
    // 如果有多个文件，可以提供批量导入的汇总信息
    if (files.length > 1) {
      toast.success("批量导入完成", {
        description: `成功导入 ${successCount}/${files.length} 个文件，共 ${totalRecords} 条记录`,
      });
    }
  }
}

/**
 * 创建进度定时器
 * 
 * @param {Function} setUploadProgress - 设置上传进度的函数
 * @param {Function} callback - 进度回调函数
 * @returns {NodeJS.Timeout} 定时器引用
 * 
 * @update 2025-06-04 16:15:02 从ShareholderRegistryImport组件中提取
 */
export function createProgressTimer(
  setUploadProgress: (progress: number) => void, 
  callback: (progress: number) => void = () => {}
): NodeJS.Timeout {
  // 从0%开始
  let progress = 0;
  setUploadProgress(0);

  // 创建定时器，模拟进度更新
  const timer = setInterval(() => {
    // 根据当前进度动态调整增长速度，越接近90%增长越慢
    const increment = Math.max(0.5, 5 * (1 - progress / 90));
    progress += increment;

    // 确保进度不超过90%（最后10%在上传完成后设置）
    if (progress > 90) {
      progress = 90;
      // 达到90%时不停止定时器，保持在90%直到后端响应
    }

    // 更新进度并回调
    setUploadProgress(Math.round(progress));
    callback(Math.round(progress));
  }, 200);

  return timer;
}

/**
 * 完成进度条动画
 * 
 * @param {number} currentProgress - 当前进度
 * @param {Function} setUploadProgress - 设置上传进度的函数
 * 
 * @update 2025-06-04 16:15:02 从ShareholderRegistryImport组件中提取
 */
export function finalizeProgressAnimation(
  currentProgress: number,
  setUploadProgress: (progress: number) => void
): void {
  // 从当前进度（应该是90%左右）开始
  let progress = currentProgress;
  const progressInterval = setInterval(() => {
    progress += 2;
    if (progress >= 100) {
      progress = 100;
      clearInterval(progressInterval);
    }
    setUploadProgress(progress);
  }, 50);
  
  // 确保在500毫秒后清除定时器（以防卡住）并设置为100%
  setTimeout(() => {
    clearInterval(progressInterval);
    setUploadProgress(100);
  }, 500);
}

/**
 * 显示失败进度动画
 * 
 * @param {number} currentProgress - 当前进度
 * @param {Function} setUploadProgress - 设置上传进度的函数
 * 
 * @update 2025-06-04 16:15:02 从ShareholderRegistryImport组件中提取
 */
export function showFailureAnimation(
  currentProgress: number,
  setUploadProgress: (progress: number) => void
): void {
  // 从当前进度逐渐减少到0，给用户一个视觉反馈
  let progress = currentProgress;
  const progressInterval = setInterval(() => {
    progress -= 5;
    if (progress <= 0) {
      progress = 0;
      clearInterval(progressInterval);
    }
    setUploadProgress(progress);
  }, 30);
  
  // 确保在300毫秒后清除定时器并设置为0
  setTimeout(() => {
    clearInterval(progressInterval);
    setUploadProgress(0);
  }, 300);
} 