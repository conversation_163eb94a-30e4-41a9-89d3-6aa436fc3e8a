/* 修改块开始: 将UserAvatar改为使用BoringAvatar
 * 修改范围: 整个文件
 * 修改时间: 2025-04-29
 * 对应计划步骤: 1-5
 * 恢复方法: 删除此修改块内所有代码，恢复上方注释中的原代码
 */
"use client";

import { config } from "@repo/config";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import BoringAvatar from "boring-avatars";
import { forwardRef, useMemo } from "react";
import { useIsClient } from "usehooks-ts";

export const UserAvatar = forwardRef<
	HTMLSpanElement,
	{
		name: string;
		avatarUrl?: string | null;
		className?: string;
	}
>(({ name, avatarUrl, className }, ref) => {
	/* 原代码:
	 * const initials = useMemo(
	 *	() =>
	 *		name
	 *			.split(" ")
	 *			.slice(0, 2)
	 *			.map((n) => n[0])
	 *			.join(""),
	 *	[name],
	 * );
	 * 修改原因: 改为使用BoringAvatar库生成默认头像，与组织头像保持一致
	 * 修改时间: 2025-04-29
	 * 修改人: LLM
	 * 关联需求: 将个人用户默认头像实现方式改成和组织头像一样
	 * 恢复方法: 删除当前代码，取消上方原代码的注释
	 */
	const isClient = useIsClient();
	const avatarColors = useMemo(() => {
		if (typeof window === "undefined") {
			return [];
		}

		const styles = getComputedStyle(window.document.documentElement);
		return [
			styles.getPropertyValue("--color-highlight"),
			styles.getPropertyValue("--color-accent"),
			styles.getPropertyValue("--color-highlight"),
		];
	}, []);

	const avatarSrc = useMemo(
		() =>
			avatarUrl
				? avatarUrl.startsWith("http")
					? avatarUrl
					: `/image-proxy/${config.storage.bucketNames.avatars}/${avatarUrl}`
				: undefined,
		[avatarUrl],
	);

	if (!isClient) {
		return null;
	}

	return (
		<Avatar ref={ref} className={className}>
			<AvatarImage src={avatarSrc} />
			{/* 原代码:
			 * <AvatarFallback className="bg-secondary/10 text-secondary">
			 *	{initials}
			 * </AvatarFallback>
			 * 修改原因: 改为使用BoringAvatar库生成默认头像，与组织头像保持一致
			 * 修改时间: 2025-04-29
			 * 修改人: LLM
			 * 关联需求: 将个人用户默认头像实现方式改成和组织头像一样
			 * 恢复方法: 删除当前代码，取消上方原代码的注释
			 */}
			<AvatarFallback>
				<BoringAvatar
					size={96}
					name={name}
					variant="beam"
					colors={avatarColors}
					square
				/>
			</AvatarFallback>
		</Avatar>
	);
});

UserAvatar.displayName = "UserAvatar";
/* 修改块结束: 将UserAvatar改为使用BoringAvatar
 * 修改时间: 2025-04-29
 */
