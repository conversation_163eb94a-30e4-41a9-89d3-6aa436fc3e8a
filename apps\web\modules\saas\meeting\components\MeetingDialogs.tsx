"use client";

import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	DialogTitle,
	DialogDescription,
} from "@ui/components/dialog";
import { File, Users } from "lucide-react";
import type { MeetingDocsResponse } from "./types";
import { AntMeetingTable } from "./ant-meeting-table";
import { useState } from "react";
import { toast } from "sonner";
import { Check, Copy } from "lucide-react";

interface CopyableEmailProps {
	email: string;
	className?: string;
}

function CopyableEmail({ email, className = "" }: CopyableEmailProps) {
	const [copied, setCopied] = useState(false);

	const handleCopy = async () => {
		try {
			await navigator.clipboard.writeText(email);
			setCopied(true);
			toast.success("邮箱已复制到剪贴板");
			// setTimeout(() => setCopied(false), 2000);
		} catch (error) {
			toast.error("复制失败，请手动复制");
		}
	};

	return (
		<div className={`inline-flex items-center gap-2 ${className}`}>
			<span className="text-muted-foreground">{email}</span>
			<button
				type="button"
				onClick={handleCopy}
				className="inline-flex items-center justify-center w-6 h-6 rounded-md hover:bg-muted transition-colors cursor-pointer"
				title={copied ? "已复制" : "点击复制邮箱"}
			>
				{copied ? (
					<Check className="w-4 h-4 text-green-600" />
				) : (
					<Copy className="w-4 h-4 text-muted-foreground hover:text-foreground" />
				)}
			</button>
		</div>
	);
}


/**
 * 会议文档对话框组件的属性接口
 * @interface DocsDialogProps
 */
interface DocsDialogProps {
	/** 控制对话框是否打开 */
	open: boolean;
	/** 对话框打开状态变化的回调函数 */
	onOpenChange: (open: boolean) => void;
	/** 当前会议标题 */
	currentMeetingTitle: string;
	/** 当前会议的文档列表数据，可能为空 */
	currentMeetingDocs: MeetingDocsResponse | null;
	/** 文档加载状态标志 */
	loadingDocs: boolean;
	/** 时间戳格式化函数，用于显示文档的最后编辑时间 */
	formatTimestamp: (timestamp: number | string) => string;
}

export function DocsDialog({
	open,
	onOpenChange,
	currentMeetingTitle,
	currentMeetingDocs,
	loadingDocs,
	formatTimestamp,
}: DocsDialogProps) {
	// 会议文档表格 columns 和数据
	const docColumns = [
		{ title: "文档标题", dataIndex: "doc_title", key: "doc_title" },
		{
			title: "创建者",
			dataIndex: "doc_creator_user_name",
			key: "doc_creator_user_name",
		},
		{
			title: "最后编辑",
			dataIndex: "doc_edit_time",
			key: "doc_edit_time",
			render: (value: string) => formatTimestamp(value),
		},
		{
			title: "最后编辑者",
			dataIndex: "doc_editor_user_name",
			key: "doc_editor_user_name",
		},
	];
	const docRows = (currentMeetingDocs?.doc_info_list || []).map(
		(row: any, idx: number) => ({ ...row, id: String(idx) }),
	);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<File className="h-5 w-5" />
						会议文档
					</DialogTitle>
					<DialogDescription>
						{currentMeetingTitle} 的会议文档
						{/* {currentMeetingDocs && currentMeetingDocs.total_count > 0 && (
              <span className="ml-1 text-xs">（共 {currentMeetingDocs.total_count} 个）</span>
            )} */}
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{loadingDocs ? (
						<div className="flex justify-center items-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
						</div>
					) : (
						<>
							{(currentMeetingDocs?.doc_info_list?.length ?? 0) >
							0 ? (
								<div className="border rounded-lg overflow-hidden">
									<div className="divide-y divide-border">
										<AntMeetingTable
											columns={docColumns}
											data={docRows}
											loading={loadingDocs}
										/>
									</div>
								</div>
							) : (
								<div className="text-center py-8 text-muted-foreground">
									<File className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
									<p>暂无会议文档</p>
								</div>
							)}
						</>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

// 获取文件类型对应图标
function getFileTypeIcon(fileType: string) {
	switch (fileType.toLowerCase()) {
		case "pdf":
			return (
				<svg
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<title>PDF图标</title>
					<path d="M12 16H16V20H8V4H16V8H12V16Z" fill="#FF5252" />
					<path d="M16 4L20 8H16V4Z" fill="#FF5252" />
				</svg>
			);
		case "docx":
		case "docs":
			return (
				<svg
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<title>Word图标</title>
					<path
						d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"
						fill="#4285F4"
					/>
					<path d="M14 2V8H20L14 2Z" fill="#A1C2FA" />
					<path d="M8 14H16V16H8V14Z" fill="white" />
					<path d="M8 10H16V12H8V10Z" fill="white" />
					<path d="M8 18H12V20H8V18Z" fill="white" />
				</svg>
			);
		case "txt":
			return (
				<svg
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<title>Text图标</title>
					<path
						d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"
						fill="#607D8B"
					/>
					<path d="M14 2V8H20L14 2Z" fill="#B0BEC5" />
					<path d="M8 14H16V16H8V14Z" fill="white" />
					<path d="M8 10H16V12H8V10Z" fill="white" />
					<path d="M8 18H12V20H8V18Z" fill="white" />
				</svg>
			);
		case "mp4":
			return (
				<svg
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<title>Video图标</title>
					<path
						d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5V7.5L16 12L10 16.5Z"
						fill="#FF5722"
					/>
				</svg>
			);
		default:
			return <File className="size-4" />;
	}
}

/**
 * 会议录制对话框组件的属性接口
 * @interface RecordDialogProps
 */
interface RecordDialogProps {
	/** 控制对话框是否打开 */
	open: boolean;
	/** 对话框打开状态变化的回调函数 */
	onOpenChange: (open: boolean) => void;
	/** 当前会议标题 */
	currentMeetingTitle: string;
	/** 会议ID，用于获取录制信息 */
	currentMeetingId: string;
	/** 录制数据加载状态标志 */
	loadingRecord: boolean;
	/** 获取并打开录制函数 */
	handleOpenRecord: (url: string) => Promise<void>;
	/** 录制视频URL */
	recordUrl: string;
}

export function RecordDialog({
	open,
	onOpenChange,
	currentMeetingTitle,
	loadingRecord,
	recordUrl,
	handleOpenRecord,
}: RecordDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<svg
							className="h-5 w-5"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
						>
							<title>录制图标</title>
							<circle cx="12" cy="12" r="10" />
							<polygon points="10,8 16,12 10,16 10,8" />
						</svg>
						会议录制
					</DialogTitle>
					<DialogDescription>
						{currentMeetingTitle} 的录制视频
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{loadingRecord ? (
						<div className="flex justify-center items-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
						</div>
					) : (
						<div className="text-center py-8">
							<div className="bg-muted/50 p-6 rounded-lg flex flex-col items-center justify-center">
								{recordUrl ? (
									<>
										<button
											type="button"
											aria-label="播放录制"
											onClick={() =>
												handleOpenRecord(recordUrl)
											}
											disabled={loadingRecord}
											className="p-0 m-0 bg-none border-none shadow-none outline-none focus:outline-none"
										>
											<svg
												className="h-16 w-16 text-primary mx-auto mb-4"
												viewBox="0 0 24 24"
												fill="none"
												stroke="currentColor"
												strokeWidth="2"
											>
												<title>
													跳转腾讯会议查看录制
												</title>
												<circle
													cx="12"
													cy="12"
													r="10"
												/>
												<polygon points="10,8 16,12 10,16 10,8" />
											</svg>
										</button>
										<div className="text-sm text-muted-foreground">
											跳转腾讯会议查看录制
										</div>
									</>
								) : (
									<div className="text-center py-8 text-muted-foreground">
										<p>暂无录制</p>
									</div>
								)}
							</div>
						</div>
					)}
				</div>

				{/* <div className="space-y-4">
						{loadingRecord ? (
							<div className="flex justify-center items-center py-8">
								<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
							</div>
						) : (
							<div className="text-center py-8">
                <div className="bg-muted/50 p-6 rounded-lg flex flex-col items-center justify-center">
                  <button
                    type="button"
                    aria-label="播放录制"
                    onClick={() => handleOpenRecord(recordUrl)}
                    disabled={loadingRecord}
                    className="p-0 m-0 bg-none border-none shadow-none outline-none focus:outline-none"
                  >
                    {!recordUrl ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mx-auto" />
                    ) : (
                      <svg
                        className="h-16 w-16 text-primary mx-auto mb-4"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <title>跳转腾讯会议查看录制</title>
                        <circle cx="12" cy="12" r="10" />
                        <polygon points="10,8 16,12 10,16 10,8" />
                      </svg>
                    )}
                  </button>
                  <div className="text-sm text-muted-foreground">跳转腾讯会议查看录制</div>
                </div>
              </div>
						)}
					</div> */}
			</DialogContent>
		</Dialog>
	);
}

/**
 * 会议记录对话框组件的属性接口
 * @interface TranscriptDialogProps
 */
interface TranscriptDialogProps {
	/** 控制对话框是否打开 */
	open: boolean;
	/** 对话框打开状态变化的回调函数 */
	onOpenChange: (open: boolean) => void;
	/** 当前会议标题 */
	currentMeetingTitle: string;
	/** 会议ID，用于获取记录文件 */
	currentMeetingId: string;
	/** 记录数据加载状态标志 */
	loadingTranscript: boolean;
	/* 修改块开始: 新增 downloadUrl 和 txtContent props
	 * 修改范围: TranscriptDialogProps 增加 downloadUrl 和 txtContent
	 * 修改时间: 2025-06-17
	 * 对应计划步骤: 3
	 * 恢复方法: 删除 downloadUrl 和 txtContent 相关定义
	 */
	downloadUrl: string;
	txtContent: string;
	/* 修改块结束: 新增 downloadUrl 和 txtContent props
	 * 修改时间: 2025-06-17
	 */
}

export function TranscriptDialog({
	open,
	onOpenChange,
	currentMeetingTitle,
	loadingTranscript,
	/* 修改块开始: 解构 downloadUrl 和 txtContent
	 * 修改范围: 解构新增 props
	 * 修改时间: 2025-06-17
	 * 对应计划步骤: 4
	 * 恢复方法: 删除 downloadUrl 和 txtContent 解构
	 */
	downloadUrl,
	txtContent,
	/* 修改块结束: 解构 downloadUrl 和 txtContent
	 * 修改时间: 2025-06-17
	 */
}: TranscriptDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<svg
							className="h-5 w-5"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
						>
							<title>记录图标</title>
							<path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" />
							<path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" />
							<path d="M8 7h8" />
							<path d="M8 11h8" />
							<path d="M8 15h6" />
						</svg>
						会议记录
					</DialogTitle>
					<DialogDescription>
						<span className="flex items-center gap-2">
							{currentMeetingTitle} 的记录文件
							{downloadUrl && (
								<button
									type="button"
									onClick={() => {
										window.open(downloadUrl, "_blank");
									}}
									title="下载会议记录文件"
									className="hover:underline text-blue-500"
								>
									下载
								</button>
							)}
						</span>
					</DialogDescription>
				</DialogHeader>
				<div className="space-y-4">
					{loadingTranscript ? (
						<div className="flex justify-center items-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
						</div>
					) : (
						<div className="text-center py-8">
							<div className="bg-muted/50 p-6 rounded-lg">
								{/* 修改块开始: 下载按钮和 txtContent 展示
								 * 修改范围: 展示下载按钮和 txtContent
								 * 修改时间: 2025-06-17
								 * 对应计划步骤: 4
								 * 恢复方法: 删除此块，恢复原有内容
								 */}

								<div className="text-left whitespace-pre-line text-sm max-h-96 overflow-y-auto border rounded p-4 bg-white dark:bg-muted/30">
									{txtContent ? (
										txtContent
									) : (
										<span className="text-muted-foreground">
											无
										</span>
									)}
								</div>
								{/* 修改块结束: 下载按钮和 txtContent 展示
								 * 修改时间: 2025-06-17
								 */}
							</div>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

/**
 * AI会议纪要对话框组件的属性接口
 * @interface AISummaryDialogProps
 */
interface AISummaryDialogProps {
	/** 控制对话框是否打开 */
	open: boolean;
	/** 对话框打开状态变化的回调函数 */
	onOpenChange: (open: boolean) => void;
	/** 当前会议标题 */
	currentMeetingTitle: string;
	/** 会议ID，用于获取AI纪要 */
	currentMeetingId: string;
	/** AI纪要数据加载状态标志 */
	loadingAISummary: boolean;
	/** 获取AI纪要函数 */
	downloadUrl: string;
	txtContent: string;
}

export function AISummaryDialog({
	open,
	onOpenChange,
	currentMeetingTitle,
	loadingAISummary,
	downloadUrl,
	txtContent,
}: AISummaryDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<svg
							className="h-5 w-5"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
						>
							<title>AI纪要图标</title>
							<path d="M12 6V4a2 2 0 0 1 2-2h5a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2" />
							<path d="M3 10a2 2 0 0 1 2-2h5a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-9z" />
							<circle cx="7" cy="14" r="1" />
							<path d="m15.5 17.5 2.5 2.5L22 16" />
						</svg>
						AI会议纪要
					</DialogTitle>
					<DialogDescription>
						<span className="flex items-center gap-2">
							{currentMeetingTitle} 的AI会议纪要
							{downloadUrl && (
								<button
									type="button"
									onClick={() => {
										window.open(downloadUrl, "_blank");
									}}
									title="下载AI智能纪要文件"
									className="hover:underline text-blue-500"
								>
									下载
								</button>
							)}
						</span>
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{loadingAISummary ? (
						<div className="flex justify-center items-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
						</div>
					) : (
						<div className="text-center py-8">
							<div className="bg-muted/50 p-6 rounded-lg">
								{/* 修改块开始: 下载按钮和 txtContent 展示
								 * 修改范围: 展示下载按钮和 txtContent
								 * 修改时间: 2025-06-17
								 * 对应计划步骤: 4
								 * 恢复方法: 删除此块，恢复原有内容
								 */}

								<div className="text-left whitespace-pre-line text-sm max-h-96 overflow-y-auto border rounded p-4 bg-white dark:bg-muted/30">
									{txtContent ? (
										txtContent
									) : (
										<span className="text-muted-foreground">
											无
										</span>
									)}
								</div>
								{/* 修改块结束: 下载按钮和 txtContent 展示
								 * 修改时间: 2025-06-17
								 */}
							</div>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

export interface SignInDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	currentMeetingTitle: string;
	currentMeetingSignIn: any | null;
	loadingSignIn: boolean;
	formatTimestamp: (timestamp: number | string) => string;
}

export function SignInDialog({
	open,
	onOpenChange,
	currentMeetingTitle,
	currentMeetingSignIn,
	loadingSignIn,
	formatTimestamp,
}: SignInDialogProps) {
	// 状态显示辅助函数
	function getSignInStatusDisplay(status: string | number) {
		switch (status) {
			case 1:
			case "1":
				return {
					text: "进行中",
					color: "text-green-600 dark:text-green-400",
				};
			case 0:
			case "0":
				return { text: "未开始", color: "text-muted-foreground" };
			case 2:
			case "2":
				return {
					text: "已结束",
					color: "text-yellow-600 dark:text-yellow-400",
				};
			default:
				return { text: "未知", color: "text-muted-foreground" };
		}
	}

	// 在签到记录展示处，替换为 AntMeetingTable 渲染
	// 假设 currentMeetingSignIn.sign_in_list 为签到记录数组，loadingSignIn 为加载状态
	const statusMap: Record<string, string> = {
		"0": "未开始",
		"1": "进行中",
		"2": "已结束",
	};
	const signInColumns = [
		{ title: "签到ID", dataIndex: "sign_in_id", key: "sign_in_id" },
		{
			title: "开始时间",
			dataIndex: "start_time",
			key: "start_time",
			render: (value: string) => formatTimestamp(value),
		},
		{
			title: "状态",
			dataIndex: "sign_in_status",
			key: "sign_in_status",
			render: (status: number) => statusMap[String(status)],
		},
		// { title: "说明", dataIndex: "sign_in_specification", key: "sign_in_specification" },
	];
	const signInRows = (currentMeetingSignIn?.sign_in_list || []).map(
		(row: any, idx: number) => ({ ...row, id: String(idx) }),
	);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Users className="h-5 w-5" />
						会议签到记录
					</DialogTitle>
					<DialogDescription>
						{currentMeetingTitle} 的签到记录
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{loadingSignIn ? (
						<div className="flex justify-center items-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
						</div>
					) : currentMeetingSignIn &&
						typeof currentMeetingSignIn === "object" ? (
						<>
							{/* 签到记录列表 */}
							{currentMeetingSignIn.sign_in_list &&
							currentMeetingSignIn.sign_in_list.length > 0 ? (
								<AntMeetingTable
									columns={signInColumns}
									data={signInRows}
									loading={loadingSignIn}
								/>
							) : (
								<div className="text-center py-8 text-muted-foreground">
									<Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
									<p>暂无签到记录</p>
								</div>
							)}
						</>
					) : (
						<div className="text-center py-8 text-muted-foreground">
							<p>获取签到记录失败</p>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

/**
 * 参会人员对话框组件的属性接口
 * @interface ParticipantsDialogProps
 */
interface ParticipantsDialogProps {
	/** 控制对话框是否打开 */
	open: boolean;
	/** 对话框打开状态变化的回调函数 */
	onOpenChange: (open: boolean) => void;
	/** 当前会议标题 */
	currentMeetingTitle: string;
	/** 会议ID，用于下载参会人员列表 */
	currentMeetingId: string;
	/** 参会人员数据加载状态标志 */
	loadingParticipants: boolean;
	/** 导出参会人员函数 */

	/** 新增导出数据props */
	exportUrl: string | null;
	sheetData: any;
}

export function ParticipantsDialog({
	open,
	onOpenChange,
	currentMeetingTitle,
	loadingParticipants,

	exportUrl,
	sheetData,
}: ParticipantsDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-7xl max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Users className="h-5 w-5" />
						参会人员
					</DialogTitle>
					<DialogDescription>
						<span className="flex items-center gap-2">
							{currentMeetingTitle} 的参会人员
							{exportUrl && (
								<button
									type="button"
									onClick={() => {
										window.open(exportUrl, "_blank");
									}}
									title="下载参会人员表"
									className="hover:underline text-blue-500"
									// className="justify-center border font-medium enabled:cursor-pointer transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:pointer-events-none disabled:opacity-50 [&>svg+svg]:hidden size-9 [&>svg]:m-0 [&>svg]:opacity-100 w-9 flex items-center gap-1.5 shrink-0 text-sm h-9 rounded-md bg-background text-foreground border-border hover:bg-accent"
								>
									下载
								</button>
							)}
						</span>
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{loadingParticipants ? (
						<div className="flex justify-center items-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
						</div>
					) : (
						<div className="text-center py-8">
							<div>
								{sheetData &&
								Array.isArray(sheetData) &&
								sheetData.length > 0 ? (
									(() => {
										// 1. 找到表头行（value包含"用户昵称（入会昵称）"）
										const headerRow = sheetData.find(
											(row) =>
												Object.values(row).includes(
													"用户昵称（入会昵称）",
												),
										);
										const headerKeys =
											Object.keys(headerRow);
										const headers = headerKeys.map(
											(key) => headerRow[key],
										);
										// 2. 过滤出数据行（排除表头行，且只保留所有headerKeys都存在的行）
										const dataRows = sheetData
											.filter(
												(row) =>
													row !== headerRow &&
													headerKeys.every(
														(key) => key in row,
													),
											)
											.map((row, idx) => ({
												...row,
												id: String(idx),
											}));

										// 新增：生成 columns
										const columns = headerKeys.map(
											(key, idx) => ({
												title: headers[idx],
												dataIndex: key,
												key,
											}),
										);

										return (
											<AntMeetingTable
												columns={columns}
												data={dataRows}
												loading={false}
											/>
										);
									})()
								) : (
									<div className="text-muted-foreground mt-4">
										暂无参会人员数据
									</div>
								)}
							</div>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

interface InfoDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	email: string;
}

export function InfoDialog({
	open,
	onOpenChange,
	email,
}: InfoDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-2xl max-h-[60vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2 justify-center">
						温馨提示
					</DialogTitle>
				</DialogHeader>
				<div className="text-center">
					<div className="text-muted-foreground mt-4 text-center text-sm">
						本月平台预约次数已用完，如需预约新会议，可前往腾讯会议官网或APP进行。
					</div>
					<div className="text-muted-foreground mt-4 text-center text-sm">
						登录账号：
						<CopyableEmail email={email} />
					</div>
					<div className="text-muted-foreground mt-4 text-center text-sm">
						腾讯会议官网：
						<button
							type="button"
							className="underline text-blue-500 cursor-pointer"
							onClick={() =>
								window.open(
									"https://meeting.tencent.com/user-center/user-meeting-list/schedule",
									"_blank",
								)
							}
						>
							https://meeting.tencent.com/user-center/user-meeting-list/schedule
						</button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}