import type { Context, MiddlewareHandler, Next } from "hono";
import { 
  decryptRequestData, 
  verifySign, 
  encryptData, 
  isTimestampValid 
} from "@repo/utils/lib/crypto";
import { HTTPException } from "hono/http-exception";

// 定义响应的类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 扩展Hono的Variables类型
declare module 'hono' {
  interface ContextVariableMap {
    requestData: any;
    response: ApiResponse;
  }
}

/**
 * 股东名册专用加解密中间件
 * 处理请求解密和响应加密，验证请求签名和时间戳
 */
export const shareholderCryptoMiddleware = (): MiddlewareHandler => {
  return async (c: Context, next: Next) => {
    try {
      // 解析请求体
      const requestBody = await c.req.json();

      // 1. 验证请求格式
      if (!requestBody.content || !requestBody.sign) {
        throw new HTTPException(400, { message: "无效的请求格式" });
      }

      // 2. 验证签名
      const { content, sign } = requestBody;
   
      const isSignValid = verifySign(content, sign);

      if (!isSignValid) {
        throw new HTTPException(400, { message: "无效的请求签名" });
      }

      // 3. 解密内容并验证时间戳
      try {
        const { data, timestamp } = decryptRequestData<any>(content);
        
        // 4. 验证时间戳
        const timestampValid = isTimestampValid(timestamp);
        if (!timestampValid) {
          throw new HTTPException(400, { message: "请求已过期" });
        }

        // 5. 将解密后的业务数据注入到请求上下文
        c.set("requestData", data);

        // 继续处理请求
        await next();

        // 6. 处理响应加密
        const response = c.get("response");
        
        // 检查响应是否存在
        if (!response) {
          c.header("Content-Type", "application/json");
          return c.json({
            code: 500,
            message: "服务器内部错误：控制器未设置响应",
            data: null
          }, 500);
        }
        
        // 处理成功响应
        if (response.code === 200) {
          const encryptedData = encryptData(JSON.stringify(response.data));
          c.header("Content-Type", "application/json");
          return c.json({
            code: 200,
            message: response.message || "操作成功",
            data: encryptedData
          });
        } 
        // 处理其他响应状态
        c.header("Content-Type", "application/json");
        return c.json({
          code: response.code || 500,
          message: response.message || "服务器内部错误", 
          data: null
        }, response.code ? (response.code >= 100 && response.code < 600 ? response.code as 100|200|300|400|500 : 500) : 500);
      } catch (error) {
        throw new HTTPException(400, { message: "非法请求" });
      }
    } catch (error) {
      // 处理整体请求错误
      if (error instanceof HTTPException) {
        return c.json({
          code: error.status,
          message: error.message,
          data: null
        }, error.status);
      }
      
      // 处理其他意外错误
      return c.json({
        code: 500,
        message: "服务器内部错误",
        data: null
      }, 500);
    }
  };
}; 