# 股东数据模块E2E测试用例（优化版）

## 测试环境准备
- 测试环境URL: http://192.168.138.123:3000
- 登录凭据:
  - 邮箱输入框: #«R1ljrlpbl7»-form-item
  - 密码输入框: #«R25jrlpbl7»-form-item > input
  - 登录按钮: body > div.flex.min-h-screen.w-full.py-6 > div > div.container.flex.justify-center > main > div > form > button
  - 登录URL: http://192.168.138.123:3000/auth/login?redirectTo=%2Fapp
- 前置条件: 系统中已通过名册管理模块导入多期股东名册数据

## 模块概述
股东数据模块主要负责查看和分析已导入的股东名册数据，包括期数切换、股东信息查询、搜索功能、数据变化分析等功能。用户可以通过此模块查看不同报告期的股东详细信息。

## 删减说明
**删减理由**：
1. 原文档不完整，基于代码分析补充完整的测试场景
2. 删除了过于细粒度的字段验证测试
3. 合并了相似的搜索功能测试
4. 专注于核心的用户流程和业务场景
5. 删除了技术上难以实现的复杂测试场景

---

## 1. 核心功能测试

### 1.1 页面初始化和期数切换
**测试用例ID**: TC_SD_001
**测试描述**: 验证股东数据页面初始状态和期数切换功能
**测试步骤**:
1. 登录系统并导航到股东数据页面
2. 观察页面初始状态
3. 点击日期下拉框，观察可选期数列表
4. 选择不同的历史期数，观察数据变化

**预期结果**:
- 列表默认显示最近一期导入的股东名册数据
- 日期选择框默认显示最新一期
- 表格包含字段：证件号码、账户名称、股东标签、序号、持股比例、持股数量、变化等
- 默认每页显示20条数据，包含搜索框、名册导入按钮等功能组件
- 下拉框显示所有已导入的期数，按日期降序排列
- 选择不同期数后，列表数据自动更新，搜索条件被清除，分页重置为第一页
- 如果当前期数无数据，显示"无数据"

### 1.2 股东数据列表展示和分页
**测试用例ID**: TC_SD_002
**测试描述**: 验证股东数据列表的正确展示和分页功能
**测试步骤**:
1. 选择一个有数据的期数
2. 观察列表展示的字段和内容
3. 检查数据的完整性和准确性
4. 测试分页功能（如果数据超过20条）

**预期结果**:
- 表格从"证件号码"开始，按顺序展示所有必要字段
- 包含：证件号码、账户名称、股东标签、序号、持股比例、持股数量等
- 变化列显示在最后一列
- 数据显示完整无缺失，数据格式正确（数字、百分比、日期等）
- 分页功能正常，可以切换页面查看更多数据

## 2. 搜索和筛选功能测试

### 2.1 多字段搜索功能
**测试用例ID**: TC_SD_003
**测试描述**: 验证通过不同字段搜索股东的功能
**测试步骤**:
1. 在搜索框中输入股东的证件号码，按回车搜索
2. 清空搜索框，输入股东名称，确认搜索
3. 清空搜索框，输入一码通账户，确认搜索
4. 输入不存在的信息，测试无结果情况

**预期结果**:
- 证件号码搜索：完全匹配，显示对应股东信息
- 股东名称搜索：支持模糊匹配，显示包含关键词的股东
- 一码通搜索：精确匹配，显示对应账户的股东
- 搜索范围限制在当前选定的报告期数据
- 无结果时列表显示为空，页面中间显示"无数据"
- 搜索结果准确，支持清空搜索条件恢复全部数据

## 3. 数据变化分析功能测试

### 3.1 股东变化标识和分析
**测试用例ID**: TC_SD_004
**测试描述**: 验证股东数据变化标识和分析功能
**测试步骤**:
1. 选择有历史数据对比的期数
2. 观察变化列的显示内容
3. 查看新增、减少、变动的股东标识
4. 验证变化数据的准确性

**预期结果**:
- 变化列正确显示股东的变动情况（新增、减少、持股变动等）
- 新增股东有明确的标识
- 持股数量变化有准确的数值显示
- 变化标识颜色区分明显（如新增为绿色，减少为红色）
- 变化数据计算准确，与历史期数对比正确

## 4. 边界条件和异常处理测试

### 4.1 边界条件和异常情况处理
**测试用例ID**: TC_SD_005
**测试描述**: 验证边界条件和异常情况的处理
**测试步骤**:
1. 测试空数据状态（选择无数据的期数）
2. 测试大量数据的加载性能（如果有大量股东数据）
3. 模拟网络异常情况
4. 测试特殊字符的股东名称显示

**预期结果**:
- 空数据时显示"无数据"提示，页面布局正常
- 大量数据加载时有loading指示，分页正常工作
- 网络异常时显示错误提示，提供重试机制
- 特殊字符的股东名称正确显示，不影响页面布局

## 5. 用户体验和兼容性测试

### 5.1 用户体验和浏览器兼容性
**测试用例ID**: TC_SD_006
**测试描述**: 验证用户体验和基本兼容性
**测试步骤**:
1. 在Chrome、Firefox、Edge浏览器中测试核心功能
2. 测试响应式布局（使用开发者工具模拟移动端）
3. 验证操作反馈和加载状态
4. 检查数据脱敏显示

**预期结果**:
- 主流浏览器中功能正常，UI展示一致
- 移动端布局适配良好，关键功能可用
- 操作有及时反馈，加载状态有明确指示
- 敏感信息（如证件号码）按需脱敏显示

## 测试执行建议

### 测试优先级
1. **P0 (高优先级)**: TC_SD_001-TC_SD_004 (核心功能)
2. **P1 (中优先级)**: TC_SD_005 (边界条件和异常处理)
3. **P2 (低优先级)**: TC_SD_006 (用户体验和兼容性)

### 测试数据要求
- 至少准备3个不同期数的股东名册数据
- 包含50-100个股东记录
- 包含新增、减少、变动的股东数据
- 包含特殊字符的股东名称
- 包含不同类型的股东（个人、机构）

### 自动化建议
- 核心功能测试（TC_SD_001-TC_SD_004）建议实现自动化
- 使用Playwright进行E2E自动化，配置特定的登录凭据
- 重点关注期数切换和搜索功能的自动化测试

## 优化总结
**删减内容**：
- 原文档不完整，基于代码分析重新设计测试用例
- 删除了过于细粒度的字段验证测试
- 合并了相似的搜索功能测试为统一的多字段搜索测试
- 简化了边界条件测试，专注于实际可能遇到的场景

**保留内容**：
- 核心用户流程：页面初始化、期数切换、数据展示、搜索功能
- 关键业务场景：股东数据变化分析、多字段搜索
- 重要的边界条件：空数据处理、异常情况处理
- 基本的用户体验和兼容性验证

**优化效果**：
- 重新设计为6个完整的测试用例，覆盖核心功能
- 专注于端到端用户流程验证
- 提高了测试的可执行性和维护性
- 符合E2E测试最佳实践