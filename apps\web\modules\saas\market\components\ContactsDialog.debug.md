# ContactsDialog 调试修复说明

## 问题分析

根据你提供的截图，`list` 和 `create` 请求都失败了，`content` 内容为空。这表明：

1. **organizationId 可能为空**：导致请求参数不完整
2. **加密数据为空**：可能是因为参数缺失导致加密失败

## 修复措施

### 1. 添加调试信息

在以下文件中添加了详细的调试日志：

#### `ContactsDialog.tsx`
```typescript
// 调试信息
console.log("ContactsDialog Debug:", {
  activeOrganization,
  organizationSlug,
  organizationId,
  activeOrgId: activeOrganization?.id,
  activeOrgSlug: activeOrganization?.slug,
  isLoading,
  contactsData,
});
```

#### `useContacts.ts`
```typescript
queryFn: () => {
  console.log("useContacts fetchContactsList params:", params);
  return fetchContactsList(params);
},
```

#### `contacts_data.ts`
```typescript
console.log("fetchContactsList called with params:", params);
console.log("Encrypted data:", encrypted);
console.log("Generated sign:", sign);
console.log("Response status:", res.status);
console.log("Response ok:", res.ok);
console.log("Response result:", result);
```

### 2. 修复 organizationId 获取逻辑

**原问题**：只使用 `activeOrganization?.id`，可能为空

**修复方案**：使用 `organizationSlug` 作为备选
```typescript
// 使用 organizationSlug 作为 organizationId（因为后端可能期望的是 slug 而不是 id）
const organizationId = activeOrganization?.id || organizationSlug || "";
```

### 3. 统一 organizationId 使用

将所有使用 `activeOrganization?.id` 的地方改为使用统一的 `organizationId` 变量：

- `handleAddContact`
- `handleSave` 
- `handleDelete`
- `useContacts` 参数

## 调试步骤

1. **打开浏览器开发者工具**
2. **切换到 Console 标签**
3. **点击联系人标签**
4. **查看控制台输出**

### 预期的调试输出

```
ContactsDialog Debug: {
  activeOrganization: {...},
  organizationSlug: "your-org-slug",
  organizationId: "your-org-id-or-slug",
  activeOrgId: "...",
  activeOrgSlug: "...",
  isLoading: false,
  contactsData: {...}
}

useContacts fetchContactsList params: {
  organizationId: "your-org-id-or-slug",
  keyword: "",
  page: 1,
  limit: 100
}

fetchContactsList called with params: {
  organizationId: "your-org-id-or-slug",
  keyword: "",
  page: 1,
  limit: 100
}

Encrypted data: "encrypted-string"
Generated sign: "signature-string"
Response status: 200
Response ok: true
Response result: {...}
```

## 可能的问题和解决方案

### 1. organizationId 仍然为空
**检查**：控制台中 `organizationId` 的值
**解决**：确认 URL 路径中包含正确的 `organizationSlug`

### 2. 加密失败
**检查**：`Encrypted data` 是否为空
**解决**：检查加密函数和参数

### 3. 后端接口问题
**检查**：`Response status` 和 `Response result`
**解决**：确认后端接口是否正确实现

### 4. 字段名不匹配
**检查**：后端是否期望 `organizationId` 还是 `organizationSlug`
**解决**：根据后端要求调整字段名

## 下一步操作

1. 运行应用并查看控制台输出
2. 根据调试信息确定具体问题
3. 如果 `organizationId` 为空，检查路由参数
4. 如果加密数据为空，检查加密函数
5. 如果后端返回错误，检查接口实现

## 清理调试代码

测试完成后，记得移除所有 `console.log` 调试语句。
