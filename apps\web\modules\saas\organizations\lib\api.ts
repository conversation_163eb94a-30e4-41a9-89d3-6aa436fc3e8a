/**
 * 组织相关的API操作
 * 包含组织列表、活跃组织、完整组织信息的查询以及创建和更新组织的操作
 */

import type { OrganizationMetadata } from "@repo/auth";
import { authClient } from "@repo/auth/client";
import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery } from "@tanstack/react-query";

/**
 * 组织列表查询的key
 */
export const organizationListQueryKey = ["user", "organizations"] as const;

/**
 * 获取组织列表的Hook
 * @returns 组织列表查询结果
 */
export const useOrganizationListQuery = () => {
	return useQuery({
		queryKey: organizationListQueryKey,
		queryFn: async () => {
			const { data, error } = await authClient.organization.list();

			if (error) {
				throw new Error(
					error.message || "Failed to fetch organizations",
				);
			}

			return data;
		},
	});
};

/**
 * 获取活跃组织查询的key
 * @param slug - 组织的slug
 */
export const activeOrganizationQueryKey = (slug: string) =>
	["user", "activeOrganization", slug] as const;

/**
 * 获取活跃组织信息的Hook
 * @param slug - 组织的slug
 * @param options - 查询选项
 * @returns 活跃组织查询结果
 */
export const useActiveOrganizationQuery = (
	slug: string,
	options?: {
		enabled?: boolean;
	},
) => {
	return useQuery({
		queryKey: activeOrganizationQueryKey(slug),
		queryFn: async () => {
			const { data, error } =
				await authClient.organization.getFullOrganization({
					query: {
						organizationSlug: slug,
					},
				});

			if (error) {
				throw new Error(
					error.message || "Failed to fetch active organization",
				);
			}

			return data;
		},
		enabled: options?.enabled,
	});
};

/**
 * 获取完整组织信息查询的key
 * @param id - 组织ID
 */
export const fullOrganizationQueryKey = (id: string) =>
	["fullOrganization", id] as const;

/**
 * 获取完整组织信息的Hook
 * @param id - 组织ID
 * @returns 完整组织信息查询结果
 */
export const useFullOrganizationQuery = (id: string) => {
	return useQuery({
		queryKey: fullOrganizationQueryKey(id),
		queryFn: async () => {
			const { data, error } =
				await authClient.organization.getFullOrganization({
					query: {
						organizationId: id,
					},
				});

			if (error) {
				throw new Error(
					error.message || "Failed to fetch full organization",
				);
			}

			return data;
		},
	});
};

/**
 * 根据组织名称生成slug
 * @param name - 组织名称
 * @returns 生成的slug
 */
export const generateOrganizationSlug = async (name: string) => {
	const response = await apiClient.organizations["generate-slug"].$get({
		query: {
			name,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to generate organization slug");
	}

	const { slug } = await response.json();

	return slug;
};

/**
 * 创建组织的mutation key
 */
export const createOrganizationMutationKey = ["create-organization"] as const;

/**
 * 创建组织的Hook
 * @returns 创建组织的mutation对象
 */
export const useCreateOrganizationMutation = () => {
	return useMutation({
		mutationKey: createOrganizationMutationKey,
		mutationFn: async ({
			name,
			metadata,
		}: { name: string; metadata?: OrganizationMetadata }) =>
			(
				await authClient.organization.create({
					name,
					slug: await generateOrganizationSlug(name),
					metadata,
				})
			).data,
	});
};

/**
 * 更新组织的mutation key
 */
export const updateOrganizationMutationKey = ["update-organization"] as const;

/**
 * 更新组织的Hook
 * @returns 更新组织的mutation对象
 */
export const useUpdateOrganizationMutation = () => {
	return useMutation({
		mutationKey: updateOrganizationMutationKey,
		mutationFn: async ({
			id,
			name,
			metadata,
			updateSlug,
		}: {
			id: string;
			name: string;
			metadata?: OrganizationMetadata;
			updateSlug?: boolean;
		}) =>
			(
				await authClient.organization.update({
					organizationId: id,
					data: {
						name,
						slug: updateSlug
							? await generateOrganizationSlug(name)
							: undefined,
						metadata,
					},
				})
			).data,
	});
};
