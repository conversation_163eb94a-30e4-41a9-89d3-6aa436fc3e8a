import axios from "axios";
import type { AxiosInstance, InternalAxiosRequestConfig } from "axios";
import { generateSignature } from "../../../lib/meeting-sign";

// 环境变量
export const appId = process.env.TENCENT_APP_ID || "";
export const sdkId = process.env.TENCENT_SDK_ID || "";
export const secretId = process.env.TENCENT_SECRET_ID || "";
export const secretKey = process.env.TENCENT_SECRET_KEY || "";
export const operatorId = process.env.TENCENT_OPERATOR_ID || "";
export const tencent_userid = "wemeeting6043617";

// API基础URL
export const API_BASE_URL = "https://api.meeting.qq.com";

/**
 * 创建带有腾讯会议认证的axios实例
 */
export function createMeetingApiClient(): AxiosInstance {
	const apiClient = axios.create({
		baseURL: API_BASE_URL,
		headers: {
			AppId: appId,
			"X-TC-Registered": "1",
			SdkId: sdkId,
			"Content-Type": "application/json",
		},
	});

	// 请求拦截器 - 添加签名和认证信息
	apiClient.interceptors.request.use(
		(config: InternalAxiosRequestConfig) => {
			// 处理请求参数
			const method = config.method?.toUpperCase() || "GET";

			// 构建requestUri
			let requestUri = config.url || "";

			// 确保requestUri不包含baseURL
			if (requestUri.startsWith(API_BASE_URL)) {
				requestUri = requestUri.substring(API_BASE_URL.length);
			}

			// 对于GET请求，将params添加到requestUri (与原有逻辑保持一致)
			// 这里不改变config.params，因为axios还需要这些参数
			let paramsString = "";
			if ((method === "GET" || method === "DELETE") && config.params) {
				const queryParams = new URLSearchParams();
				for (const key in config.params) {
					queryParams.append(key, String(config.params[key]));
				}
				paramsString = queryParams.toString();

				// 添加到requestUri用于签名计算 (不改变实际请求的URL)
				if (paramsString) {
					requestUri = requestUri.includes("?")
						? `${requestUri}&${paramsString}`
						: `${requestUri}?${paramsString}`;
				}
			}

			// 请求体 - 与原有逻辑保持一致
			const requestBody =
				method === "GET" ? "" : JSON.stringify(config.data || {});

			


			// 生成签名
			const timestamp = Math.floor(Date.now() / 1000);
			const nonce = Math.floor(Math.random() * 100000);

			const signature = generateSignature(
				method,
				requestUri,
				requestBody,
				timestamp,
				nonce,
				secretId,
				secretKey,
			);
			// 添加认证头
			if (config.headers) {
				config.headers["X-TC-Key"] = secretId;
				config.headers["X-TC-Timestamp"] = timestamp;
				config.headers["X-TC-Nonce"] = nonce;
				config.headers["X-TC-Signature"] = signature;
			}

			
			return config;
		},
		(error) => {
			return Promise.reject(error);
		},
	);

	// 响应拦截器 - 处理错误响应
	apiClient.interceptors.response.use(
		(response) => {
			return response;
		},
		(error) => {
			console.error(
				"腾讯会议API请求失败:",
				error.response?.data || error.message,
			);
			return Promise.reject(error);
		},
	);

	return apiClient;
}

// 导出默认的API客户端实例
export const meetingApiClient = createMeetingApiClient();

export function generateHeaders() {};

