# 数据库使用问题记录

本文档记录了项目中使用 Prisma 数据库过程中遇到的常见问题和解决方案，可作为团队成员的参考指南。

## 1. 迁移问题

### 1.1 迁移文件丢失问题

**问题描述**：
运行 `pnpm --filter database migrate` 命令时，出现以下错误：
```
Error: P3015
Could not find the migration file at prisma\migrations\0_init\migration.sql. Please delete the directory or restore the migration file.
```

**原因分析**：
此错误表明 Prisma 期望在 `prisma\migrations\0_init\` 目录下找到一个 `migration.sql` 文件，但该文件不存在。这通常发生在迁移历史不完整或损坏的情况下。

**解决方案**：

1. **使用 db push 替代 migrate**:
   ```bash
   pnpm --filter database push
   ```
   ```bash
   pnpm --filter database generate
   ```
   
   这个push命令会直接将 schema 变更推送到数据库，而不依赖于迁移历史。适合在开发环境中使用。
   这个generate命令会直接将 schema 重新生成 prisma 客户端。

2. **创建更新 schema 的综合命令**:

   在 `package.json` 中添加一个更新 schema 的命令：
   ```json
   "scripts": {
     "update-schema": "pnpm push && pnpm generate"
   }
   ```
   
   使用方式：
   ```bash
   pnpm --filter database update-schema
   ```
   
   此命令会先推送 schema 变更到数据库，然后重新生成 Prisma 客户端。

3. **如果一定要修复迁移历史**:
   
   a. 删除问题目录:
   ```bash
   Remove-Item -Path packages\database\prisma\migrations\0_init -Recurse -Force
   ```
   
   b. 创建初始迁移:
   ```bash
   cd packages/database
   npx dotenv -c -e ../../.env.local -- prisma migrate dev --name init_from_current_state
   ```
   
   但这种方法可能会要求重置数据库，不推荐在生产环境使用。

### 1.2 环境变量加载问题

**问题描述**：
Prisma 命令无法正确加载 `.env.local` 文件中的环境变量。

**解决方案**：
修改 `package.json` 中的命令，明确指定环境变量文件的位置：

```json
"scripts": {
  "generate": "prisma generate --no-hints",
  "push": "dotenv -c -e ../../.env.local -- prisma db push --skip-generate",
  "migrate": "dotenv -c -e ../../.env.local -- prisma migrate dev",
  "studio": "dotenv -c -e ../../.env.local -- prisma studio"
}
```

## 2. 修改数据库结构问题

### 2.1 添加必填字段到已有数据的表

**问题描述**：
当向已包含数据的表添加新的必填字段时，出现以下错误：
```
Error: 
⚠️ We found changes that cannot be executed:
  • Added the required column `test1` to the `company_info` table without a default value. 
    There are 2 rows in this table, it is not possible to execute this step.
```

**原因分析**：
当表中已经有数据时，添加一个没有默认值的必填字段会导致错误，因为现有记录无法满足新字段的非空约束。

**解决方案**：

1. **为新字段添加默认值**:
   在 schema.prisma 文件中，为新字段添加一个默认值：
   
   ```prisma
   test1 String @default("") // 添加默认值
   ```

2. **将字段设为可选**:
   如果该字段在某些情况下可以为空，可以将其设为可选：
   
   ```prisma
   test1 String? // 将字段设为可选
   ```

3. **分两步修改**（适用于需要必填且没有合适默认值的情况）:
   
   a. 先添加为可选字段:
   ```prisma
   test1 String? // 临时设为可选
   ```
   
   b. 更新数据库并填充该字段的值
   c. 再将字段改为必填:
   ```prisma
   test1 String // 改回必填
   ```

## 3. 最佳实践

### 3.1 日常开发建议

1. **优先使用 `push` 命令**:
   在开发环境中，使用 `pnpm --filter database push` 命令更加方便，可以避免迁移历史的问题。

2. **定期备份数据**:
   在进行重要的 schema 变更之前，确保备份数据库。

3. **添加新字段的策略**:
   - 优先考虑添加可选字段（使用 `?` 标记）
   - 必填字段尽量提供合理的默认值
   - 对于枚举类型的字段，确保默认值是有效的枚举值

4. **使用综合命令**:
   使用 `pnpm --filter database update-schema` 命令可以一步完成 schema 推送和客户端生成。

### 3.2 环境差异

1. **开发环境**:
   - 使用 `push` 命令快速迭代
   - 可以根据需要重置数据库

2. **测试/生产环境**:
   - 使用正式的迁移流程
   - 避免数据丢失风险
   - 为必填字段提供合理默认值

## 4. 常用命令参考

```bash
# 查看数据库内容
pnpm --filter database studio

# 推送 schema 变更到数据库
pnpm --filter database push

# 更新 schema 并重新生成客户端
pnpm --filter database update-schema

# 创建新的迁移
pnpm --filter database migrate

# 查看迁移状态
cd packages/database
npx dotenv -c -e ../../.env.local -- prisma migrate status
```

## 5. 疑难解答

如果遇到其他数据库问题，可以参考以下资源：

1. Prisma 官方文档: https://www.prisma.io/docs/
2. Prisma 迁移故障排除: https://www.prisma.io/docs/guides/database/developing-with-prisma-migrate/troubleshooting-development
3. Supastarter 文档: https://supastarter.dev/docs/nextjs/database/update-schema 