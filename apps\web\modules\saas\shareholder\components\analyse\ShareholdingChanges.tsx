/**
 * 股东名册分析 - 持股变动分析组件
 * @file ShareholdingChanges.tsx
 * @description 用于展示公司股东持股变动的分析
 * @created 2025-06-12 17:26:08
 * @updated 2025-06-30 12:03:04
 * @modified 添加数据说明弹窗功能
 * @modified 2025-06-30 12:03:04 - 添加错误状态处理，当API加载失败时显示错误描述信息，类似StructureTrend.tsx的实现
 * <AUTHOR>
 */
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
	TooltipPortal,
} from "@ui/components/tooltip";
import { BarChart3Icon } from "lucide-react";
import { AntShareholderTable, type ShareholderColumn } from "@saas/shareholder/components/ant-shareholder-table";
import { useMemo, useState, useRef, useEffect, useCallback } from "react";
import { DataExplanationDialog} from "@saas/shareholder/components/dialogs"
import {
	useIncreaseIndividualShareholders,
	useDecreaseIndividualShareholders,
	useNewIndividualShareholders,
	useExitIndividualShareholders,
	type ShareholderItem,
	type PaginationInfo,
} from "@saas/shareholder/hooks/useShareholderAnalyseChanges";
import {
	useIncreaseInstitutionShareholders,
	useDecreaseInstitutionShareholders,
	useNewInstitutionShareholders,
	useExitInstitutionShareholders
} from "@saas/shareholder/hooks/useShareholderAnalyseChangesExtended";
import { TablePagination } from "@saas/shareholder/components/TablePagination";



// 修改记录 2025-06-19 11:08:50 - 移除所有数据处理函数，逻辑已移至hooks中
// 修改记录 2025-06-19 11:18:10 - 重构getColumnKey、getColumnWidth、getColumnRender函数为统一的列配置格式

/**
 * 空数据状态组件
 * @description 在表格中没有数据时显示友好的提示信息，确保表格高度统一
 * <AUTHOR>
 * @created 2025-06-17 11:21:07
 * @modified 2025-06-17 12:20:47 - 导出EmptyState组件供其他组件复用
 */
export function EmptyState(): JSX.Element {
	return (
		<div className="flex flex-col items-center justify-center py-12 text-center px-4" style={{ height: "200px" }}>
			<p className="font-medium text-slate-700 mb-2">
				暂无数据
			</p>
		</div>
	);
}

/**
 * 股东表格组件
 * @param props 表格属性
 * @returns 股东表格UI组件
 * @modified 2025-06-17 10:31:52 - 简化组件，移除模拟数据加载功能，直接使用传入的真实API数据
 * @modified 2025-06-19 11:47:33 - 添加表头排序功能，支持一码通、持股、发生期数、股东名称、排名字段排序
 * @modified 2025-06-30 12:03:04 - 添加错误状态处理，显示API加载失败的描述信息 - hayden
 * <AUTHOR>
 */
function ShareholderTable({
	headers,
	data,
	type = "个人",
	loading = false,
	pagination,
	page = 1,
	onPageChange,
	onSort,
	error
}: {
	headers: string[];
	data: ShareholderItem[];
	type?: "个人" | "机构";
	loading?: boolean;
	pagination?: PaginationInfo;
	page?: number;
	onPageChange?: React.Dispatch<React.SetStateAction<number>>;
	onSort?: (sortKey: string, sortOrder: 'asc' | 'desc') => void;
	error?: Error | null;
}): JSX.Element {

	// 滚动相关状态 - 添加于2025年06月19日10:10:58
	const [isScrollLoading, setIsScrollLoading] = useState(false);
	const [accumulatedData, setAccumulatedData] = useState<ShareholderItem[]>([]);
	const scrollRef = useRef({
		left: 0,
		top: 0,
	});
	const tableScrollRef = useRef<HTMLDivElement>(null); // 表格滚动容器引用

	// 当新数据到达时，根据页码决定是替换还是累积 - 修改于2025年06月19日10:39:16
	useEffect(() => {
		if (page === 1) {
			// 第一页数据，直接替换
			setAccumulatedData(data);
		} else if (data.length > 0) {
			// 后续页数据，进行累积（去重）
			setAccumulatedData((prevData) => {
				// 使用ID进行去重，因为现在ID是唯一的
				const existingIds = new Set(prevData.map((item) => item.id));
				const newItems = data.filter((item) => !existingIds.has(item.id));
				return [...prevData, ...newItems];
			});
		}
	}, [data, page]);

	// 处理表格滚动事件 - 修改于2025年06月19日10:35:43
	const handleTableScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
		const { scrollTop, clientHeight, scrollHeight, scrollLeft } =
			event.target as HTMLDivElement;

		// 记录滚动容器引用
		if (!tableScrollRef.current && event.target) {
			tableScrollRef.current = event.target as HTMLDivElement;
		}

		// 计算是否还有更多数据可以加载
		const totalPages = pagination?.totalPages || 1;
		const hasMoreData = page < totalPages;

		// 垂直滚动 & 到底了 & 有更多数据 & 不在加载中
		if (
			Math.abs(scrollTop - scrollRef.current.top) > 0 &&
			scrollTop + clientHeight >= scrollHeight - 50 && // 提前50px触发加载
			hasMoreData &&
			!isScrollLoading &&
			!loading &&
			onPageChange
		) {

			// 设置滚动加载状态
			setIsScrollLoading(true);

			// 使用函数式更新确保基于当前页码递增
			onPageChange((prevPage: number) => {
				const nextPage = prevPage + 1;
				return nextPage;
			});

			// 延迟重置加载状态，避免频繁触发
			setTimeout(() => {
				setIsScrollLoading(false);
			}, 1000); // 增加延迟时间，确保API调用完成
		}

		// 记录当前滚动信息
		scrollRef.current = {
			left: scrollLeft,
			top: scrollTop,
		};
	}, [pagination?.totalPages, page, isScrollLoading, loading, onPageChange]);

	const scrollConfig = useMemo(() => {
		// 智能垂直滚动：只有当数据超过4行时才启用垂直滚动，避免少量数据时出现滚动条
		const shouldEnableYScroll = accumulatedData.length > 4;

		return {
			x: shouldEnableYScroll ? "max-content" : undefined, // 当数据超过4条时启用水平滚动
			y: shouldEnableYScroll ? 250 : undefined, // 固定高度250px，适配300px容器
			scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
		};
	}, [accumulatedData.length]);

	/**
	 * 根据表头生成列配置
	 * @param headers 表头数组
	 * @param total 数据总数，用于控制排序功能开启条件
	 * @returns ShareholderColumn数组
	 * <AUTHOR>
	 * @created 2025-06-17 11:05:07
	 * @modified 2025-06-17 11:36:18 - 添加className属性实现表头表体居中对齐
	 */
	const generateColumns = (headers: string[], total: number | undefined): ShareholderColumn[] => {
		// 定义列配置映射表，确保每个表头对应正确的API数据字段
		const columnMappings: Record<string, ShareholderColumn> = {
			排名: {
				key: "rank",
				title: "排名",
				width: 80,
				sortable: total !== undefined && total >= 3,
				className: "text-center",
				render: (value: any) => (
					<div className="text-center">{value}</div>
				),
			},
			股东名称: {
				key: "name",
				title: "股东名称",
				width: 160,
				sortable: total !== undefined && total >= 3,
				className: "text-center font-medium",
				render: (value: any) => {
					if (!value) {
						return <div className="text-center">-</div>;
					}

					// 统一使用div容器确保居中对齐，如果文本长度超过10个字符，使用tooltip
					if (String(value).length > 10) {
						return (
							<div className="text-center">
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<span className="cursor-default truncate inline-block max-w-[120px]">
												{String(value).slice(0, 10)}...
											</span>
										</TooltipTrigger>
										<TooltipPortal>
											<TooltipContent
												side="top"
												align="center"
												className="max-w-[300px]"
											>
												{value}
											</TooltipContent>
										</TooltipPortal>
									</Tooltip>
								</TooltipProvider>
							</div>
						);
					}

					return <div className="text-center">{value}</div>;
				},
			},
			一码通: {
				key: "identifier",
				title: "一码通",
				width: 150,
				sortable: total !== undefined && total >= 3,
				className: "text-center",
			},
			// 新进股东持股数量 - API字段：number_of_shares
			当期持股: {
				key: "shares",
				title: "当期持股",
				width: 120,
				sortable: total !== undefined && total >= 3,
				className: "text-center font-mono",
				render: (value: any) => value, // 已在hooks中格式化
			},
			// 退出股东退出前持股数 - API字段：prev_numberofshares
			退出前持股数: {
				key: "shares",
				title: "退出前持股数",
				width: 120,
				sortable: total !== undefined && total >= 3,
				className: "text-center font-mono",
				render: (value: any) => value, // 已在hooks中格式化
			},
			// 增持股东增持股数 - API字段：increased_shares
			增持股数: {
				key: "shares",
				title: "增持股数",
				width: 120,
				sortable: total !== undefined && total >= 3,
				className: "text-center font-mono",
				render: (value: any) => value, // 已在hooks中格式化
			},
			// 减持股东减持股数 - API字段：decreased_shares
			减持股数: {
				key: "shares",
				title: "减持股数",
				width: 120,
				sortable: total !== undefined && total >= 3,
				className: "text-center font-mono",
				render: (value: any) => value, // 已在hooks中格式化
			},
			// 增持/减持股东当前持股数 - API字段：current_numberofshares
			持股: {
				key: "currentShares",
				title: "持股",
				width: 120,
				sortable: total !== undefined && total >= 3,
				className: "text-center font-mono",
				render: (value: any) => value, // 已在hooks中格式化
			},
			// 新进股东持股比例 - API字段：shareholder_ratio
			"当期持股比例(%)": {
				key: "percentage",
				title: "当期持股比例(%)",
				width: 150,
				sortable: false,
				className: "text-center font-mono",
				render: (value: any) => value, // 已在hooks中格式化
			},
			// 退出股东退出前持股比例 - API字段：prev_shareholdingratio
			"退出前持股比例(%)": {
				key: "percentage",
				title: "退出前持股比例(%)",
				width: 150,
				sortable: false,
				className: "text-center font-mono",
				render: (value: any) => value, // 已在hooks中格式化
			},
			// 增持股东增持比例 - API字段：increased_ratio_percent
			"增持比例(%)": {
				key: "percentage",
				title: "增持比例(%)",
				width: 150,
				sortable: false,
				className: "text-center font-mono",
				render: (value: any) => value, // 已在hooks中格式化
			},
			// 减持股东减持比例 - API字段：decreased_ratio_percent
			"减持比例(%)": {
				key: "percentage",
				title: "减持比例(%)",
				width: 150,
				sortable: false,
				className: "text-center font-mono",
				render: (value: any) => value, // 已在hooks中格式化
			},
			// 新进股东新进日期 - API字段：register_date
			发生期数: {
				key: "date",
				title: "发生期数",
				width: 130,
				sortable: total !== undefined && total >= 3,
				className: "text-center",
			},
		};

		// 根据传入的headers筛选对应的列配置
		return headers.map(header => columnMappings[header]).filter(Boolean);
	};

	// 生成列配置
	const columns = generateColumns(headers, pagination?.total);

	// 错误状态处理 - 添加于2025年06月30日12:03:04
	if (error) {
		return (
			<div className="w-full space-y-2">
				<div className="flex items-center justify-between">
					<h3 className="text-sm font-medium">{type}</h3>
				</div>
				<div className="rounded-md border">
					<div className="flex items-center justify-center h-64">
						<div className="text-muted-foreground">
							加载失败: {error.message}
						</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="w-full space-y-2">
			<div className="flex items-center justify-between">
				<h3 className="text-sm font-medium">{type}</h3>
			</div>
			<div className="rounded-md border">
				<AntShareholderTable
					data={accumulatedData}
					columns={columns}
					emptyContent={<EmptyState />}
					loading={loading}
					className="h-[300px]"
					onScroll={handleTableScroll}
					scroll={scrollConfig}
					onSort={onSort} // 添加于2025年06月19日11:47:33 - 传递排序处理函数
					// 修改记录 2025-06-19 11:27:42 - 移除footer属性，改为使用外部TablePagination组件
					// footer={footerContent}
				/>
			</div>
			{/* 添加TablePagination组件和没有更多数据提示 - 添加于2025年06月19日11:27:42 */}
			{pagination && (
					<TablePagination pagination={pagination} page={page} />
				)}
		</div>
	);
}

/**
 * 持股变动分析组件属性接口
 * @interface ShareholdingChangesProps
 */
interface ShareholdingChangesProps {
	/** 组织ID */
	organizationId: string;
}

/**
 * 持股变动分析组件
 * @param props 组件属性
 * @returns 持股变动分析UI组件
 * @description 使用真实API数据展示股东变动分析，包含增持、减持、新进、退出股东信息
 * <AUTHOR>
 * @modified 2025-06-16 18:35:23 - 集成真实API数据，接收organizationId参数
 */
export function ShareholdingChanges({ organizationId }: ShareholdingChangesProps): JSX.Element {
	// 分页状态管理 - 修改于2025年06月19日10:35:43
	const [newIndividualPage, setNewIndividualPage] = useState(1);
	const [newInstitutionPage, setNewInstitutionPage] = useState(1);
	const [exitIndividualPage, setExitIndividualPage] = useState(1);
	const [exitInstitutionPage, setExitInstitutionPage] = useState(1);
	const [increaseIndividualPage, setIncreaseIndividualPage] = useState(1);
	const [increaseInstitutionPage, setIncreaseInstitutionPage] = useState(1);
	const [decreaseIndividualPage, setDecreaseIndividualPage] = useState(1);
	const [decreaseInstitutionPage, setDecreaseInstitutionPage] = useState(1);

	// 排序状态管理 - 添加于2025年06月19日11:47:33
	const [newIndividualSort, setNewIndividualSort] = useState<{field?: string; order?: 'asc' | 'desc'}>({});
	const [newInstitutionSort, setNewInstitutionSort] = useState<{field?: string; order?: 'asc' | 'desc'}>({});
	const [exitIndividualSort, setExitIndividualSort] = useState<{field?: string; order?: 'asc' | 'desc'}>({});
	const [exitInstitutionSort, setExitInstitutionSort] = useState<{field?: string; order?: 'asc' | 'desc'}>({});
	const [increaseIndividualSort, setIncreaseIndividualSort] = useState<{field?: string; order?: 'asc' | 'desc'}>({});
	const [increaseInstitutionSort, setIncreaseInstitutionSort] = useState<{field?: string; order?: 'asc' | 'desc'}>({});
	const [decreaseIndividualSort, setDecreaseIndividualSort] = useState<{field?: string; order?: 'asc' | 'desc'}>({});
	const [decreaseInstitutionSort, setDecreaseInstitutionSort] = useState<{field?: string; order?: 'asc' | 'desc'}>({});

	// 当organizationId变化时重置所有分页状态和排序状态 - 修改于2025年06月19日11:47:33
	useEffect(() => {
		setNewIndividualPage(1);
		setNewInstitutionPage(1);
		setExitIndividualPage(1);
		setExitInstitutionPage(1);
		setIncreaseIndividualPage(1);
		setIncreaseInstitutionPage(1);
		setDecreaseIndividualPage(1);
		setDecreaseInstitutionPage(1);

		// 重置排序状态
		setNewIndividualSort({});
		setNewInstitutionSort({});
		setExitIndividualSort({});
		setExitInstitutionSort({});
		setIncreaseIndividualSort({});
		setIncreaseInstitutionSort({});
		setDecreaseIndividualSort({});
		setDecreaseInstitutionSort({});
	}, [organizationId]);

	/**
	 * 将前端字段名映射为API排序字段名
	 * @param fieldKey 前端字段名
	 * @param tableType 表格类型，用于确定正确的API字段映射
	 * @returns API排序字段名
	 * <AUTHOR>
	 * @created 2025-06-19 11:47:33
	 * @modified 2025-06-19 12:30:14 - 重新规划字段映射，使用真实API字段名，简化映射逻辑
	 * @modified 2025-06-19 12:57:35 - 修复排序字段错误，为不同表格类型提供正确的字段映射
	 */
	const mapSortFieldToApi = (fieldKey: string, tableType: string): string => {
		// 基础字段映射 - 所有表格通用
		const commonMapping: Record<string, string> = {
			"name": "name",                           // 股东名称
			"identifier": "unified_account_number",   // 一码通
			"rank": "rank",                          // 排名
		};

		// 根据表格类型确定特殊字段映射
		const typeSpecificMapping: Record<string, Record<string, string>> = {
			// 新进股东字段映射
			newIndividual: {
				shares: "number_of_shares", // 持股数量 -> number_of_shares
				percentage: "shareholding_ratio", // 持股比例 -> shareholder_ratio
				date: "register_date", // 新进日期 -> register_date
			},
			newInstitution: {
				shares: "number_of_shares", // 持股数量 -> number_of_shares
				percentage: "shareholding_ratio", // 持股比例 -> shareholder_ratio
				date: "register_date", // 新进日期 -> register_date
			},
			// 退出股东字段映射
			exitIndividual: {
				shares: "prev_numberofshares", // 退出前持股数 -> prev_numberofshares
				percentage: "prev_shareholdingratio", // 退出前持股比例 -> prev_shareholdingratio
				date: "exit_date", // 退出日期 -> exit_date
			},
			exitInstitution: {
				shares: "prev_numberofshares", // 退出前持股数 -> prev_numberofshares
				percentage: "prev_shareholdingratio", // 退出前持股比例 -> prev_shareholdingratio
				date: "exit_date", // 退出日期 -> exit_date
			},
			// 增持股东字段映射
			increaseIndividual: {
				shares: "increased_shares", // 增持股数 -> increased_shares
				percentage: "current_shareholdingratio", // 增持比例 -> increased_ratio_percent
				currentShares: "current_numberofshares", // 当前持股数 -> current_numberofshares
				date: "increased_date", // 增持日期 -> increased_date
			},
			increaseInstitution: {
				shares: "increased_shares", // 增持股数 -> increased_shares
				percentage: "current_shareholdingratio", // 增持比例 -> increased_ratio_percent
				currentShares: "current_numberofshares", // 当前持股数 -> current_numberofshares
				date: "increased_date", // 增持日期 -> increased_date
			},
			// 减持股东字段映射
			decreaseIndividual: {
				shares: "decreased_shares", // 减持股数 -> decreased_shares
				percentage: "decreased_ratio_percent", // 减持比例 -> decreased_ratio_percent
				currentShares: "current_numberofshares", // 当前持股数 -> current_numberofshares
				date: "decreased_date", // 减持日期 -> decreased_date
			},
			decreaseInstitution: {
				shares: "decreased_shares", // 减持股数 -> decreased_shares
				percentage: "decreased_ratio_percent", // 减持比例 -> decreased_ratio_percent
				currentShares: "current_numberofshares", // 当前持股数 -> current_numberofshares
				date: "decreased_date", // 减持日期 -> decreased_date
			},
		};

		// 首先检查通用映射
		if (commonMapping[fieldKey]) {
			return commonMapping[fieldKey];
		}

		// 然后检查特定类型映射
		const specificMapping = typeSpecificMapping[tableType];
		if (specificMapping?.[fieldKey]) {
			return specificMapping[fieldKey];
		}

		// 如果没有找到映射，返回原字段名
		return fieldKey;
	};

	// 获取增持自然人股东数据 - 修改于2025年06月19日12:57:35，修复排序字段映射错误
	// 修改记录 2025-06-30 12:03:04 - 添加错误状态处理，显示API加载失败的描述信息 - hayden
	const {
		processedData: increaseIndividualData,
		pagination: increaseIndividualPagination,
		isLoading: increaseIndividualLoading,
		error: increaseIndividualError
	} = useIncreaseIndividualShareholders(organizationId, {
		page: increaseIndividualPage,
		pageSize: 10,
		order: increaseIndividualSort.order ? (increaseIndividualSort.order === 'asc' ? 'Asc' : 'Desc') : undefined,
		orderBase: increaseIndividualSort.field ? mapSortFieldToApi(increaseIndividualSort.field, 'increaseIndividual') : undefined
	});
	// 获取增持机构股东数据 - 修改于2025年06月19日12:57:35，修复排序字段映射错误
	// 修改记录 2025-06-30 12:03:04 - 添加错误状态处理，显示API加载失败的描述信息 - hayden
	const {
		processedData: increaseInstitutionData,
		pagination: increaseInstitutionPagination,
		isLoading: increaseInstitutionLoading,
		error: increaseInstitutionError
	} = useIncreaseInstitutionShareholders(organizationId, {
		page: increaseInstitutionPage,
		pageSize: 10,
		order: increaseInstitutionSort.order ? (increaseInstitutionSort.order === 'asc' ? 'Asc' : 'Desc') : undefined,
		orderBase: increaseInstitutionSort.field ? mapSortFieldToApi(increaseInstitutionSort.field, 'increaseInstitution') : undefined
	});

	// 获取减持自然人股东数据 - 修改于2025年06月19日12:57:35，修复排序字段映射错误
	// 修改记录 2025-06-30 12:03:04 - 添加错误状态处理，显示API加载失败的描述信息 - hayden
	const {
		processedData: decreaseIndividualData,
		pagination: decreaseIndividualPagination,
		isLoading: decreaseIndividualLoading,
		error: decreaseIndividualError
	} = useDecreaseIndividualShareholders(organizationId, {
		page: decreaseIndividualPage,
		pageSize: 10,
		order: decreaseIndividualSort.order ? (decreaseIndividualSort.order === 'asc' ? 'Asc' : 'Desc') : undefined,
		orderBase: decreaseIndividualSort.field ? mapSortFieldToApi(decreaseIndividualSort.field, 'decreaseIndividual') : undefined
	});

	// 获取减持机构股东数据 - 修改于2025年06月19日12:57:35，修复排序字段映射错误
	// 修改记录 2025-06-30 12:03:04 - 添加错误状态处理，显示API加载失败的描述信息 - hayden
	const {
		processedData: decreaseInstitutionData,
		pagination: decreaseInstitutionPagination,
		isLoading: decreaseInstitutionLoading,
		error: decreaseInstitutionError
	} = useDecreaseInstitutionShareholders(organizationId, {
		page: decreaseInstitutionPage,
		pageSize: 10,
		order: decreaseInstitutionSort.order ? (decreaseInstitutionSort.order === 'asc' ? 'Asc' : 'Desc') : undefined,
		orderBase: decreaseInstitutionSort.field ? mapSortFieldToApi(decreaseInstitutionSort.field, 'decreaseInstitution') : undefined
	});

	// 获取新进自然人股东数据 - 修改于2025年06月19日12:57:35，修复排序字段映射错误
	// 修改记录 2025-06-30 12:03:04 - 添加错误状态处理，显示API加载失败的描述信息 - hayden
	const {
		processedData: newIndividualData,
		pagination: newIndividualPagination,
		isLoading: newIndividualLoading,
		error: newIndividualError
	} = useNewIndividualShareholders(organizationId, {
		page: newIndividualPage,
		pageSize: 10,
		order: newIndividualSort.order ? (newIndividualSort.order === 'asc' ? 'Asc' : 'Desc') : undefined,
		orderBase: newIndividualSort.field ? mapSortFieldToApi(newIndividualSort.field, 'newIndividual') : undefined
	});

	// 获取新进机构股东数据 - 修改于2025年06月19日12:57:35，修复排序字段映射错误
	// 修改记录 2025-06-30 12:03:04 - 添加错误状态处理，显示API加载失败的描述信息 - hayden
	const {
		processedData: newInstitutionData,
		pagination: newInstitutionPagination,
		isLoading: newInstitutionLoading,
		error: newInstitutionError
	} = useNewInstitutionShareholders(organizationId, {
		page: newInstitutionPage,
		pageSize: 10,
		order: newInstitutionSort.order ? (newInstitutionSort.order === 'asc' ? 'Asc' : 'Desc') : undefined,
		orderBase: newInstitutionSort.field ? mapSortFieldToApi(newInstitutionSort.field, 'newInstitution') : undefined
	});

	// 获取退出自然人股东数据 - 修改于2025年06月19日12:57:35，修复排序字段映射错误
	// 修改记录 2025-06-30 12:03:04 - 添加错误状态处理，显示API加载失败的描述信息 - hayden
	const {
		processedData: exitIndividualData,
		pagination: exitIndividualPagination,
		isLoading: exitIndividualLoading,
		error: exitIndividualError
	} = useExitIndividualShareholders(organizationId, {
		page: exitIndividualPage,
		pageSize: 10,
		order: exitIndividualSort.order ? (exitIndividualSort.order === 'asc' ? 'Asc' : 'Desc') : undefined,
		orderBase: exitIndividualSort.field ? mapSortFieldToApi(exitIndividualSort.field, 'exitIndividual') : undefined
	});

	// 获取退出机构股东数据 - 修改于2025年06月19日12:57:35，修复排序字段映射错误
	// 修改记录 2025-06-30 12:03:04 - 添加错误状态处理，显示API加载失败的描述信息 - hayden
	const {
		processedData: exitInstitutionData,
		pagination: exitInstitutionPagination,
		isLoading: exitInstitutionLoading,
		error: exitInstitutionError
	} = useExitInstitutionShareholders(organizationId, {
		page: exitInstitutionPage,
		pageSize: 10,
		order: exitInstitutionSort.order ? (exitInstitutionSort.order === 'asc' ? 'Asc' : 'Desc') : undefined,
		orderBase: exitInstitutionSort.field ? mapSortFieldToApi(exitInstitutionSort.field, 'exitInstitution') : undefined
	});

	/**
	 * 排序处理函数
	 * @param tableType 表格类型
	 * @param sortKey 排序字段
	 * @param sortOrder 排序方向
	 * <AUTHOR>
	 * @created 2025-06-19 11:47:33
	 */
	const handleSort = (tableType: string, sortKey: string, sortOrder: 'asc' | 'desc') => {
		const sortState = { field: sortKey, order: sortOrder };

		switch (tableType) {
			case 'newIndividual':
				setNewIndividualSort(sortState);
				setNewIndividualPage(1); // 排序时重置到第一页
				break;
			case 'newInstitution':
				setNewInstitutionSort(sortState);
				setNewInstitutionPage(1);
				break;
			case 'exitIndividual':
				setExitIndividualSort(sortState);
				setExitIndividualPage(1);
				break;
			case 'exitInstitution':
				setExitInstitutionSort(sortState);
				setExitInstitutionPage(1);
				break;
			case 'increaseIndividual':
				setIncreaseIndividualSort(sortState);
				setIncreaseIndividualPage(1);
				break;
			case 'increaseInstitution':
				setIncreaseInstitutionSort(sortState);
				setIncreaseInstitutionPage(1);
				break;
			case 'decreaseIndividual':
				setDecreaseIndividualSort(sortState);
				setDecreaseIndividualPage(1);
				break;
			case 'decreaseInstitution':
				setDecreaseInstitutionSort(sortState);
				setDecreaseInstitutionPage(1);
				break;
		}
	};

	/**
	 * 定义表格标题，按照API文档字段意思渲染
	 * @description 根据真实API字段重新规划表头，确保与API响应数据字段完全对应
	 * <AUTHOR>
	 * @created 2025-06-19 12:30:14
	 * @modified 2025-06-19 12:30:14 - 重新规划表头字段名，使用真实API字段对应的中文表头
	 */
	const tableHeaders = {
		// 新进股东表头 - 对应API字段：name, unified_account_number, number_of_shares, shareholder_ratio, register_date
		newIndividual: [
			"股东名称", // name
			"一码通", // unified_account_number
			"当期持股", // number_of_shares
			"当期持股比例(%)", // shareholder_ratio
			"发生期数", // register_date
		],
		newInstitution: [
			"股东名称", // name
			"一码通", // unified_account_number
			"当期持股", // number_of_shares
			"当期持股比例(%)", // shareholder_ratio
			"发生期数", // register_date
		],
		// 退出股东表头 - 对应API字段：name, unified_account_number, prev_numberofshares, prev_shareholdingratio, exit_date
		exitIndividual: [
			"股东名称", // name
			"一码通", // unified_account_number
			"退出前持股数", // prev_numberofshares
			"退出前持股比例(%)", // prev_shareholdingratio
			"发生期数", // exit_date
		],
		exitInstitution: [
			"股东名称", // name
			"一码通", // unified_account_number
			"退出前持股数", // prev_numberofshares
			"退出前持股比例(%)", // prev_shareholdingratio
			"发生期数", // exit_date
		],
		// 增持股东表头 - 对应API字段：rank, name, unified_account_number, increased_shares, increased_ratio_percent, current_numberofshares, increased_date
		increaseIndividual: [
			"排名", // rank (增持个人股东无rank字段)
			"股东名称", // name (增持个人股东无rank字段)
			"一码通", // unified_account_number
			"增持股数", // increased_shares
			"增持比例(%)", // increased_ratio_percent
			"持股", // current_numberofshares
			"发生期数", // increased_date
		],
		increaseInstitution: [
			"排名", // rank (增持机构股东有rank字段)
			"股东名称", // name
			"一码通", // unified_account_number
			"增持股数", // increased_shares
			"增持比例(%)", // increased_ratio_percent
			"持股", // current_numberofshares
			"发生期数", // increased_date
		],
		// 减持股东表头 - 对应API字段：rank, name, unified_account_number, decreased_shares, decreased_ratio_percent, current_numberofshares, decreased_date
		decreaseIndividual: [
			"排名", // rank
			"股东名称", // name
			"一码通", // unified_account_number
			"减持股数", // decreased_shares
			"减持比例(%)", // decreased_ratio_percent
			"持股", // current_numberofshares
			"发生期数", // decreased_date
		],
		decreaseInstitution: [
			"排名", // rank
			"股东名称", // name
			"一码通", // unified_account_number
			"减持股数", // decreased_shares
			"减持比例(%)", // decreased_ratio_percent
			"持股", // current_numberofshares
			"发生期数", // decreased_date
		],
	};

	return (
		<div className="w-full">
			<div className="flex items-center gap-2">
				<BarChart3Icon className="size-5 text-primary" />
				<h2 className="text-lg font-semibold">持股变动分析</h2>
				<DataExplanationDialog />
			</div>
			<div className="space-y-6 mt-4">
				{/* 新进股东 */}
				<div className="space-y-4">
					<h2 className="text-lg font-semibold z-50">新进股东</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<ShareholderTable
							headers={tableHeaders.newIndividual}
							data={newIndividualData}
							type="个人"
							loading={newIndividualLoading}
							pagination={newIndividualPagination}
							page={newIndividualPage}
							onPageChange={setNewIndividualPage}
							onSort={(sortKey, sortOrder) => handleSort('newIndividual', sortKey, sortOrder)}
							error={newIndividualError}
						/>
						<ShareholderTable
							headers={tableHeaders.newInstitution}
							data={newInstitutionData}
							type="机构"
							loading={newInstitutionLoading}
							pagination={newInstitutionPagination}
							page={newInstitutionPage}
							onPageChange={setNewInstitutionPage}
							onSort={(sortKey, sortOrder) => handleSort('newInstitution', sortKey, sortOrder)}
							error={newInstitutionError}
						/>
					</div>
				</div>

				{/* 退出股东 */}
				<div className="space-y-4">
					<h2 className="text-lg font-semibold">退出股东</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<ShareholderTable
							headers={tableHeaders.exitIndividual}
							data={exitIndividualData}
							type="个人"
							loading={exitIndividualLoading}
							pagination={exitIndividualPagination}
							page={exitIndividualPage}
							onPageChange={setExitIndividualPage}
							onSort={(sortKey, sortOrder) => handleSort('exitIndividual', sortKey, sortOrder)}
							error={exitIndividualError}
						/>
						<ShareholderTable
							headers={tableHeaders.exitInstitution}
							data={exitInstitutionData}
							type="机构"
							loading={exitInstitutionLoading}
							pagination={exitInstitutionPagination}
							page={exitInstitutionPage}
							onPageChange={setExitInstitutionPage}
							onSort={(sortKey, sortOrder) => handleSort('exitInstitution', sortKey, sortOrder)}
							error={exitInstitutionError}
						/>
					</div>
				</div>

				{/* 增持股东 */}
				<div className="space-y-4">
					<h2 className="text-lg font-semibold">增持股东</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<ShareholderTable
							headers={tableHeaders.increaseIndividual}
							data={increaseIndividualData}
							type="个人"
							loading={increaseIndividualLoading}
							pagination={increaseIndividualPagination}
							page={increaseIndividualPage}
							onPageChange={setIncreaseIndividualPage}
							onSort={(sortKey, sortOrder) => handleSort('increaseIndividual', sortKey, sortOrder)}
							error={increaseIndividualError}
						/>
						<ShareholderTable
							headers={tableHeaders.increaseInstitution}
							data={increaseInstitutionData}
							type="机构"
							loading={increaseInstitutionLoading}
							pagination={increaseInstitutionPagination}
							page={increaseInstitutionPage}
							onPageChange={setIncreaseInstitutionPage}
							onSort={(sortKey, sortOrder) => handleSort('increaseInstitution', sortKey, sortOrder)}
							error={increaseInstitutionError}
						/>
					</div>
				</div>

				{/* 减持股东 */}
				<div className="space-y-4">
					<h2 className="text-lg font-semibold z-50">减持股东</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<ShareholderTable
							headers={tableHeaders.decreaseIndividual}
							data={decreaseIndividualData}
							type="个人"
							loading={decreaseIndividualLoading}
							pagination={decreaseIndividualPagination}
							page={decreaseIndividualPage}
							onPageChange={setDecreaseIndividualPage}
							onSort={(sortKey, sortOrder) => handleSort('decreaseIndividual', sortKey, sortOrder)}
							error={decreaseIndividualError}
						/>
						<ShareholderTable
							headers={tableHeaders.decreaseInstitution}
							data={decreaseInstitutionData}
							type="机构"
							loading={decreaseInstitutionLoading}
							pagination={decreaseInstitutionPagination}
							page={decreaseInstitutionPage}
							onPageChange={setDecreaseInstitutionPage}
							onSort={(sortKey, sortOrder) => handleSort('decreaseInstitution', sortKey, sortOrder)}
							error={decreaseInstitutionError}
						/>
					</div>
				</div>
			</div>
		</div>
	);
}