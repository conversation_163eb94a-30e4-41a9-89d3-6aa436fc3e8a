import type { z } from "zod";
import { 
  UploadRegistrySchema, 
  ListRegistrySchema, 
  ShareholdersSchema, 
  DeleteRegistrySchema,
  BatchDeleteRegistrySchema
} from "./lib/validators";

// 重新导出验证器
export { 
  UploadRegistrySchema, 
  ListRegistrySchema, 
  ShareholdersSchema, 
  DeleteRegistrySchema,
  BatchDeleteRegistrySchema
};

/**
 * 上传股东名册的响应类型
 */
export type UploadRegistryResponse = {
	id: string;
	fileName: string;
	recordCount: number;
	registerDate: string;
	uploadedAt: string;
};

/**
 * 公司详细信息类型
 */
export type CompanyDetailInfo = {
	companyName: string;
	totalShares: string;
	totalShareholders: number;
	totalInstitutions: number;
	largeSharesCount: string;
	institutionShares: string;
	largeShareholdersCount: number;
	// 控股股东信息
	controllingShareholderInfo?: {
		securitiesAccountName: string;  // 证券账户名称
		shareholdingRatio: string;      // 持股比例
		numberOfShares: string;         // 持股数量
	};
	// 前十股东持股信息
	topTenShareholdersInfo?: {
		totalRatio: string;            // 前十大股东持股比例总和
		totalShares: string;           // 前十大股东持股数量总和
	};
};

/**
 * 股东名册列表项的类型
 */
export type RegistryListItem = {
	id: string;
	fileName: string;
	recordCount: number;
	registerDate: string;
	companyCode: string;
	companyName: string;
	uploadedAt: string;
	companyDetail?: CompanyDetailInfo;
	userName: string;
};

/**
 * 分页信息类型
 */
export type Pagination = {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

/**
 * 股东名册列表响应类型
 */
export type ListRegistryResponse = {
  registries: RegistryListItem[];
  pagination: Pagination;
};

/**
 * 单个股东信息项类型
 */
export type ShareholderItem = {
  id: string;
  shareholderId: string;
  unifiedAccountNumber: string;
  securitiesAccountName: string;
  shareholderCategory: string;
  numberOfShares: string;
  lockedUpShares: string;
  shareholdingRatio: string;
  frozenShares: string;
  contactAddress?: string;
  contactNumber?: string;
  zipCode?: string;
  cashAccount?: string;
  sharesInCashAccount?: string;
  marginAccount?: string;
  sharesInMarginAccount?: string;
  relatedPartyIndicator?: string;
  clientCategory?: string;
  remarks?: string;
  rank?: number; // 股东排名，按照持股比例从大到小排序
  marginCollateralAccountNumber?: string; // 汇总账户号码
  marginCollateralAccountName?: string;   // 汇总账户名称
  natureOfShares?: string;                // 股份性质
  // 沪市名册特有字段
  shareTradingCategory?: string;          // 流通类型（t1名册）
  rightsCategory?: string;                // 权益类别（t1名册）
  investorName?: string;                  // 投资者名称（t1/t2/t3名册）
  shareholderName?: string;               // 股东名称（t1/t2/t3名册）
  certificateType?: string;               // 证件类型（t1/t2/t3名册）
  shareholderNature?: string;             // 股东种类（t1/t2/t3名册）
  accountStatus?: string;                 // 账户状态（t1/t2/t3名册）

  // 新增股东类型字段 - 2025-06-25 11:33:27 hayden 添加
  shareholderType?: string;               // 股东类型（如"知名牛散"、"境内个人"等）
};

/**
 * 股东列表响应类型
 */
export type ShareholdersResponse = {
  shareholders: ShareholderItem[];
  pagination: Pagination;
};

/**
 * 请求类型导出（从验证模式中推导）
 */
export type UploadRegistryRequest = z.infer<typeof UploadRegistrySchema>;
export type ListRegistryRequest = z.infer<typeof ListRegistrySchema>;
export type ShareholdersRequest = z.infer<typeof ShareholdersSchema>;
export type DeleteRegistryRequest = z.infer<typeof DeleteRegistrySchema>;
export type BatchDeleteRegistryRequest = z.infer<typeof BatchDeleteRegistrySchema>; 