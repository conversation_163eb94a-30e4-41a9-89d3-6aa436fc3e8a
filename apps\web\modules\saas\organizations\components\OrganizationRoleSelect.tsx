// 导入组织成员角色类型定义
import type { OrganizationMemberRole } from "@repo/auth";
// 导入获取组织成员角色的自定义hook
import { useOrganizationMemberRoles } from "@saas/organizations/hooks/member-roles";
// 导入Select组件及其子组件
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";

/**
 * 组织角色选择组件
 * @param value - 当前选中的角色值
 * @param onSelect - 角色选择变更时的回调函数
 * @param disabled - 是否禁用选择器
 */
export function OrganizationRoleSelect({
	value,
	onSelect,
	disabled,
}: {
	value: OrganizationMemberRole;
	onSelect: (value: OrganizationMemberRole) => void;
	disabled?: boolean;
}) {
	// 获取组织成员角色配置
	const organizationMemberRoles = useOrganizationMemberRoles();

	// 将角色配置转换为Select组件需要的选项格式
	const roleOptions = Object.entries(organizationMemberRoles).map(
		([value, label]) => ({
			value,
			label,
		}),
	);

	// 渲染Select组件
	return (
		<Select value={value} onValueChange={onSelect} disabled={disabled}>
			{/* 选择器触发器 */}
			<SelectTrigger>
				<SelectValue />
			</SelectTrigger>
			{/* 选择器下拉内容 */}
			<SelectContent>
				{/* 遍历角色选项并渲染 */}
				{roleOptions.map((option) => (
					<SelectItem key={option.value} value={option.value}>
						{option.label}
					</SelectItem>
				))}
			</SelectContent>
		</Select>
	);
}
