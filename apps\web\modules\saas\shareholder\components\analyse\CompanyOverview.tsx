/**
 * 股东名册分析 - 公司概览组件
 * @file CompanyOverview.tsx
 * @description 用于展示公司股东结构的概览信息
 * <AUTHOR>
 * @created 2025-06-12 17:26:08
 * @modified 2025-06-12 19:16:13
 * @modified 2025-06-18 15:59:48 - 调整头部背景渐变颜色，从深紫色改为柔和的蓝灰色调，更适合数据分析场景
 * @modified 2025-06-18 16:02:35 - 移除灰色调，改用纯净蓝色系渐变(from-blue-600 via-blue-700 to-indigo-800)，更契合数据分析场景
 * @modified 2025-06-23 11:16:15 - 在持股比例分布图表的圆环内部添加Label显示文字标签，移除数值显示
 * @modified 2025-06-23 11:18:20 - 修正Label位置配置，使文字正确显示在各自圆环的中间位置
 * @modified 2025-06-23 11:20:15 - 调整Label位置为insideTop，使文字显示在圆环的上方位置
 * @modified 2025-06-23 11:45:48 - 修正Label位置为top，确保文字正确显示在圆环的上方
 * @modified 2025-06-23 11:48:34 - 修正圆环显示和Label位置：显示完整圆环，文字标签正确定位在上方
 * @modified 2025-06-23 12:12:55 - 调整同心圆标识显示方式：将标识从垂直排列改为显示在各自圆环的灰色部分，移除Label组件改用自定义label函数
 * @modified 2025-06-23 12:17:51 - 重新实现标识定位：使用独立的text元素替代label函数，精确控制标识在圆环中的位置
 * @modified 2025-06-23 12:20:46 - 调整标识布局：将三个标识在12点钟方向垂直并列显示，类似参考图片效果
 * @modified 2025-06-23 12:42:05 - 调整标识位置：使每个标识显示在其对应的同心圆上，增加间距以匹配圆环层级
 * @modified 2025-06-23 14:17:43 - 优化小屏幕适配：圆环标签在小屏幕下隐藏，中等屏幕及以上显示，并调整字体大小为8px
 * @modified 2025-06-23 14:24:29 - 修复小屏幕超出问题：调整容器高度，优化底部图例布局，缩小字体和图标尺寸，添加截断处理
 * @modified 2025-06-23 14:28:39 - 修复中屏图例覆盖问题：调整响应式断点从lg改为xl，让中等屏幕也使用底部图例
 * @modified 2025-06-23 14:35:58 - 优化图例显示格式：添加持股数量和比例信息，格式为"股东群体 持股数量（比例%）"
 */

"use client";

import { useEffect, useState } from "react";
import {
	ResponsiveContainer,
	PieChart,
	Pie,
	Cell,
	Tooltip,
} from "recharts";
import { RefreshCcw } from "lucide-react";
import { cn } from "@ui/lib";
import { Button } from "@ui/components/button";
import { useCompanyOverview } from "@saas/shareholder/hooks/useCompanyOverview";
import { CompanyOverviewSkeleton } from "./CompanyOverviewSkeleton";
import type { ShareholderTypeItem } from "@saas/shareholder/lib/company-overview-api";
import { useQueryClient } from "@tanstack/react-query";

/**
 * 公司概览渲染数据接口
 * @interface CompanyOverviewRenderData
 * @description 用于组件渲染的数据结构，基于 API 数据转换而来
 * <AUTHOR>
 * @created 2025-06-16 13:55:57
 */
interface CompanyOverviewRenderData {
	companyName: string;
	stockCode: string;
	analysisDateRange: string;
	generatedTime: string;
	metrics: {
		latestPeriod: {
			date: string;
		};
		latestProduct: {
			value: number;
			change: number;
		};
		latestTotalCapital: {
			value: number;
			changePercent: string;
		};
		totalPersonalShareholders: {
			value: number;
			change: string;
		};
		totalInstitutionalShareholders: {
			value: number;
			change: string;
		};
		totalShareholders: {
			value: number;
			change: string;
		};
		totalPersonalShares: {
			value: number;
			changePercent: string;
		};
		totalInstitutionalShares: {
			value: number;
			changePercent: string;
		};
		totalProductShares: {
			value: number;
			change: string;
		};
		topTenConcentration: {
			value: string;
			changePercent: string;
		};
		topTwentyConcentration: {
			value: string;
			changePercent: string;
		};
	};
}



/**
 * 指标卡片组件
 * @param title 指标标题
 * @param value 指标值
 * @param change 变化量
 * @returns 指标卡片UI组件
 * @modified 2025-06-16 18:02:15 - 添加响应式字体大小支持，解决小屏幕字体过大问题
 * @modified 2025-06-18 15:54:03 - 统一变化数值为黑色字体，正数带+号，负数带-号，移除未使用的isPositive参数
 * @modified 2025-06-30 10:21:12 - 添加border边框样式，解决暗色主题下卡片边界不可见问题 - hayden
 * <AUTHOR>
 */
function MetricCard({
	title,
	value,
	change
}: {
	title: string;
	value: string | number;
	change?: string | number;
}): JSX.Element {
	/**
	 * 格式化变化数值，确保正数带+号，负数带-号
	 * @param changeValue 变化值
	 * @returns 格式化后的变化值字符串
	 * <AUTHOR>
	 * @created 2025-06-18 15:54:03
	 */
	function formatChangeValue(changeValue: string | number): string {
		const changeStr = String(changeValue);

		// 如果已经有符号，直接返回
		if (changeStr.startsWith("+") || changeStr.startsWith("-")) {
			return changeStr;
		}

		// 判断是否为负数（包含负号或者数值小于0）
		const numValue = Number.parseFloat(changeStr.replace(/[^\d.-]/g, ''));
		if (numValue < 0) {
			// 如果是负数但没有负号，添加负号
			return changeStr.startsWith("-") ? changeStr : `-${changeStr}`;
		}

		if (numValue > 0) {
			// 如果是正数，添加正号
			return `+${changeStr}`;
		}

		// 如果是0，不添加符号
		return changeStr;
	}

	return (
		<div className="rounded-lg bg-card border border-border p-2 sm:p-3 shadow-sm">
			{/* 响应式标题字体：小屏幕使用更小字体 */}
			<div className="text-[10px] sm:text-xs truncate" title={title}>
				{title}
			</div>
			<div className="mt-0.5 sm:mt-1 flex items-end justify-between gap-1">
				{/* 响应式数值字体：根据屏幕大小调整 */}
				<div className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold truncate flex-1 min-w-0">
					{typeof value === 'number' ? value.toLocaleString() : value}
				</div>
				{change && (
					<div className="text-[9px] sm:text-[10px] md:text-xs flex-shrink-0 text-foreground">
						{formatChangeValue(change)}
					</div>
				)}
			</div>
		</div>
	);
}


/**
 * 持股比例分布饼图组件属性接口
 * @interface ShareholdingDistributionChartProps
 * <AUTHOR>
 * @created 2025-06-16 14:14:19
 * @modified 2025-06-19 14:01:45 - 添加持股数量字段支持，用于在图表上显示具体持股数量 - hayden
 */
interface ShareholdingDistributionChartProps {
	/** 控股股东持股比例 */
	controllingRatio?: number;
	/** 前十股东持股比例 */
	top10Ratio?: number;
	/** 前百股东持股比例 */
	top100Ratio?: number;
	/** 第一大股东持股数量（股） */
	top1ShareholdingAmount?: number;
	/** 前十大股东合计持股数量（股） */
	top10ShareholdingAmount?: number;
	/** 前一百大股东合计持股数量（股） */
	top100ShareholdingAmount?: number;
}

/**
 * 持股比例分布饼图组件
 * @param props 组件属性
 * @returns 持股比例分布饼图UI组件
 * <AUTHOR>
 */
function ShareholdingDistributionChart({
	controllingRatio = 0,
	top10Ratio = 0,
	top100Ratio = 0,
	top1ShareholdingAmount = 0,
	top10ShareholdingAmount = 0,
	top100ShareholdingAmount = 0
}: ShareholdingDistributionChartProps): JSX.Element {
	// 响应式状态管理 - 检测屏幕大小
	const [isSmallScreen, setIsSmallScreen] = useState(false);

	useEffect(() => {
		/**
		 * 检查屏幕大小的函数
		 * <AUTHOR>
		 * @created 2025-06-23 14:17:43
		 */
		function checkScreenSize(): void {
			setIsSmallScreen(window.innerWidth < 640); // sm breakpoint
		}

		// 初始检查
		checkScreenSize();

		// 添加窗口大小变化监听器
		window.addEventListener('resize', checkScreenSize);

		// 清理监听器
		return () => {
			window.removeEventListener('resize', checkScreenSize);
		};
	}, []);

	// 确保所有传入的值都是数字类型，并设置默认值用于测试
	const safeControllingRatio = Number(controllingRatio) || 0;
	const safeTop10Ratio = Number(top10Ratio) || 0;
	const safeTop100Ratio = Number(top100Ratio) || 0;

	// 确保持股数量字段都是数字类型
	const safeTop1ShareholdingAmount = Number(top1ShareholdingAmount) || 0;
	const safeTop10ShareholdingAmount = Number(top10ShareholdingAmount) || 0;
	const safeTop100ShareholdingAmount = Number(top100ShareholdingAmount) || 0;

	/**
	 * 格式化持股数量显示
	 * @param amount 持股数量（股）
	 * @returns 格式化后的持股数量字符串
	 * <AUTHOR>
	 * @created 2025-06-19 14:01:45
	 * @modified 2025-06-23 14:38:46 - 简化显示格式，直接显示股数不带单位，减少文字长度
	 */
	function formatShareholdingAmount(amount: number): string {
		if (amount === 0) {
			return "0";
		}

		// 直接返回格式化的数字，不带单位
		return amount.toLocaleString();
	}

	/**
	 * 格式化图例显示文本 - 紧凑格式
	 * @param name 股东群体名称
	 * @param amount 持股数量
	 * @param ratio 持股比例
	 * @returns 格式化后的图例文本
	 * <AUTHOR>
	 * @created 2025-06-23 14:38:46
	 */
	function formatLegendText(name: string, amount: number, ratio: number): string {
		return `${name} ${formatShareholdingAmount(amount)}（${ratio.toFixed(1)}%）`;
	}

	/**
	 * 同心圆图表数据配置
	 * <AUTHOR>
	 * @created 2025-06-16 14:34:54
	 * @modified 2025-06-16 14:34:54 - 使用RadialBarChart实现真正的同心圆图表
	 */

	/**
	 * 图例数据配置 - 只显示股东群体名称
	 * <AUTHOR>
	 * @created 2025-06-19 14:33:28
	 * @modified 2025-06-23 10:17:21 - 更新图例格式为：股东群体 数量（比例%） - hayden
	 * @modified 2025-06-23 10:36:52 - 简化图例显示，只显示股东群体名称，详细信息通过悬浮显示 - hayden
	 * @modified 2025-06-23 12:09:23 - 修改控股股东颜色为紫色系 - hayden
	 */
	const legendData = [
		{
			name: "控股股东",
			fill: "#7c3aed", // 原色值: "#1e40af" - 修改为紫色系 - hayden - 2025-06-23 12:09:23
			ratio: safeControllingRatio,
			amount: safeTop1ShareholdingAmount,
		},
		{
			name: "前十股东",
			fill: "#7dd3fc",
			ratio: safeTop10Ratio,
			amount: safeTop10ShareholdingAmount,
		},
		{
			name: "前百股东",
			fill: "#10b981",
			ratio: safeTop100Ratio,
			amount: safeTop100ShareholdingAmount,
		},
	];

	// 检查是否有有效数据 - 如果所有比例都为0，则显示空数据状态
	// 修改记录 2025-06-30 11:34:22 - 添加空数据状态处理，与InstitutionalSharesDistributionChart组件保持一致 - hayden
	const hasValidData = safeControllingRatio > 0 || safeTop10Ratio > 0 || safeTop100Ratio > 0;

	// 如果没有数据，显示暂无数据状态
	if (!hasValidData) {
		return (
			<div className="h-full">
				<h3 className="mb-1 sm:mb-2 text-xs sm:text-sm font-medium">
					持股比例分布
				</h3>
				{/* 修改记录 2025-06-30 11:34:22 - 调整暂无数据状态高度，与图表容器保持一致 - hayden */}
				<div className="h-[260px] sm:h-[280px] flex items-center justify-center text-muted-foreground">
					<div className="text-center">
						<div className="text-xs sm:text-sm">暂无数据</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="h-full">
			<h3 className="mb-1 sm:mb-2 text-xs sm:text-sm font-medium">
				持股比例分布
			</h3>

			{/* 响应式布局：大屏幕使用图例在右上角，小屏幕使用图例在底部 */}
			{/* 修改记录 2025-06-23 14:24:29 - 调整容器高度，为小屏幕底部图例预留空间，防止超出 - hayden */}
			{/* 修改记录 2025-06-23 14:51:01 - 修复图表容器布局，为右上角图例预留足够空间，避免图例被挤压 - hayden */}
			{/* 修改记录 2025-06-30 10:12:56 - 减少图表容器高度，解决底部空白过多问题 - hayden */}
			<div className="h-[260px] sm:h-[280px] relative">
				{/* 图表容器 - 为中小屏幕底部图例预留空间，为大屏幕右侧图例预留空间 */}
				{/* 修改记录 2025-06-23 14:28:39 - 调整断点从lg改为xl，为更多屏幕尺寸预留底部空间 - hayden */}
				{/* 修改记录 2025-06-23 14:51:01 - 为大屏幕右侧图例预留空间，避免图例覆盖图表 - hayden */}
				{/* 修改记录 2025-06-27 19:17:13 - 减少底部间距，从pb-6改为pb-2，解决图表底部空白过多问题 - hayden */}
				{/* 修改记录 2025-06-30 10:12:56 - 进一步优化底部间距，移除不必要的pb-2 - hayden */}
				<div className="w-full h-full xl:pr-32">
					{" "}
					{/* 中小屏幕底部预留空间，超大屏幕右侧预留图例空间 - hayden - 2025-06-23 14:51:01 */}
					<ResponsiveContainer width="100%" height="100%">
						<PieChart>
							{/* 外层圆环 - 前百股东 */}
							{/* 修改记录 2025-06-27 19:02:58 - 调整圆环位置向左，统一靠左布局 - hayden */}
							<Pie
								data={[
									{
										name: "前百股东",
										value: safeTop100Ratio,
										amount: safeTop100ShareholdingAmount,
									},
									{
										name: "其他",
										value: 100 - safeTop100Ratio,
										amount: 0,
									},
								]}
								cx="40%"
								cy="50%"
								innerRadius="70%"
								outerRadius="83%"
								startAngle={90}
								endAngle={450}
								dataKey="value"
							>
								<Cell fill="#10b981" />
								<Cell fill="#e5e7eb" />
							</Pie>

							{/* 中层圆环 - 前十股东 */}
							<Pie
								data={[
									{
										name: "前十股东",
										value: safeTop10Ratio,
										amount: safeTop10ShareholdingAmount,
									},
									{
										name: "其他",
										value: 100 - safeTop10Ratio,
										amount: 0,
									},
								]}
								cx="40%"
								cy="50%"
								innerRadius="50%"
								outerRadius="63%"
								startAngle={90}
								endAngle={450}
								dataKey="value"
							>
								<Cell fill="#7dd3fc" />
								<Cell fill="#e5e7eb" />
							</Pie>

							{/* 内层圆环 - 控股股东 */}
							<Pie
								data={[
									{
										name: "控股股东",
										value: safeControllingRatio,
										amount: safeTop1ShareholdingAmount,
									},
									{
										name: "其他",
										value: 100 - safeControllingRatio,
										amount: 0,
									},
								]}
								cx="40%"
								cy="50%"
								innerRadius="30%"
								outerRadius="43%"
								startAngle={90}
								endAngle={450}
								dataKey="value"
							>
								<Cell fill="#7c3aed" />{" "}
								{/* 原色值: "#1e40af" - 修改为紫色系 - hayden - 2025-06-23 12:09:23 */}
								<Cell fill="#e5e7eb" />
							</Pie>

							{/* 外层圆环标签 - 前百股东 (在外层圆环上) - 响应式字体大小 */}
							{/* 修改记录 2025-06-27 19:02:58 - 调整文本标签位置向左，配合圆环位置调整 - hayden */}
							{!isSmallScreen && (
								<text
									x="40%"
									y="14%"
									fill="#374151"
									textAnchor="middle"
									dominantBaseline="middle"
									fontSize="8px" // 原值: "10px" - 调整为更小字体适配小屏幕 - hayden - 2025-06-23 14:17:43
									fontWeight="bold"
								>
									前百股东
								</text>
							)}

							{/* 中层圆环标签 - 前十股东 (在中层圆环上) - 响应式字体大小 */}
							{!isSmallScreen && (
								<text
									x="40%"
									y="24%"
									fill="#374151"
									textAnchor="middle"
									dominantBaseline="middle"
									fontSize="8px" // 原值: "10px" - 调整为更小字体适配小屏幕 - hayden - 2025-06-23 14:17:43
									fontWeight="bold"
								>
									前十股东
								</text>
							)}

							{/* 内层圆环标签 - 控股股东 (在内层圆环上) - 响应式字体大小 */}
							{!isSmallScreen && (
								<text
									x="40%"
									y="33%"
									fill="#374151"
									textAnchor="middle"
									dominantBaseline="middle"
									fontSize="8px" // 原值: "10px" - 调整为更小字体适配小屏幕 - hayden - 2025-06-23 14:17:43
									fontWeight="bold"
								>
									控股股东
								</text>
							)}

							<Tooltip
								content={({ active, payload }) => {
									if (active && payload && payload.length) {
										const data = payload[0].payload;
										// 添加数据安全检查
										if (
											!data ||
											typeof data.value === "undefined" ||
											data.name === "其他"
										) {
											return null;
										}
										return (
											<div className="bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-border">
												<div className="text-sm font-medium text-foreground">
													{data.name || "未知"}
												</div>
												<div className="text-xs text-foreground/80 mt-1">
													持股数量：
													{formatShareholdingAmount(
														data.amount || 0,
													)}
												</div>
												<div className="text-xs text-foreground/80">
													持股比例：
													{(data.value || 0).toFixed(
														1,
													)}
													%
												</div>
											</div>
										);
									}
									return null;
								}}
							/>
						</PieChart>
					</ResponsiveContainer>
				</div>

				{/* 超大屏幕右上角图例 - 显示股东群体名称、持股数量和比例 */}
				{/* 修改记录 2025-06-23 14:28:39 - 调整断点从lg改为xl，避免中等屏幕图例覆盖图表 - hayden */}
				{/* 修改记录 2025-06-23 14:35:58 - 更新图例格式，显示持股数量和比例信息 - hayden */}
				<div className="absolute top-1 right-1 hidden xl:block">
					<div className="bg-background/90 backdrop-blur-sm rounded-md p-1 shadow-sm border border-border max-h-[280px] overflow-y-aut">
						{legendData.map((item, index) => (
							<div
								key={index}
								className="flex items-center gap-2 mb-1 last:mb-0"
							>
								<div
									className="w-3 h-3 rounded-sm flex-shrink-0"
									style={{ backgroundColor: item.fill }}
								/>
								<div className="text-[9px] text-foreground/80 leading-tight">
									{" "}
									{/* 原值: "text-[10px]" - 缩小字体适配更多内容 - hayden - 2025-06-23 14:38:46 */}
									<div className="font-medium">
										{formatLegendText(
											item.name,
											item.amount,
											item.ratio,
										)}
									</div>
								</div>
							</div>
						))}
					</div>
				</div>

				{/* 中小屏幕底部图例 - 显示股东群体名称、持股数量和比例 */}
				{/* 修改记录 2025-06-23 14:17:43 - 进一步优化小屏幕字体大小，从9px调整为8px - hayden */}
				{/* 修改记录 2025-06-23 14:24:29 - 优化布局防止超出，添加内边距和更好的间距控制，极小屏幕隐藏图例 - hayden */}
				{/* 修改记录 2025-06-23 14:28:39 - 调整断点从lg改为xl，让中等屏幕也使用底部图例 - hayden */}
				{/* 修改记录 2025-06-23 14:35:58 - 更新图例格式，显示持股数量和比例信息，调整布局适配更多内容 - hayden */}
				<div className="absolute bottom-0 left-0 right-0 hidden sm:flex xl:hidden flex-wrap justify-center gap-0.5 px-1 py-0.5">
					{" "}
					{/* 原值: "gap-1 py-1" - 进一步缩小间距 - hayden - 2025-06-23 14:38:46 */}
					{legendData.map((item, index) => (
						<div
							key={index}
							className="flex items-center gap-0.5 min-w-0 max-w-[32%]"
						>
							{" "}
							{/* 原值: "max-w-[30%]" - 稍微增加宽度适配内容 - hayden - 2025-06-23 14:38:46 */}
							<div
								className="w-1.5 h-1.5 rounded-sm flex-shrink-0"
								style={{ backgroundColor: item.fill }}
							/>
							<div className="text-[5px] text-foreground/80 leading-tight text-center truncate">
								{" "}
								{/* 原值: "text-[6px]" - 进一步缩小字体适配更紧凑内容 - hayden - 2025-06-23 14:38:46 */}
								<div className="font-medium">
									{formatLegendText(
										item.name,
										item.amount,
										item.ratio,
									)}
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}


/**
 * 机构股东类型分布图表组件属性接口
 * @interface InstitutionalSharesDistributionChartProps
 * <AUTHOR>
 * @created 2025-06-27 18:28:05
 */
interface InstitutionalSharesDistributionChartProps {
	/** 股东类型分布数据 */
	shareholderTypes?: ShareholderTypeItem[];
	/** 显示模式：'count' 为户数分布，'shares' 为持股分布 */
	mode?: 'count' | 'shares';
}

/**
 * 机构股东类型分布饼图组件
 * @param props 组件属性
 * @returns 机构股东类型分布饼图UI组件
 * @modified 2025-06-16 18:03:45 - 添加响应式字体大小支持
 * @modified 2025-06-27 18:28:05 - 重构为真实数据驱动的图表组件，支持户数分布和持股分布两种模式 - hayden
 * <AUTHOR>
 */
function InstitutionalSharesDistributionChart({
	shareholderTypes = [],
	mode = 'count'
}: InstitutionalSharesDistributionChartProps): JSX.Element {


	/**
	 * 转换股东类型数据为图表数据
	 * @param data 股东类型数据
	 * @param displayMode 显示模式
	 * @returns 图表数据
	 * <AUTHOR>
	 * @created 2025-06-27 18:28:05
	 */
	function transformShareholderTypesToChartData(
		data: ShareholderTypeItem[],
		displayMode: 'count' | 'shares'
	): Array<{
		name: string;
		value: number;
		count: number;
		shares: number;
		countPercentage: number;
		sharesPercentage: number;
		fill: string;
	}> {
		if (!data || data.length === 0) {
			return [];
		}

		// 定义简约颜色配置 - 参考ShareholdingDistributionChart，支持暗色主题适配
		const colors = [
			"hsl(210, 70%, 60%)",   // 柔和蓝色
			"hsl(142, 60%, 50%)",   // 柔和绿色
			"hsl(38, 80%, 60%)",    // 柔和橙色
			"hsl(358, 60%, 65%)",   // 柔和红色
			"hsl(262, 70%, 65%)",   // 柔和紫色
			"hsl(173, 50%, 55%)",   // 柔和青色
			"hsl(43, 60%, 70%)",    // 柔和黄色
			"hsl(197, 60%, 70%)",   // 柔和浅蓝色
			"hsl(326, 60%, 70%)",   // 柔和粉色
			"hsl(84, 60%, 55%)"     // 柔和草绿色
		];

		return data.map((item, index) => {
			const count = Number.parseInt(item.typeCount) || 0;
			const shares = Number.parseInt(item.typeShares) || 0;
			const countPercentage = Number.parseFloat(item.typePercentage) || 0;
			const sharesPercentage = Number.parseFloat(item.sharesPercentage) || 0;

			return {
				name: item.shareholderType,
				value: displayMode === 'count' ? countPercentage : sharesPercentage,
				count,
				shares,
				countPercentage,
				sharesPercentage,
				fill: colors[index % colors.length]
			};
		}).filter(item => item.value > 0); // 过滤掉值为0的项
	}

	// 转换数据
	const chartData = transformShareholderTypesToChartData(shareholderTypes, mode);

	/**
	 * 格式化数值显示
	 * @param value 数值
	 * @returns 格式化后的字符串
	 * <AUTHOR>
	 * @created 2025-06-27 18:28:05
	 */
	function formatNumber(value: number): string {
		if (value === 0) {
			return "0";
		}
		return value.toLocaleString();
	}



	// 如果没有数据，显示暂无数据状态
	if (chartData.length === 0) {
		return (
			<div className="h-full">
				<h3 className="mb-1 sm:mb-2 text-xs sm:text-sm font-medium">
					股东类型{mode === 'count' ? '户数' : '持股'}分布（TOP200）
				</h3>
				{/* 修改记录 2025-06-30 10:12:56 - 调整暂无数据状态高度，与图表容器保持一致 - hayden */}
				<div className="h-[260px] sm:h-[280px] flex items-center justify-center text-muted-foreground">
					<div className="text-center">
						<div className="text-xs sm:text-sm">暂无数据</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="h-full">
			<h3 className="mb-1 sm:mb-2 text-xs sm:text-sm font-medium">
				股东类型{mode === 'count' ? '户数' : '持股'}分布（TOP200）
			</h3>

			{/* 响应式布局：参考ShareholdingDistributionChart的设计 */}
			{/* 修改记录 2025-06-30 10:12:56 - 减少图表容器高度，与ShareholdingDistributionChart保持一致 - hayden */}
			<div className="h-[260px] sm:h-[280px] relative">
				{/* 图表容器 - 参考ShareholdingDistributionChart的布局 */}
				{/* 修改记录 2025-06-27 19:17:13 - 减少底部间距，从pb-6改为pb-2，与ShareholdingDistributionChart保持一致 - hayden */}
				{/* 修改记录 2025-06-30 10:12:56 - 进一步优化底部间距，移除不必要的pb-2，与ShareholdingDistributionChart保持一致 - hayden */}
				<div className="w-full h-full xl:pr-32">
					<ResponsiveContainer width="100%" height="100%">
						<PieChart>
							{/* 修改记录 2025-06-27 19:02:58 - 调整圆环位置向左，避免图例覆盖，统一靠左布局 - hayden */}
							<Pie
								data={chartData}
								cx="40%"
								cy="50%"
								innerRadius="65%"
								outerRadius="80%"
								dataKey="value"
								startAngle={90}
								endAngle={450}
							>
								{chartData.map((entry, index) => (
									<Cell key={`cell-${index}`} fill={entry.fill} />
								))}
							</Pie>
							<Tooltip
								content={({ active, payload }) => {
									if (active && payload && payload.length) {
										const data = payload[0].payload;
										return (
											<div className="bg-background/95 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-border">
												<div className="text-sm font-medium text-foreground">
													{data.name}
												</div>
												<div className="text-xs text-muted-foreground mt-1">
													股东数量：{formatNumber(data.count)}
												</div>
												<div className="text-xs text-muted-foreground">
													户数占比：{data.countPercentage.toFixed(1)}%
												</div>
												<div className="text-xs text-muted-foreground">
													持股数量：{formatNumber(data.shares)}
												</div>
												<div className="text-xs text-muted-foreground">
													持股占比：{data.sharesPercentage.toFixed(1)}%
												</div>
											</div>
										);
									}
									return null;
								}}
							/>
						</PieChart>
					</ResponsiveContainer>
				</div>

				{/* 超大屏幕右上角图例 - 参考ShareholdingDistributionChart的设计，调整位置避免覆盖 */}
				<div className="absolute top-1 right-1 hidden xl:block">
					<div className="bg-background/90 backdrop-blur-sm rounded-md p-1 shadow-sm border border-border max-h-[280px] overflow-y-auto">
						{chartData.map((item, index) => (
							<div
								key={index}
								className="flex items-center gap-2 mb-1 last:mb-0"
							>
								<div
									className="w-3 h-3 rounded-sm flex-shrink-0"
									style={{ backgroundColor: item.fill }}
								/>
								<div className="text-[9px] text-foreground/80 leading-tight">
									<div className="font-medium">
										{mode === 'count'
											? `${item.name} ${formatNumber(item.count)}（${item.countPercentage.toFixed(1)}%）`
											: `${item.name} ${formatNumber(item.shares)}（${item.sharesPercentage.toFixed(1)}%）`
										}
									</div>
								</div>
							</div>
						))}
					</div>
				</div>

				{/* 中小屏幕底部图例 - 参考ShareholdingDistributionChart的设计 */}
				<div className="absolute bottom-0 left-0 right-0 hidden sm:flex xl:hidden flex-wrap justify-center gap-0.5 px-1 py-0.5">
					{chartData.slice(0, 6).map((item, index) => ( // 限制显示前6项避免过度拥挤
						<div key={index} className="flex items-center gap-0.5 min-w-0 max-w-[32%]">
							<div
								className="w-1.5 h-1.5 rounded-sm flex-shrink-0"
								style={{ backgroundColor: item.fill }}
							/>
							<div className="text-[5px] text-foreground/80 leading-tight text-center truncate">
								<div className="font-medium">
									{mode === 'count'
										? `${item.name} ${formatNumber(item.count)}（${item.countPercentage.toFixed(1)}%）`
										: `${item.name} ${formatNumber(item.shares)}（${item.sharesPercentage.toFixed(1)}%）`
									}
								</div>
							</div>
						</div>
					))}
					{chartData.length > 6 && (
						<div className="text-[5px] text-foreground/60">
							+{chartData.length - 6}项
						</div>
					)}
				</div>
			</div>
		</div>
	);
}

/**
 * 公司概览组件属性接口
 * @interface CompanyOverviewProps
 * @modified 2025-06-16 12:25:02 - 添加组织ID属性支持
 */
interface CompanyOverviewProps {
	/** 组织ID，用于获取公司概览数据 */
	organizationId?: string;
}

/**
 * 公司概览组件
 * @param props 组件属性
 * @returns 公司概览UI组件
 * @modified 2025-06-12 18:15:53 - 将生成时间移至右下角底部
 * @modified 2025-06-12 18:18:06 - 调整生成时间位置，使其与公司名称和分析范围保持在同一水平线上
 * @modified 2025-06-12 18:20:28 - 调整生成时间位置，使其再下移一点
 * @modified 2025-06-16 12:25:02 - 添加组织ID参数支持和日志打印功能
 * @modified 2025-06-16 13:47:17 - 添加真实API数据渲染支持，将API数据映射到界面指标
 */
export function CompanyOverview({ organizationId }: CompanyOverviewProps): JSX.Element {
	// 获取 queryClient 实例用于刷新所有相关查询
	const queryClient = useQueryClient();

	// 使用 useCompanyOverview 钩子获取真实数据
	const {data: apiData, isLoading, error, refetch } = useCompanyOverview(organizationId || "", {
		enabled: !!organizationId, // 只有当 organizationId 存在时才启用查询
	});

	/**
	 * 解析API数据并转换为组件所需格式
	 * @param apiResponse API响应数据
	 * @returns 解析后的公司数据
	 * <AUTHOR>
	 * @created 2025-06-16 13:47:17
	 */
	function parseApiData(apiResponse: any): any {
		if (!apiResponse?.data) {
			return null;
		}

		try {
			// 解析字符串格式的数据
			let parsedData: any;
			if (typeof apiResponse.data === 'string') {
				parsedData = JSON.parse(apiResponse.data);
			} else {
				parsedData = apiResponse.data;
			}

			// 如果是数组，取第一个元素
			const dataItem = Array.isArray(parsedData) ? parsedData[0] : parsedData;

			// 如果有嵌套的data字段，提取出来
			const companyData = dataItem?.data || dataItem;

			return companyData;
		} catch (error) {
			console.error("解析API数据失败:", error);
			return null;
		}
	}

	// 解析API数据
	const parsedApiData = parseApiData(apiData);

	/**
	 * 全面刷新所有股东分析相关数据
	 * @description 刷新公司概览、总体报告、趋势分析、股东变动分析等所有相关数据
	 * <AUTHOR>
	 * @created 2025-06-30 10:43:08
	 */
	function handleRefreshAllData(): void {
		if (!organizationId) {
			return;
		}

		// 刷新公司概览数据
		refetch();

		// 刷新所有相关的查询缓存
		queryClient.invalidateQueries({
			queryKey: ["companyGeneralReport", organizationId]
		});
		queryClient.invalidateQueries({
			queryKey: ["shareholdersTrend", organizationId]
		});

		// 刷新自然人股东变动分析数据
		queryClient.invalidateQueries({
			queryKey: ["increaseIndividualShareholders", organizationId]
		});
		queryClient.invalidateQueries({
			queryKey: ["decreaseIndividualShareholders", organizationId]
		});
		queryClient.invalidateQueries({
			queryKey: ["newIndividualShareholders", organizationId]
		});
		queryClient.invalidateQueries({
			queryKey: ["exitIndividualShareholders", organizationId]
		});

		// 刷新机构股东变动分析数据
		queryClient.invalidateQueries({
			queryKey: ["increaseInstitutionShareholders", organizationId]
		});
		queryClient.invalidateQueries({
			queryKey: ["decreaseInstitutionShareholders", organizationId]
		});
		queryClient.invalidateQueries({
			queryKey: ["newInstitutionShareholders", organizationId]
		});
		queryClient.invalidateQueries({
			queryKey: ["exitInstitutionShareholders", organizationId]
		});
	}

	/**
	 * 转换API数据为渲染数据格式
	 * @param apiData 解析后的API数据
	 * @param timestamp API响应时间戳
	 * @returns 渲染数据
	 * <AUTHOR>
	 * @created 2025-06-16 13:55:57
	 * @modified 2025-06-16 14:05:52 - 添加空值检查，防止访问null对象属性导致的运行时错误
	 */
	function transformApiDataToRenderData(apiData: any, timestamp?: string): CompanyOverviewRenderData {
		// 添加空值检查，确保 apiData 不为 null 或 undefined
		if (!apiData) {

			// 返回默认的空数据结构
			return {
				companyName: "暂无数据",
				stockCode: "000000",
				analysisDateRange: "数据范围未知",
				generatedTime: new Date().toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit',
					second: '2-digit'
				}),
				metrics: {
					// 第一行指标数据 - 默认值
					latestPeriod: {
						date: "未知",
					},
					latestProduct: {
						value: 0,
						change: 0
					},
					latestTotalCapital: {
						value: 0,
						changePercent: "0%"
					},
					totalPersonalShareholders: {
						value: 0,
						change: "0"
					},
					totalInstitutionalShareholders: {
						value: 0,
						change: "0"
					},
					totalShareholders: {
						value: 0,
						change: "0"
					},
					// 第二行指标数据 - 默认值
					totalPersonalShares: {
						value: 0,
						changePercent: "0%"
					},
					totalInstitutionalShares: {
						value: 0,
						changePercent: "0%"
					},
					totalProductShares: {
						value: 0,
						change: "0"
					},
					topTenConcentration: {
						value: "0%",
						changePercent: "0%"
					},
					topTwentyConcentration: {
						value: "0%",
						changePercent: "0%"
					}
				}
			};
		}

		return {
			companyName: apiData.companyName || "未知公司",
			stockCode: apiData.companyCode || "000000",
			analysisDateRange: apiData.registerDate && apiData.oldestRegisterDate ?
				`${apiData.oldestRegisterDate.split('T')[0]} 至 ${apiData.registerDate.split('T')[0]}` :
				"数据范围未知",
			generatedTime: timestamp ?
				new Date(timestamp).toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit',
					second: '2-digit'
				}) :
				new Date().toLocaleString('zh-CN'),
			metrics: {
				// 第一行指标数据
				latestPeriod: {
					date: apiData.registerDate?.split('T')[0] || "未知",
				},
				latestProduct: {
					value: apiData.totalShareholders || 0,
					change: apiData.shareholdersChange || 0
				},
				latestTotalCapital: {
					value: apiData.totalShares ? Math.round(Number(apiData.totalShares) / 10000) : 0,
					changePercent: apiData.totalSharesChangePercent ? `${apiData.totalSharesChangePercent}%` : "0%"
				},
				totalPersonalShareholders: {
					value: apiData.individualShareholders || 0,
					change: apiData.individualShareholdersChange ?
						(apiData.individualShareholdersChange >= 0 ? `+${apiData.individualShareholdersChange}` : `${apiData.individualShareholdersChange}`) :
						"0"
				},
				totalInstitutionalShareholders: {
					value: apiData.totalInstitutions || 0,
					change: apiData.institutionsChange ?
						(apiData.institutionsChange >= 0 ? `+${apiData.institutionsChange}` : `${apiData.institutionsChange}`) :
						"0"
				},
				totalShareholders: {
					value: apiData.creditAccountCount || 0,
					change: apiData.creditAccountCountChange ?
						(apiData.creditAccountCountChange >= 0 ? `+${apiData.creditAccountCountChange}` : `${apiData.creditAccountCountChange}`) :
						"0"
				},
				// 第二行指标数据
				totalPersonalShares: {
					value: apiData.institutionShares ? Number(apiData.institutionShares) : 0,
					changePercent: apiData.institutionSharesChangePercent ? `${apiData.institutionSharesChangePercent}%` : "0%"
				},
				totalInstitutionalShares: {
					value: apiData.totalMarginShares ? Number(apiData.totalMarginShares) : 0,
					changePercent: apiData.totalMarginSharesChangePercent ? `${apiData.totalMarginSharesChangePercent}%` : "0%"
				},
				totalProductShares: {
					value: apiData.avgSharesPerHolder ? Math.round(Number(apiData.avgSharesPerHolder)) : 0,
					change: apiData.avgSharesPerHolderChange ?
						(apiData.avgSharesPerHolderChange >= 0 ? `+${Math.round(apiData.avgSharesPerHolderChange)}` : `${Math.round(apiData.avgSharesPerHolderChange)}`) :
						"0"
				},
				topTenConcentration: {
					value: apiData.top10ShareholdingRatio ? `${apiData.top10ShareholdingRatio}%` : "0%",
					changePercent: apiData.top10RatioChange ? `${apiData.top10RatioChange}%` : "0%"
				},
				topTwentyConcentration: {
					value: apiData.top20ShareholdingRatio ? `${apiData.top20ShareholdingRatio}%` : "0%",
					changePercent: apiData.top20RatioChange ? `${apiData.top20RatioChange}%` : "0%"
				}
			}
		};
	}


	// 如果正在加载且没有数据，显示骨架屏
	// 修复条件判断逻辑：当正在加载且没有解析到数据时显示骨架屏
	if (isLoading && !parsedApiData) {
		return <CompanyOverviewSkeleton />;
	}

	// 如果有错误且没有数据，显示错误状态
	if (error) {
		return (
			<div className="w-full">
				{/* 修改记录 2025-06-18 15:59:48 - 调整错误状态背景渐变颜色，与正常状态保持一致 - hayden */}
				{/* 修改记录 2025-06-18 16:02:35 - 更新错误状态背景为纯净蓝色系渐变，与正常状态保持一致 - hayden */}
				<div className="w-full bg-gradient-to-br   from-slate-900 via-gray-800 to-slate-800 p-6 rounded-lg text-white shadow-lg relative overflow-hidden">
					<div className="relative z-10 flex items-center justify-center">
						<div className="flex items-center gap-2">
							<span className="text-lg">
								加载失败: {error.message}
							</span>
							<Button
								variant="outline"
								size="sm"
								className="text-white border-white/30 hover:bg-white/20"
								onClick={() => {
									handleRefreshAllData();
								}}
							>
								重试
							</Button>
						</div>
					</div>
				</div>
			</div>
		);
	}

	// 转换数据为渲染格式
	const data = transformApiDataToRenderData(parsedApiData, apiData?.timestamp);

	return (
		<div className="w-full">
			{/* 头部信息 - 数据分析风格渐变背景 */}
			{/* 修改记录 2025-06-18 15:59:48 - 调整背景渐变颜色，从深紫色改为柔和的蓝灰色调，更适合数据分析场景 - hayden */}
			{/* 修改记录 2025-06-18 16:02:35 - 移除灰色调，改用纯净蓝色系渐变，更契合数据分析场景 - hayden */}
			<div className="w-full bg-gradient-to-br from-slate-900 via-gray-800 to-slate-800 p-6 rounded-lg text-white shadow-lg relative overflow-hidden">
				{/* 装饰性背景元素 */}
				<div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/2 to-transparent" />
				<div className="absolute top-0 right-0 w-32 h-32 bg-white/3 rounded-full -translate-y-16 translate-x-16" />
				<div className="absolute bottom-0 left-0 w-24 h-24 bg-white/2 rounded-full translate-y-12 -translate-x-12" />

				{/* 内容区域 */}
				<div className="relative z-10">
					<div className="flex flex-col md:flex-row md:items-start md:justify-between">
						<div>
							<h2 className="text-2xl font-bold text-white">
								{data.companyName}
							</h2>
							<div className="mt-1 flex items-center gap-2">
								<span className="text-sm bg-white/8 px-2 py-1 rounded-md backdrop-blur-sm border border-white/10">
									{data.stockCode}
								</span>
								<span className="text-sm text-slate-300">
									分析范围: {data.analysisDateRange}
								</span>
							</div>
						</div>
					</div>

					{/* 生成时间单独放置，与股票代码和分析范围在同一水平线上 */}
					<div className="flex justify-end mt-[-24px] md:mt-[-20px]">
						<div className="flex items-center gap-1 text-sm">
							<span className="text-slate-300">
								生成时间: {data.generatedTime}
							</span>
							<Button
								variant="outline"
								size="icon"
								className="size-4 m-0 text-white border-white/20 hover:bg-white/10 hover:border-white/30 transition-all duration-200"
								onClick={handleRefreshAllData}
								disabled={isLoading}
							>
								<RefreshCcw
									className={cn(
										"size-4",
										isLoading && "animate-spin",
									)}
								/>
							</Button>
						</div>
					</div>
				</div>
			</div>
			<div>
				{/* 指标部分 - 响应式间距和网格 */}
				<div className="mt-4 sm:mt-6 mb-4 sm:mb-6 grid grid-cols-2 gap-1.5 sm:gap-2 md:grid-cols-3 lg:grid-cols-6">
					<MetricCard
						title="最新期数"
						value={data.metrics.latestPeriod.date}
					/>
					<MetricCard
						title="最新总户数"
						value={data.metrics.latestProduct.value}
						change={data.metrics.latestProduct.change}
					/>
					<MetricCard
						title="最新总股本 (万股)"
						value={data.metrics.latestTotalCapital.value}
						change={data.metrics.latestTotalCapital.changePercent}
					/>
					<MetricCard
						title="最新个人股东数"
						value={data.metrics.totalPersonalShareholders.value}
						change={data.metrics.totalPersonalShareholders.change}
					/>
					<MetricCard
						title="最新机构数"
						value={
							data.metrics.totalInstitutionalShareholders.value
						}
						change={
							data.metrics.totalInstitutionalShareholders.change
						}
					/>
					<MetricCard
						title="最新信用户数"
						value={data.metrics.totalShareholders.value}
						change={data.metrics.totalShareholders.change}
					/>
				</div>

				{/* 第二行指标 - 响应式间距和网格 */}
				<div className="mb-4 sm:mb-6 grid grid-cols-2 gap-1.5 sm:gap-2 md:grid-cols-3 lg:grid-cols-6">
					<MetricCard
						title="最新机构持股"
						value={data.metrics.totalPersonalShares.value.toLocaleString()}
						change={data.metrics.totalPersonalShares.changePercent}
					/>
					<MetricCard
						title="最新信用户持股"
						value={data.metrics.totalInstitutionalShares.value.toLocaleString()}
						change={
							data.metrics.totalInstitutionalShares.changePercent
						}
					/>
					<MetricCard
						title="最新户均持股"
						value={data.metrics.totalProductShares.value.toLocaleString()}
						change={data.metrics.totalProductShares.change}
					/>
					<MetricCard
						title="TOP10持股集中度"
						value={data.metrics.topTenConcentration.value}
						change={data.metrics.topTenConcentration.changePercent}
					/>
					<MetricCard
						title="TOP20持股集中度"
						value={data.metrics.topTwentyConcentration.value}
						change={
							data.metrics.topTwentyConcentration.changePercent
						}
					/>
					<MetricCard
						title="暂无数据"
						value="--"
						change="(API中无此字段)"
					/>
				</div>

				{/* 图表部分 - 响应式间距 */}
				{/* 修改记录 2025-06-30 10:12:56 - 减少图表容器底部内边距，解决底部空白过多问题 - hayden */}
				<div className="grid grid-cols-1 gap-4 sm:gap-2 md:grid-cols-3">
					<div className="rounded-lg border p-3 pb-2 sm:p-4 sm:pb-3">
						<ShareholdingDistributionChart
							controllingRatio={
								parsedApiData?.top1_shareholding_ratio || 0
							}
							top10Ratio={
								parsedApiData?.top10_shareholding_ratio || 0
							}
							top100Ratio={
								parsedApiData?.top100_shareholding_ratio || 0
							}
							top1ShareholdingAmount={
								parsedApiData?.top1_shareholding_amount || 0
							}
							top10ShareholdingAmount={
								parsedApiData?.top10_shareholding_amount || 0
							}
							top100ShareholdingAmount={
								parsedApiData?.top100_shareholding_amount || 0
							}
						/>
					</div>
					<div className="rounded-lg border p-3 pb-2 sm:p-4 sm:pb-3">
						<InstitutionalSharesDistributionChart
							shareholderTypes={parsedApiData?.shareholderTypes || []}
							mode="count"
						/>
					</div>
					<div className="rounded-lg border p-3 pb-2 sm:p-4 sm:pb-3">
						<InstitutionalSharesDistributionChart
							shareholderTypes={parsedApiData?.shareholderTypes || []}
							mode="shares"
						/>
					</div>
				</div>
			</div>
		</div>
	);
}