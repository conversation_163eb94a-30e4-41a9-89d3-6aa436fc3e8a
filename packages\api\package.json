{"dependencies": {"@repo/ai": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@scalar/hono-api-reference": "^0.5.175", "@sindresorhus/slugify": "^2.2.1", "axios": "^1.9.0", "hono": "^4.7.2", "hono-openapi": "^0.4.5", "jsonwebtoken": "^9.0.2", "nanoid": "^5.1.2", "openai": "^4.85.4", "openapi-merge": "^1.3.3", "use-intl": "^3.26.5", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/jsonwebtoken": "^9.0.9", "@types/react": "19.0.12", "encoding": "^0.1.13", "prisma": "^6.5.0", "typescript": "5.8.2"}, "main": "./index.ts", "name": "@repo/api", "scripts": {"type-check": "tsc --noEmit"}, "version": "0.0.0"}