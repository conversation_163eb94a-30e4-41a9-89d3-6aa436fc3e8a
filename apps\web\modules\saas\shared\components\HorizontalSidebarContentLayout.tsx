import type { ReactNode } from "react";

/**
 * 会议专用的水平侧边栏布局组件
 * 与原SidebarContentLayout不同，此组件让侧边栏始终保持水平布局
 */
export function HorizontalSidebarContentLayout({
	children,
	sidebar,
}: {
	children: React.ReactNode;
	sidebar: ReactNode;
}) {
	return (
		<div className="relative">
			<div className="flex flex-col items-start gap-4">
				{/* 侧边栏始终保持水平布局，不受屏幕尺寸影响 */}
				<div className="w-full top-4">
					{sidebar}
				</div>

				<div className="w-full flex-1">{children}</div>
			</div>
		</div>
	);
} 