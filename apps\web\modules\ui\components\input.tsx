import { cn } from "@ui/lib";
import React from "react";

export type InputProps = React.InputHTMLAttributes<HTMLInputElement>;

const Input = React.forwardRef<HTMLInputElement, InputProps>(
	({ className, type, ...props }, ref) => {
		// 检测是否为日期和时间类型
		const isDateTimeInput =
			type === "date" || type === "time" || type === "datetime-local";

		return (
			<input
				type={type}
				className={cn(
					"flex h-9 w-full rounded-lg border border-input px-3 py-1 text-base transition-colors file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-foreground/60 focus-visible:outline-hidden focus-visible:ring-1 focus-visible:border-ring focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
					// 隐藏日期时间输入框的原生图标
					isDateTimeInput && [
						"cursor-pointer",
						"relative",
						// 隐藏所有浏览器的日期时间选择器图标
						"[&::-webkit-calendar-picker-indicator]:opacity-0",
						"[&::-webkit-calendar-picker-indicator]:absolute",
						"[&::-webkit-calendar-picker-indicator]:inset-0",
						"[&::-webkit-calendar-picker-indicator]:w-full",
						"[&::-webkit-calendar-picker-indicator]:h-full",
						"[&::-webkit-calendar-picker-indicator]:cursor-pointer",
						"[&::-webkit-inner-spin-button]:appearance-none",
						"[&::-webkit-outer-spin-button]:appearance-none",
						// Firefox 兼容
						"[&::-moz-calendar-picker-indicator]:opacity-0",
						// 移除默认样式
						"appearance-none",
						// 确保整个区域可点击
						"z-[1]",
					],
					className,
				)}
				ref={ref}
				{...props}
			/>
		);
	},
);
Input.displayName = "Input";

export { Input };