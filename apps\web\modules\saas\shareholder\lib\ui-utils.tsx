import React from "react";
import { 
  Toolt<PERSON>, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger,
  TooltipPortal
} from "@ui/components/tooltip";
import { ChevronDown, ChevronUp, ArrowUpDown } from "lucide-react";

/**
 * 创建可排序表头的配置参数
 */
export interface SortableHeaderConfig {
  /**
   * 当前排序字段
   */
  sortBy: string;
  
  /**
   * 当前排序顺序 (asc 或 desc)
   */
  sortOrder: "asc" | "desc";
  
  /**
   * 排序变更处理函数
   */
  onSort: (column: string) => void;
}

/**
 * 获取排序图标
 * @param sortBy 当前排序的字段
 * @param sortOrder 当前排序顺序
 * @param column 当前列名称
 * @returns 排序图标组件
 */
export function getSortIcon(column: string, { sortBy, sortOrder }: SortableHeaderConfig) {
  if (sortBy !== column) {
    // 未选中状态显示默认的双向箭头
    return <ArrowUpDown className="h-3 w-3 text-slate-400" />;
  }
  // 选中状态根据排序方向显示向上或向下箭头
  return sortOrder === 'asc' ? 
    <ChevronUp className="h-4 w-4 text-primary" /> : 
    <ChevronDown className="h-4 w-4 text-primary" />;
}

/**
 * 处理排序点击
 * @param column 要排序的列
 * @param config 排序配置
 */
export function handleSortClick(column: string, { onSort }: SortableHeaderConfig) {
  onSort(column);
}

/**
 * 处理键盘事件
 * @param event 键盘事件
 * @param column 列名
 * @param config 排序配置
 */
export function handleKeyDown(
  event: React.KeyboardEvent, 
  column: string, 
  { onSort }: SortableHeaderConfig
) {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault();
    onSort(column);
  }
}

/**
 * 渲染带有Tooltip的截断文本
 * @param text 要显示的文本
 * @param maxLength 最大文本长度，超过则截断并显示tooltip
 * @returns 截断后的文本组件或原文本
 * 
 * @version 1.1.0 (2025-06-03 18:36:44) - 增强对undefined和null值的处理
 */
export function renderTruncatedWithTooltip(text: string | undefined | null, maxLength = 15) {
  // 如果文本为空、undefined或null，返回占位符
  if (text === undefined || text === null) {
    return "-";
  }
  
  if (text === "") {
    return "-";
  }
  
  // 确保text是字符串类型
  const textStr = String(text);
  
  // 使用更智能的判断：只有当文本实际超出maxLength时才截断
  // 移除了简单的长度比较，因为有些文本虽然字符数少但宽度大
  const shouldTruncate = textStr.length > maxLength;
  
  if (!shouldTruncate) {
    return textStr;
  }
  
  return (  
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="cursor-default truncate inline-block max-w-full">
            {textStr.slice(0, maxLength)}...
          </span>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent side="top" align="start" className="max-w-[300px]">
            {textStr}
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * 格式化数字显示
 * @param value 要格式化的数值
 * @param options 格式化选项
 * @returns 格式化后的字符串
 * 
 * @version 1.1.0 (2025-06-03 18:36:44) - 增加对undefined和null值的处理，避免在调用toFixed方法时出错
 */
export function formatNumber(
  value: string | number | undefined | null,
  options: {
    /**
     * 小数位数
     * @default 0
     */
    decimals?: number;
    
    /**
     * 是否添加千分位分隔符
     * @default true
     */
    useThousandsSeparator?: boolean;
    
    /**
     * 是否添加百分号
     * @default false
     */
    percentage?: boolean;
  } = {}
) {
  const {
    decimals = 0,
    useThousandsSeparator = true,
    percentage = false
  } = options;
  
  // 处理undefined和null值
  if (value === undefined || value === null) {
    return "-";
  }
  
  // 确保值是数字
  let numValue: number;
  
  if (typeof value === 'string') {
    numValue = Number.parseFloat(value);
    if (Number.isNaN(numValue)) {
      return value; // 如果无法转换为数字，返回原始值
    }
  } else {
    numValue = value as number;
  }
  
  // 处理NaN值
  if (Number.isNaN(numValue)) {
    return "-";
  }
  
  // 根据配置格式化
  if (useThousandsSeparator) {
    // 分别处理整数部分和小数部分
    const parts = numValue.toFixed(decimals).split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    
    return percentage 
      ? `${parts.join('.')}%` 
      : parts.join('.');
  }
  
  return percentage 
    ? `${numValue.toFixed(decimals)}%`
    : numValue.toFixed(decimals);
}

/**
 * 渲染可排序表头
 * @param column 列字段名
 * @param title 列显示标题
 * @param widthClass 列宽CSS类
 * @param sortConfig 排序配置
 * @param alignment 文本对齐方式
 * @param fontSizeClass 自定义字体大小CSS类
 * @param spacingClass 自定义间距CSS类
 * @returns 表头单元格JSX元素
 */
export function renderSortableHeader(
  column: string,
  title: string,
  widthClass: string,
  sortConfig: SortableHeaderConfig,
  alignment: "left" | "center" | "right" = "left",
  fontSizeClass?: string,
  spacingClass?: string
) {
  const alignClass = 
    alignment === "center" ? "text-center" : 
    alignment === "right" ? "text-right" : "text-left";
  
  const paddingClass = 
    alignment === "right" ? "pr-3" : 
    alignment === "left" ? "pl-3" : "";
  
  // 当前列是否为激活排序列
  const isActive = sortConfig.sortBy === column;
  
  // 使用传入的字体大小和间距，如果未提供则使用默认值
  const fontSize = fontSizeClass || "text-xs";
  const spacing = spacingClass || "py-2";
  
  return (
    <th className={`${widthClass} ${spacing} font-medium ${fontSize} tracking-wide ${isActive ? 'text-primary' : 'text-slate-600'} ${alignClass}`}>
      <button 
        type="button"
        onClick={() => handleSortClick(column, sortConfig)}
        onKeyDown={(e) => handleKeyDown(e, column, sortConfig)}
        className={`w-full h-full flex items-center gap-1 focus:outline-none focus:ring-2 focus:ring-primary/20 rounded-sm py-1 px-2 ${
          alignment === "right" ? "justify-end" : 
          alignment === "center" ? "justify-center" : "justify-start"
        }`}
        tabIndex={0}
        aria-label={`Sort by ${title}`}
        title={`Sort by ${title}`}
      >
        <span>{title}</span>
        {getSortIcon(column, sortConfig)}
      </button>
    </th>
  );
} 