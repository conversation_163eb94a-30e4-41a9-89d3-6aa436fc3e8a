#!/bin/bash

# =============================================================================
# 简单合并脚本：将dev分支合并到当前分支
# =============================================================================
# 创建时间: 2025-01-05
# 作者: Alson
# 版本: v1.0
#
# 📖 使用说明:
#   pnpm fdev                    # 将dev分支合并到当前分支
#   ./scripts/merge-from-dev.sh  # 直接执行脚本
#
# 🎯 使用场景:
#   • 功能开发中途同步dev分支的新代码
#   • 解决功能分支落后于dev分支的问题
#   • 在提交PR前确保代码是最新的
#   • 获取团队其他成员提交的最新功能
#
# 🔄 执行流程:
#   1. 检查工作区是否干净（必须先提交当前更改）
#   2. 获取当前分支名（如: feature/login）
#   3. 拉取远程dev分支最新代码
#   4. 合并dev分支到当前分支
#   5. 显示合并结果和提交历史
#
# ⚠️ 注意事项:
#   • 确保当前分支的更改已提交
#   • 如遇冲突需要手动解决
#   • 合并后仍在当前分支上（不会切换分支）
#   • 简单脚本，无备份和复杂回滚机制
#
# 🔥 冲突处理:
#   如果出现合并冲突：
#   1. 手动编辑冲突文件，解决冲突标记
#   2. git add <文件名>
#   3. git commit
#   或者放弃合并: git merge --abort
#
# 💡 典型工作流程:
#   git add . && git commit -m "保存进度"  # 提交当前更改
#   pnpm fdev                             # 同步dev最新代码
#   # 继续开发...
# =============================================================================

set -e  # 遇到错误立即退出

# 修改块开始: 权限检查和环境验证
# 修改范围: 添加权限检查和自动修复功能
# 修改时间: 2025-07-09
# 对应计划步骤: 1
# 恢复方法: 删除此修改块内所有代码，恢复上方注释中的原代码

# 权限检查和自动修复
check_and_fix_permissions() {
    local script_path="$0"
    
    # 检查脚本是否有执行权限
    if [ ! -x "$script_path" ]; then
        echo -e "${YELLOW}[WARNING]${NC} 脚本缺少执行权限，正在自动修复..."
        
        # 尝试修复权限
        if chmod +x "$script_path" 2>/dev/null; then
            echo -e "${GREEN}[SUCCESS]${NC} 权限修复完成"
        else
            echo -e "${RED}[ERROR]${NC} 无法修复脚本权限，请手动执行: chmod +x $script_path"
            exit 1
        fi
    fi
}

# 环境依赖检查
check_environment() {
    local missing_deps=()
    
    # 检查pnpm是否安装
    if ! command -v pnpm &> /dev/null; then
        missing_deps+=("pnpm")
    fi
    
    # 检查git是否安装
    if ! command -v git &> /dev/null; then
        missing_deps+=("git")
    fi
    
    # 检查是否在项目根目录（package.json存在且包含fdev脚本）
    if [ ! -f "package.json" ]; then
        echo -e "${RED}[ERROR]${NC} 未找到package.json文件，请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查package.json中是否包含fdev脚本配置
    if ! grep -q '"fdev"' package.json 2>/dev/null; then
        echo -e "${YELLOW}[WARNING]${NC} package.json中未找到fdev脚本配置"
        echo -e "${BLUE}[INFO]${NC} 请确保package.json中包含: \"fdev\": \"./scripts/merge-from-dev.sh\""
    fi
    
    # 报告缺失的依赖
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${RED}[ERROR]${NC} 缺少必要的依赖工具:"
        for dep in "${missing_deps[@]}"; do
            echo "  ❌ $dep"
        done
        echo
        echo -e "${BLUE}[INFO]${NC} 请安装缺失的工具后重试"
        exit 1
    fi
}

# 初始化检查
initialize_checks() {
    echo -e "${BLUE}[INFO]${NC} 🔍 正在进行环境检查..."
    check_and_fix_permissions
    check_environment
    echo -e "${GREEN}[SUCCESS]${NC} ✅ 环境检查通过"
    echo
}

# 修改块结束: 权限检查和环境验证
# 修改时间: 2025-07-09

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << 'EOF'
🔄 将dev分支合并到当前分支

用法: pnpm fdev

功能: 将dev分支的最新代码同步到当前功能分支
适用: 功能开发中途同步主分支代码

注意:
• 确保当前分支更改已提交
• 合并后仍在当前分支上
• 详细说明请查看脚本注释

EOF
}

# 参数处理
while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            log_info "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 原代码:
# log_info "🔄 开始将dev分支合并到当前分支..."
# 修改原因: 在主流程开始前添加初始化检查
# 修改时间: 2025-07-09
# 修改人: LLM
# 关联需求: 添加权限检查和环境验证功能
# 恢复方法: 删除当前代码，取消上方原代码的注释

# 执行初始化检查
initialize_checks

log_info "🔄 开始将dev分支合并到当前分支..."

# 1. 检查是否在Git仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    log_error "当前目录不是Git仓库"
    exit 1
fi

# 2. 检查工作区是否干净
if [[ -n $(git status --porcelain) ]]; then
    log_error "工作区有未提交的更改，请先提交或暂存："
    git status --short
    log_warning "提示：可以使用 'git add .' 和 'git commit -m \"message\"' 提交更改"
    exit 1
fi

# 3. 获取当前分支名
CURRENT_BRANCH=$(git branch --show-current)
log_info "当前分支: $CURRENT_BRANCH"

# 4. 检查是否已经在dev分支
if [[ "$CURRENT_BRANCH" == "dev" ]]; then
    log_warning "已经在dev分支上，无需合并"
    exit 0
fi

# 5. 检查dev分支是否存在
if ! git show-ref --verify --quiet refs/heads/dev; then
    log_error "dev分支不存在"
    exit 1
fi

# 6. 检查远程仓库连接
log_info "检查远程仓库连接..."
if ! git ls-remote origin > /dev/null 2>&1; then
    log_error "无法连接到远程仓库origin"
    exit 1
fi

# 7. 拉取最新的dev分支代码
log_info "拉取最新的dev分支代码..."
if ! git fetch origin dev:dev; then
    log_error "拉取dev分支失败"
    exit 1
fi

# 8. 合并dev分支到当前分支
log_info "合并dev分支到当前分支 $CURRENT_BRANCH..."
if ! git merge dev --no-ff -m "Merge dev into $CURRENT_BRANCH

Merged by: $(git config user.name) <$(git config user.email)>
Date: $(date)
Source: dev
Target: $CURRENT_BRANCH"; then
    # 检查是否是合并冲突
    if git status --porcelain | grep -q "^UU\|^AA\|^DD"; then
        log_error "🔥 检测到合并冲突！"
        echo
        log_info "冲突文件列表:"
        git status --porcelain | grep "^UU\|^AA\|^DD" | while read -r line; do
            echo "  📄 ${line:3}"
        done
        echo
        log_warning "请按以下步骤解决冲突:"
        echo "  1️⃣  手动编辑冲突文件，解决冲突标记"
        echo "  2️⃣  添加已解决的文件: git add <文件名>"
        echo "  3️⃣  完成合并: git commit"
        echo
        log_info "或者放弃此次合并:"
        echo "  ❌ 中止合并: git merge --abort"
        exit 1
    else
        log_error "合并失败，原因未知"
        exit 1
    fi
fi

# 9. 成功完成
echo
log_success "🎉 成功将dev分支合并到 $CURRENT_BRANCH 分支！"
echo
log_info "📊 操作摘要："
echo "  ✅ 源分支: dev"
echo "  ✅ 目标分支: $CURRENT_BRANCH"
echo "  ✅ 当前位置: $CURRENT_BRANCH 分支"

echo
log_info "📈 最近的提交记录："
git log --oneline --graph -5

echo
log_success "✨ 合并操作完成！现在可以继续在当前分支开发"
