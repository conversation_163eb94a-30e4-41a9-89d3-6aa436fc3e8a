import { db } from "@repo/database";
import { logger } from "@repo/logs";

// 定义可用的角色
const AVAILABLE_ROLES = ["member", "admin", "owner"] as const;
type MemberRole = typeof AVAILABLE_ROLES[number];

async function main() {
	logger.info("👥 Update Member Role - 修改组织成员权限");
	logger.info("此脚本将修改指定组织成员的角色权限");

	// 获取用户标识 (支持邮箱或ID)
	const userIdentifier = await logger.prompt("请输入要修改权限的用户邮箱或ID:", {
		required: true,
		placeholder: "<EMAIL> 或 user_id_123",
		type: "text",
	});

	// 查找用户
	logger.info("🔍 正在查找用户...");
	
	const user = await db.user.findFirst({
		where: {
			OR: [
				{ email: userIdentifier },
				{ id: userIdentifier },
			],
		},
		include: {
			memberships: {
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
				},
			},
		},
	});

	if (!user) {
		logger.error("❌ 未找到指定的用户！");
		return;
	}

	// 显示用户信息
	logger.info("📋 找到用户信息:");
	logger.info(`   ID: ${user.id}`);
	logger.info(`   姓名: ${user.name}`);
	logger.info(`   邮箱: ${user.email}`);

	// 显示用户的组织成员身份
	if (user.memberships.length === 0) {
		logger.warn("⚠️  该用户不是任何组织的成员！");
		return;
	}

	logger.info("\n🏢 用户的组织成员身份:");
	user.memberships.forEach((membership, index) => {
		logger.info(`   ${index + 1}. ${membership.organization.name} (${membership.organization.slug || membership.organization.id})`);
		logger.info(`      当前角色: ${membership.role}`);
		logger.info(`      成员ID: ${membership.id}`);
	});

	// 选择要修改的组织
	let selectedMembership;
	if (user.memberships.length === 1) {
		selectedMembership = user.memberships[0];
		logger.info(`\n✅ 自动选择唯一的组织: ${selectedMembership.organization.name}`);
	} else {
		const organizationChoice = await logger.prompt(
			"请选择要修改权限的组织 (输入序号):",
			{
				required: true,
				type: "text",
				placeholder: "1",
			}
		);

		const choiceIndex = parseInt(organizationChoice) - 1;
		if (isNaN(choiceIndex) || choiceIndex < 0 || choiceIndex >= user.memberships.length) {
			logger.error("❌ 无效的选择！");
			return;
		}

		selectedMembership = user.memberships[choiceIndex];
	}

	// 显示当前角色信息
	logger.info("\n📊 当前成员信息:");
	logger.info(`   组织: ${selectedMembership.organization.name}`);
	logger.info(`   用户: ${user.name} (${user.email})`);
	logger.info(`   当前角色: ${selectedMembership.role}`);
	logger.info(`   加入时间: ${selectedMembership.createdAt.toLocaleString()}`);

	// 显示可用角色
	logger.info("\n🎭 可用角色:");
	AVAILABLE_ROLES.forEach((role, index) => {
		const description = {
			member: "普通成员 - 基础权限",
			admin: "管理员 - 管理权限",
			owner: "所有者 - 完全权限"
		}[role];
		const current = role === selectedMembership.role ? " (当前)" : "";
		logger.info(`   ${index + 1}. ${role} - ${description}${current}`);
	});

	// 选择新角色
	const roleChoice = await logger.prompt("请选择新角色 (输入序号):", {
		required: true,
		type: "text",
		placeholder: "1",
	});

	const roleIndex = parseInt(roleChoice) - 1;
	if (isNaN(roleIndex) || roleIndex < 0 || roleIndex >= AVAILABLE_ROLES.length) {
		logger.error("❌ 无效的角色选择！");
		return;
	}

	const newRole = AVAILABLE_ROLES[roleIndex];

	// 检查是否与当前角色相同
	if (newRole === selectedMembership.role) {
		logger.warn("⚠️  选择的角色与当前角色相同，无需修改！");
		return;
	}

	// 特殊检查：如果要设置为owner，需要额外确认
	if (newRole === "owner") {
		logger.warn("⚠️  警告：owner角色拥有组织的完全控制权限！");
		
		const confirmOwner = await logger.prompt(
			"确认要将此用户设置为组织所有者吗？",
			{
				required: true,
				type: "confirm",
				default: false,
			}
		);

		if (!confirmOwner) {
			logger.info("✅ 操作已取消。");
			return;
		}
	}

	// 最终确认
	const confirmUpdate = await logger.prompt(
		`❓ 确认将 ${user.name} 在 ${selectedMembership.organization.name} 中的角色从 ${selectedMembership.role} 修改为 ${newRole} 吗？`,
		{
			required: true,
			type: "confirm",
			default: false,
		}
	);

	if (!confirmUpdate) {
		logger.info("✅ 操作已取消，成员角色未被修改。");
		return;
	}

	// 执行角色更新
	logger.info("🔄 正在更新成员角色...");

	try {
		const updatedMember = await db.member.update({
			where: {
				id: selectedMembership.id,
			},
			data: {
				role: newRole,
			},
			include: {
				user: {
					select: {
						name: true,
						email: true,
					},
				},
				organization: {
					select: {
						name: true,
					},
				},
			},
		});

		logger.success("✅ 成员角色更新成功！");
		logger.info("📋 更新操作摘要:");
		logger.info(`   - 组织: ${updatedMember.organization.name}`);
		logger.info(`   - 用户: ${updatedMember.user.name} (${updatedMember.user.email})`);
		logger.info(`   - 原角色: ${selectedMembership.role}`);
		logger.info(`   - 新角色: ${updatedMember.role}`);
		logger.info(`   - 更新时间: ${new Date().toLocaleString()}`);

	} catch (error) {
		logger.error("❌ 更新成员角色时发生错误:");
		logger.error(error);
		logger.error("角色未被修改，请检查错误信息后重试。");
	}
}

main().catch((error) => {
	logger.error("脚本执行失败:");
	logger.error(error);
	process.exit(1);
});
