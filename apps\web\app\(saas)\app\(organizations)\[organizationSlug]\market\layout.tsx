import { getSession } from "@saas/auth/lib/server"; // 用于获取用户会话信息
import { redirect } from "next/navigation"; // 页面重定向功能



/**
 * 市值管理模块的布局组件
 * 简化布局，直接显示内容
 */
export default async function MarketValueLayout({
	children
}: { children: React.ReactNode }) {
	const session = await getSession(); // 获取用户会话信息

	// 如果用户未登录，重定向到登录页面
	if (!session) {
		return redirect("/auth/login");
	}

	// 直接渲染子组件内容，不需要导航
	return <>{children}</>;
}