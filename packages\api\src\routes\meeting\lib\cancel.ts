import { operatorId, meetingApiClient } from "./config";

/* 修改块开始: 支持传入用户ID取消会议
 * 修改范围: cancelMeeting handler函数
 * 对应需求: 使用真实用户ID而非固定operatorId取消会议
 * 恢复方法: 删除userId参数处理，恢复使用固定operatorId
 */
/**
 * 取消会议API
 */
export const cancelMeeting = {
	method: "POST",
	path: "/meetings/cancel/:id",
	handler: async (meetingId: string, reasonCode = 1, userId?: string) => {
		try {
			// 使用传入的用户ID，如果没有则使用默认的operatorId
			const targetUserId = userId || operatorId;
			
			const response = await meetingApiClient.post(`/v1/meetings/${meetingId}/cancel`, {
				userid: targetUserId,
				instanceid: 1,
				reason_code: reasonCode
			});

			// console.log("Meeting canceled:", response.data);
			return response.data;
		} catch (error: any) {
			// console.error("Error canceling meeting:", error);
			
			// 增强错误处理，提供更详细的错误信息
			if (error.response) {
				// API返回了错误响应
				const errorData = error.response.data;
				const errorMessage = errorData?.error_info?.message || 
								   errorData?.message || 
								   `取消会议失败 (状态码: ${error.response.status})`;
				throw new Error(errorMessage);
			}
			
			if (error.request) {
				// 请求发送失败
				throw new Error("网络连接失败，请检查网络连接后重试");
			}
			
			// 其他错误
			throw new Error(error.message || "取消会议时发生未知错误");
		}
	},
};
