# 股东名册股东类型字段实施方案

## 版本信息
| 版本 | 日期       | 作者   | 修订内容描述                                                                 |
|------|------------|--------|------------------------------------------------------------------------------|
| V.1  | 2025-06-24 | hayden | 初版：股东表添加股东类型字段的完整实施方案                                    |

## 1. 概述

### 1.1 背景
基于已有的股东归类数据库API方案和股东类型匹配PRD，需要在股东名册系统中添加股东类型字段，用于存储前端上传归类好的股东类型数据。

### 1.2 核心目标
1. 在Shareholder表中添加股东类型字段
2. 更新相关的API接口、验证器和字段映射器
3. 确保股东类型数据能够正确存储和查询
4. 保持与现有股东归类系统的兼容性

### 1.3 技术架构
- 数据库：基于现有Prisma Schema扩展
- API：基于Hono框架，遵循项目现有API规范
- 验证：基于Zod验证器扩展
- 字段映射：扩展现有字段映射器

## 2. 数据库设计变更

### 2.1 Shareholder表字段扩展

需要在现有的Shareholder模型中添加股东类型字段：

```prisma
model Shareholder {
  // ... 现有字段保持不变 ...

  // 新增股东类型字段 - 2025-06-24 12:06:51 hayden 添加
  shareholderType       String?             // 股东类型（如"知名牛散"、"境内个人"等）

  // ... 现有索引和映射保持不变 ...
  @@index([shareholderType]) // 股东类型索引
}
```

### 2.2 字段说明

| 字段名 | 类型 | 描述 | 说明 |
|--------|------|------|------|
| shareholderType | String? | 股东类型名称 | 如"知名牛散"、"境内个人"、"QFII&RQFII"等，可选字段 |

## 3. 涉及文件结构和修改点

### 3.1 数据库相关文件

#### 3.1.1 Schema定义
- **文件路径**: `packages/database/prisma/schema.prisma`
- **修改内容**: 在Shareholder模型中添加股东类型相关字段
- **修改类型**: 字段扩展

#### 3.1.2 Zod类型定义
- **文件路径**: `packages/database/src/zod/index.ts`
- **修改内容**:
  - 更新ShareholderSchema，添加shareholderType字段的验证规则
  - 更新ShareholderScalarFieldEnumSchema，添加shareholderType字段枚举
- **修改类型**: 类型定义扩展

### 3.2 API接口相关文件

#### 3.2.1 上传接口
- **文件路径**: `packages/api/src/routes/shareholder-registry/upload.ts`
- **修改内容**: 在股东数据处理流程中集成股东类型分类逻辑
- **修改类型**: 业务逻辑扩展

#### 3.2.2 字段验证器
- **文件路径**: `packages/api/src/routes/shareholder-registry/lib/validators.ts`
- **修改内容**: 
  - 在UploadRegistrySchema中添加股东类型字段验证
  - 在各类型名册的验证模式中添加股东类型字段支持
- **修改类型**: 验证规则扩展

#### 3.2.3 字段映射器
- **文件路径**: `packages/api/src/routes/shareholder-registry/lib/field-mapping.ts`
- **修改内容**: 
  - 在各类型名册的字段映射中添加股东类型字段映射
  - 更新mapShareholderFields函数，支持股东类型字段转换
- **修改类型**: 字段映射扩展

#### 3.2.4 类型定义
- **文件路径**: `packages/api/src/routes/shareholder-registry/types.ts`
- **修改内容**:
  - 在UploadRegistryRequest接口中添加股东类型字段
  - 在ShareholderData接口中添加shareholderType字段
- **修改类型**: 接口定义扩展

### 3.3 处理器文件

#### 3.3.1 各类型名册处理器
- **文件路径**: 
  - `packages/api/src/routes/shareholder-registry/handlers/upload-01.ts`
  - `packages/api/src/routes/shareholder-registry/handlers/upload-05.ts`
  - `packages/api/src/routes/shareholder-registry/handlers/upload-t1.ts`
  - `packages/api/src/routes/shareholder-registry/handlers/upload-t2.ts`
  - `packages/api/src/routes/shareholder-registry/handlers/upload-t3.ts`
- **修改内容**: 在股东数据入库前添加股东类型分类逻辑
- **修改类型**: 业务逻辑扩展

#### 3.3.2 合并处理器
- **文件路径**: 
  - `packages/api/src/routes/shareholder-registry/handlers/merge-01-05.ts`
  - `packages/api/src/routes/shareholder-registry/handlers/merge-t1-t2-t3.ts`
- **修改内容**: 在合并处理流程中添加股东类型分类逻辑
- **修改类型**: 业务逻辑扩展

### 3.4 前端类型定义文件

#### 3.4.1 股东数据类型
- **文件路径**: `apps/web/modules/saas/shareholder/lib/types.ts`
- **修改内容**:
  - 在ShareholderItem接口中添加shareholderType字段
  - 更新相关响应类型定义
- **修改类型**: 接口定义扩展

#### 3.4.2 组件类型定义
- **文件路径**: `apps/web/modules/saas/shareholder/components/ant-shareholder-table.tsx`
- **修改内容**: 在ShareholderItem接口中添加股东类型字段支持
- **修改类型**: 组件接口扩展

## 4. 实施步骤

### 4.1 数据库变更阶段
1. **修改schema.prisma文件**
   - 在Shareholder模型中添加股东类型相关字段
   - 添加相应的索引定义

2. **应用数据库变更**
   ```powershell
   pnpm --filter database push
   ```

3. **重新生成Prisma客户端**
   ```powershell
   pnpm --filter database generate
   ```

### 4.2 类型定义更新阶段
1. **更新Zod类型定义**
   - 修改`packages/database/src/zod/index.ts`
   - 添加shareholderType字段的验证规则和枚举

2. **更新API类型定义**
   - 修改`packages/api/src/routes/shareholder-registry/types.ts`
   - 添加shareholderType字段接口定义

3. **更新前端类型定义**
   - 修改`apps/web/modules/saas/shareholder/lib/types.ts`
   - 添加shareholderType字段支持

### 4.3 验证器和映射器更新阶段
1. **更新字段验证器**
   - 修改`packages/api/src/routes/shareholder-registry/lib/validators.ts`
   - 添加股东类型字段验证规则

2. **更新字段映射器**
   - 修改`packages/api/src/routes/shareholder-registry/lib/field-mapping.ts`
   - 添加股东类型字段映射支持

### 4.4 业务逻辑集成阶段
1. **更新上传接口**
   - 修改`packages/api/src/routes/shareholder-registry/upload.ts`
   - 集成股东类型分类逻辑

2. **更新各类型处理器**
   - 修改所有名册类型的处理器文件
   - 添加股东类型分类和存储逻辑

3. **更新合并处理器**
   - 修改合并处理器文件
   - 确保合并流程中的股东类型处理

