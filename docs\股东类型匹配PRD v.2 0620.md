# 股东类型匹配 PRD v.2

## 版本信息
| 版本 | 日期       | 修订内容描述                                                                 |
|------|------------|------------------------------------------------------------------------------|
| V.2  | 2025-06-20 | 分类逻辑基于“股东分类规则表”，按优先级分类                                   |

## 背景
### 需求来源
为实现对股东的深度分析，需建立一套统一、灵活且可维护的智能分类机制。

### 核心目标
建立一个基于单一优先级规则的股东分类引擎。该逻辑在用户上传股东名册时执行，确保所有后续分析模块使用的股东类型准确且一致。

---

## 一、核心分类逻辑
### 执行时机
此分类逻辑在用户于前端上传股东名册文件，且该文件成功通过格式、字段及数据有效性验证后，在最终名册上传数据库之前，被触发并执行。

### 数据源
股东分类过程完全由一个核心的 “股东分类规则表” 驱动。该表包含以下核心字段：
- **优先级 (Priority)**：整数 (1 - 20)，1 为最高优先级。
- **类型 (Type)**：文本，即股东匹配成功后将被赋予的分类名称（如：“国家队”、“公募基金”、“境内个人”）。
- **匹配规则 (MatchRule)**：文本/JSON，定义了匹配的具体条件。规则可以是 “关键字匹配” 或 “持有人类别代码(CYRLBMS)匹配” 等形式。

### 执行逻辑
1. 系统处理每一位待分类的股东时，会按优先级从高到低（从 1 到 20）的顺序，逐条遍历“股东分类规则表”。
2. 对于表中的每一条规则，系统会根据其匹配规则来检查当前股东的信息（如“股东名称”中的关键字是否包含或“CYRLBMS 代码”）。
3. 一旦找到第一个满足匹配规则的条目，系统立即将该条规则的类型赋值给该股东。
4. 该股东的分类流程立即结束，不再检查任何后续的低优先级规则。

### 最终回退规则
- 优先级最低（如优先级 = 20）的规则应被设定为一个“回退项”，其匹配规则能确保所有未被前面规则命中的股东都能匹配成功。
- **示例**：
  - 优先级: 20
  - 类型: "其他机构"
- **作用**：此设计保证了所有股东最终都能获得一个确切的分类。

### 流程图
graph TD
    A([开始 处理单个股东]) --> B[读取股东名称及相关信息];

    B -->  E[ 按优先级 从优先级1<br>开始逐个遍历];

    E --> G{当前规则是否匹配};
    G -- 是 --> H([赋值shareholder_type<br>为当前规则的分类名称]);

    G -- 否 --> J[获取下一条优先级规则<br>重复流程];


---

## 二、数据存储
在股东数据中新增一个字段 `shareholder_type`，用于存储本流程最终确定的分类类型。

---

## 三、处理流程
1. **初始化**：系统读取前端上传的已经通过验证的股东名册。
2. **逐条分类**：系统遍历每一位股东记录，并为每位股东执行以下 “规则匹配流程”：
   - **排序遍历**：按优先级 (1 -> 20) 的顺序，从“股东分类规则表”中取出规则。
   - **规则判断**：根据取出的匹配规则，判断当前股东是否符合条件。
   - **匹配成功**：
     - 若符合条件，则将该规则的类型赋值给股东的 `shareholder_type` 字段。
     - 立即终止对该股东的分类，跳出规则遍历，开始处理下一位股东。
   - **匹配失败**：若不符合条件，则继续用下一条低优先级规则进行判断。
3. **完成**：所有股东记录都获得 `shareholder_type` 赋值后，分类流程结束。

---

## 四、验收标准
- **优先级至上**：任何股东的最终分类，必须是其能匹配到的优先级最高的规则所定义的类型。
- **规则有效性**：“股东分类规则表”中的每一条匹配规则必须能准确识别目标股东，无错配或漏配。
- **最终回退**：所有未能匹配优先级 1 到 19 规则的股东，必须被正确归类为优先级 20 规则所定义的类型。
- **100% 覆盖**：系统处理完成后，每一位股东都必须有且只有一个 `shareholder_type`。

---