import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";
import { UploadRegistrySchema, type UploadRegistryRequest, type UploadRegistryResponse } from "./types";
import { HTTPException } from "hono/http-exception";
import { db } from "@repo/database";
import {
	successResponse,
	handleHttpException,
	detectRegistryTypeByFileName,
  shouldUseMergeHandler,
	isT3FullRegistry,
	type RegistryType
} from "./lib/utils";
import { detectRegistryTypeByFields, validateShareholderFields } from "./lib/field-mapping";

// 导入各类型名册的处理器
import { upload01Handler } from "./handlers/upload-01";
import { upload05Handler } from "./handlers/upload-05";
import { uploadT1Handler } from "./handlers/upload-t1";
import { uploadT2Handler } from "./handlers/upload-t2";
import { uploadT3Handler } from "./handlers/upload-t3";
import { merge0105Handler } from "./handlers/merge-01-05";
import { mergeT1T2T3Handler } from "./handlers/merge-t1-t2-t3";



/**
 * 股东名册上传路由
 *
 * 功能：上传并处理股东名册数据，包括公司信息和股东列表
 * 路径：/upload
 * 方法：POST
 * 中间件：
 * - authMiddleware: 验证用户身份和权限
 * - shareholderCryptoMiddleware: 处理请求和响应的加解密
 *
 * 业务逻辑：
 * 1. 验证请求数据格式
 * 2. 检查recordCount与shareholders数组长度是否匹配
 * 3. 使用双重验证机制确定名册类型
 * 4. 根据名册类型分发到对应的处理器
 * 5. 返回处理结果
 *
 * 优化说明：
 * 1. 采用模块化设计，将不同名册类型的处理逻辑分离
 * 2. 使用双重验证机制确定名册类型，提高准确性
 * 3. 统一的错误处理和响应格式
 * 4. 支持深市01/05名册和沪市t1/t2/t3名册的合并处理
 *
 * 更新记录：
 * - 2025-07-01 10:20:39 hayden 修改：优化参数验证错误信息，显示具体的验证失败字段和原因
 *
 * <AUTHOR>
 * @time 2025-07-01 10:20:39
 */
export const uploadRouter = new Hono().post(
  "/upload",
  authMiddleware,                 // 验证用户身份和权限
  shareholderCryptoMiddleware(),  // 处理请求和响应的加解密
  async (c) => {
    try {
      // 从上下文中获取解密后的请求数据
      const requestData = c.get("requestData") as UploadRegistryRequest;
      // console.log('requestData:', requestData.shareholders[0]);
     
      const userId = c.get("user").id;
      // 验证请求数据
      const validationResult = UploadRegistrySchema.safeParse(requestData);
      if (!validationResult.success) {
        // 提取具体的验证错误信息
        const errorMessages = validationResult.error.issues.map(issue => {
          const fieldPath = issue.path.length > 0 ? issue.path.join('.') : '未知字段';
          return `${fieldPath}: ${issue.message}`;
        }).join('; ');

        throw new HTTPException(400, {
          message: `请求参数验证失败: ${errorMessages}`,
          cause: validationResult.error
        });
      }
      
      const data = validationResult.data;
      // console.log("data:", data.shareholders[0]);

      // 验证recordCount与shareholders数组长度是否匹配
      if (data.recordCount !== data.shareholders.length) {
        throw new HTTPException(400, {
          message: `股东记录数量不匹配: 申报的股东数量(${data.recordCount})与实际提供的股东数据数量(${data.shareholders.length})不一致`
        });
      }

      // 双重验证机制确定名册类型
      const fieldType = detectRegistryTypeByFields(data.shareholders);
      const fileNameType = detectRegistryTypeByFileName(data.fileName);

      /**
       * 严格的双重验证机制
       *
       * 更新记录:
       * - 2025-06-19 18:57:58: hayden 修复双重验证机制，确保严格校验名册类型
       *   只支持01、05、t1、t2、t3类型，fileNameType是最后的关卡，不能放过
       *
       * 验证逻辑:
       * 1. 如果文件名检测失败，直接拒绝
       * 2. 如果字段检测成功且与文件名检测一致，通过验证
       * 3. 如果字段检测失败但文件名检测成功，使用文件名检测结果
       * 4. 如果两者都成功但不一致，提示类型不匹配错误
       */

      // 首先检查文件名类型，这是最后的关卡
      if (fileNameType === "unknown") {
        throw new HTTPException(400, {
          message: "无法从文件名确定名册类型，请确保文件名包含正确的类型标识（DQMC01、DQMC05、t1、t2、t3）"
        });
      }

      let registryType: RegistryType;

      // 如果字段检测成功
      if (fieldType !== "unknown") {
        // 检查字段类型与文件名类型是否一致
        if (fieldType === fileNameType) {
          registryType = fieldType;
        } else {
          throw new HTTPException(400, {
            message: `名册类型不匹配：文件名表明是${fileNameType}类型，但数据字段特征表明是${fieldType}类型，请检查文件内容和文件名是否正确`
          });
        }
      } else {
        // 字段检测失败，使用文件名检测结果，但给出警告
        registryType = fileNameType;
      }
      
      // 验证股东数据是否符合检测到的名册类型要求
      const fieldsValidationResult = validateShareholderFields(data.shareholders, registryType);
      if (!fieldsValidationResult.isValid) {
        throw new HTTPException(400, { 
          message: `上传的股东数据缺少${registryType}类型名册必要字段: ${fieldsValidationResult.missingFields.join(", ")}` 
        });
      }

      // 查询是否存在同日期名册，以决定是否需要使用合并处理器
      const existingRegistry = await db.shareholderRegistry.findFirst({
        where: {
          organizationId: data.organizationId,
          companyCode: data.companyCode,
          registerDate: new Date(data.registerDate),
        },
        select: { id: true, fileName: true },
      });

      // 判断是否需要使用合并处理器
      const { needMerge, mergeType } = shouldUseMergeHandler(existingRegistry, registryType);
      // 根据名册类型和合并需求选择处理器
      let result: any;
      
      if (needMerge) {
        // 使用合并处理器
        switch (mergeType) {
          case "01-05":
            result = await merge0105Handler(data, userId);
            break;
          case "t1-t2-t3":
            result = await mergeT1T2T3Handler(data, userId);
            break;
          default:
            throw new HTTPException(400, { message: "不支持的合并类型" });
        }
      } else {
        // 检查是否已存在同类型名册
        if (existingRegistry) {
          // 文件名可能包含多个名册，按换行符分割
          const existingFileNames = existingRegistry.fileName.toLowerCase().split('\n');
          
          /**
           * 检查重复上传
           *
           * @param fileNames 现有文件名数组
           * @param type 名册类型关键字
           * @returns 是否存在重复
           *
           * 更新记录:
           * - 2025-06-13 14:03:23 hayden 修改：支持T3全量名册替换T3前200版本
           *
           * @since 2025-06-04 16:42:49
           */
          const hasDuplicateType = (fileNames: string[], type: string): boolean => {
            return fileNames.some(name => {
              if (type === '01') {
                return name.includes('dqmc01');
              }
              if (type === '05') {
                return name.includes('dqmc05');
              }

              // 2025-06-13 14:03:23 hayden 修改：T3类型特殊处理
              // 如果当前上传的是T3全量名册，允许替换现有的T3前200版本
              if (type === 't3') {
                const isCurrentFullRegistry = isT3FullRegistry(data.fileName);

                // 如果当前上传的是T3全量名册，不检查重复（允许替换）
                if (isCurrentFullRegistry) {
                  return false;
                }

                // 如果当前上传的不是T3全量名册，正常检查重复
                return name.startsWith(type.toLowerCase());
              }

              return name.startsWith(type.toLowerCase());
            });
          };
          
          // 如果存在同类型名册，抛出明确的错误
          if (hasDuplicateType(existingFileNames, registryType)) {
            throw new HTTPException(400, { 
              message: `DUPLICATE_REGISTRY_TYPE:已存在同一登记日期的${registryType}类型名册，不允许重复上传` 
            });
          }
        }
        
        // 使用单独的处理器
        switch (registryType) {
          case "01":
            result = await upload01Handler(data, userId);
            break;
          case "05":
            result = await upload05Handler(data, userId);
            break;
          case "t1":
            result = await uploadT1Handler(data, userId);
            break;
          case "t2":
            result = await uploadT2Handler(data, userId);
            break;
          case "t3":
            result = await uploadT3Handler(data, userId);
            break;
          default:
            throw new HTTPException(400, { message: "不支持的名册类型" });
        }
      }
      
      // 检查处理结果
      if (!result) {
        throw new HTTPException(500, { message: "处理股东名册数据失败" });
      }
      
      // 构建响应数据
      const responseData: UploadRegistryResponse = {
        id: result.id,
        fileName: result.fileName,
        recordCount: result.recordCount,
        registerDate: result.registerDate.toISOString().split('T')[0],
        uploadedAt: result.uploadedAt.toISOString()
      };
      
      // 返回成功响应
      successResponse(c, "股东名册上传成功", responseData);
      return;
    } catch (error) {
      // console.log('error:', error);
      // 移除简单的控制台日志，错误详情记录由handleHttpException负责
      // 更新时间: 2025-06-12 15:48:05
      handleHttpException(c, error);
      return;
    }
  }
); 

