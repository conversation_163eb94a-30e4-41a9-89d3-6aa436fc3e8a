"use client";

import { authClient } from "@repo/auth/client";
import {
	adminOrganizationsQueryKey,
	useAdminOrganizationsQuery,
} from "@saas/admin/lib/api";
import { getAdminPath } from "@saas/admin/lib/links";
import { OrganizationLogo } from "@saas/organizations/components/OrganizationLogo";
import { useConfirmationAlert } from "@saas/shared/components/ConfirmationAlertProvider";
import { Pagination } from "@saas/shared/components/Pagination";
import { Spinner } from "@shared/components/Spinner";
import { useQueryClient } from "@tanstack/react-query";
import type { ColumnDef } from "@tanstack/react-table";
import {
	flexRender,
	getCoreRowModel,
	getPaginationRowModel,
	useReactTable,
} from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Input } from "@ui/components/input";
import { Table, TableBody, TableCell, TableRow } from "@ui/components/table";
import { EditIcon, MoreVerticalIcon, PlusIcon, TrashIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { parseAsInteger, parseAsString, useQueryState } from "nuqs";
import { useEffect, useMemo } from "react";
import { toast } from "sonner";
import { withQuery } from "ufo";
import { useDebounceValue } from "usehooks-ts";

/**
 * @constant ITEMS_PER_PAGE
 * @description 每页显示的组织数量
 */
const ITEMS_PER_PAGE = 10;

/**
 * @component OrganizationList
 * @description 管理员后台的组织列表组件。
 * 负责展示组织列表、提供搜索、分页、编辑和删除功能。
 * 使用 react-table 进行表格渲染，react-query 进行数据获取和缓存，
 * nuqs 管理 URL 状态，shadcn/ui 和 lucide-react 提供 UI 元素。
 */
export function OrganizationList() {
	/**
	 * @hook useTranslations
	 * @description 获取 next-intl 提供的国际化翻译函数。
	 */
	const t = useTranslations();
	/**
	 * @hook useConfirmationAlert
	 * @description 获取确认弹窗的上下文钩子，用于触发删除前的确认对话框。
	 */
	const { confirm } = useConfirmationAlert();
	/**
	 * @hook useQueryClient
	 * @description 获取 react-query 的查询客户端实例，用于手动使缓存失效。
	 */
	const queryClient = useQueryClient();
	/**
	 * @hook useQueryState
	 * @description 使用 nuqs 管理 URL search params 中的 `currentPage` 状态。
	 * 解析为整数，默认值为 1。
	 */
	const [currentPage, setCurrentPage] = useQueryState(
		"currentPage",
		parseAsInteger.withDefault(1),
	);
	/**
	 * @hook useQueryState
	 * @description 使用 nuqs 管理 URL search params 中的 `query` (搜索词) 状态。
	 * 解析为字符串，默认值为空字符串。
	 */
	const [searchTerm, setSearchTerm] = useQueryState(
		"query",
		parseAsString.withDefault(""),
	);
	/**
	 * @hook useDebounceValue
	 * @description 对搜索词 `searchTerm` 进行防抖处理，延迟 300ms 更新。
	 * `leading: true` 表示在延迟开始时触发一次更新。
	 * `trailing: false` 表示在延迟结束时不触发更新。
	 * 用于避免在用户快速输入时频繁触发 API 请求。
	 */
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useDebounceValue(
		searchTerm,
		300,
		{
			leading: true,
			trailing: false,
		},
	);

	/**
	 * @function getPathWithBackToParemeter
	 * @description 生成带有 `backTo` 查询参数的路径。
	 * 该参数用于在编辑或创建操作后返回到当前列表页面，并保留之前的分页和搜索状态。
	 * @param {string} path - 基础路径
	 * @returns {string} - 带有 `backTo` 参数的完整路径
	 */
	const getPathWithBackToParemeter = (path: string) => {
		// 获取当前的 URL 查询参数
		const searchParams = new URLSearchParams(window.location.search);
		// 使用 ufo 的 withQuery 函数将 `backTo` 参数添加到路径中
		// `backTo` 的值是当前页面的路径和查询参数
		return withQuery(path, {
			backTo: `${window.location.pathname}${searchParams.size ? `?${searchParams.toString()}` : ""}`,
		});
	};

	/**
	 * @function getOrganizationEditPath
	 * @description 获取特定组织编辑页面的路径，并附带 `backTo` 参数。
	 * @param {string} id - 组织 ID
	 * @returns {string} - 组织编辑页面的完整路径
	 */
	const getOrganizationEditPath = (id: string) => {
		// 调用 getPathWithBackToParemeter 生成带返回参数的编辑路径
		return getPathWithBackToParemeter(getAdminPath(`/organizations/${id}`));
	};

	/**
	 * @effect
	 * @description 当 `searchTerm` 发生变化时，更新防抖后的搜索词 `debouncedSearchTerm`。
	 * 这是 useDebounceValue hook 的标准用法，确保防抖状态与原始状态同步。
	 */
	useEffect(() => {
		setDebouncedSearchTerm(searchTerm);
	}, [searchTerm, setDebouncedSearchTerm]); // 依赖项包含 searchTerm 和 setDebouncedSearchTerm

	/**
	 * @hook useAdminOrganizationsQuery
	 * @description 使用 react-query 获取管理员视角的组织列表数据。
	 * 传入分页大小、当前页码和防抖后的搜索词作为查询参数。
	 * @returns {object} - 包含组织数据 (`data`) 和加载状态 (`isLoading`) 的对象
	 */
	const { data, isLoading } = useAdminOrganizationsQuery({
		itemsPerPage: ITEMS_PER_PAGE,
		currentPage,
		searchTerm: debouncedSearchTerm,
	});

	/**
	 * @effect
	 * @description 当防抖后的搜索词 `debouncedSearchTerm` 发生变化时，将当前页码重置为 1。
	 * 这是为了确保每次执行新的搜索时，都从第一页开始显示结果。
	 */
	useEffect(() => {
		setCurrentPage(1);
	}, [debouncedSearchTerm]);

	/**
	 * @function deleteOrganization
	 * @description 异步删除指定 ID 的组织。
	 * 使用 toast.promise 显示异步操作的状态 (loading, success, error)。
	 * 调用 @repo/auth/client 提供的 `authClient.organization.delete` 方法执行删除。
	 * 成功后，使用 queryClient 使相关的组织列表查询缓存失效，以重新获取最新数据。
	 * @param {string} id - 需要删除的组织的 ID
	 * @returns {Promise<void>}
	 */
	const deleteOrganization = async (id: string) => {
		// 使用 sonner 的 toast.promise 来包裹异步操作，提供用户反馈
		toast.promise(
			async () => {
				// 调用认证客户端的删除组织接口
				await authClient.organization.delete({
					organizationId: id,
				});
			},
			{
				// 异步操作进行中显示的提示信息
				loading: t("admin.organizations.deleteOrganization.deleting"),
				// 异步操作成功时执行的回调
				success: () => {
					// 使所有与 adminOrganizationsQueryKey 相关的查询缓存失效
					queryClient.invalidateQueries({
						queryKey: adminOrganizationsQueryKey,
					});
					// 返回成功提示信息
					return t("admin.organizations.deleteOrganization.deleted");
				},
				// 异步操作失败时显示的提示信息
				error: t("admin.organizations.deleteOrganization.notDeleted"),
			},
		);
	};

	/**
	 * @constant columns
	 * @description react-table 的列定义数组。
	 * 使用 useMemo 进行缓存，避免在每次渲染时重新创建列定义，优化性能。
	 * 依赖项数组为空，表示此计算仅在组件首次渲染时执行一次。
	 */
	const columns: ColumnDef<
		NonNullable<typeof data>["organizations"][number]
	>[] = useMemo(
		() => [
			// 第一列：组织信息
			{
				accessorKey: "user", // 访问器键，虽然这里写了 "user"，但实际使用了 accessorFn
				header: "", // 表头为空
				// 自定义单元格渲染函数，接收行数据
				accessorFn: (row) => row.name, // 使用组织名称作为排序/过滤依据
				cell: ({
					row: {
						// 从行原始数据中解构所需字段
						original: {
							id,
							name,
							logo,
							_count: { members: membersCount }, // 获取成员数量
						},
					},
				}) => (
					// 单元格内容：Logo、组织名称（链接到编辑页）、成员数量
					<div className="flex items-center gap-2">
						{/* 组织 Logo 组件 */}
						<OrganizationLogo name={name} logoUrl={logo} />
						<div className="leading-tight">
							{/* 组织名称，点击跳转到编辑页 */}
							<Link
								href={getOrganizationEditPath(id)} // 使用辅助函数生成带 backTo 参数的路径
								className="block font-bold"
							>
								{name}
							</Link>
							{/* 显示成员数量 */}
							<small>
								{t("admin.organizations.membersCount", {
									count: membersCount,
								})}
							</small>
						</div>
					</div>
				),
			},
			{
				accessorKey: "owner",
				header: "", // 表头为空，与其他列保持一致
				cell: ({
					row: {
						original: { members },
					},
				}) => {
					// 获取所有角色为owner的成员
					const owners = members?.filter(member => member.role === "owner") || [];
					
					// 如果存在所有者
					if (owners.length > 0) {
						return (
							<div className="leading-tight">
								<div className="font-medium truncate max-w-[280px]" title={owners.map(owner => owner.user.name).join(", ")}>
									{owners.map(owner => owner.user.name).join(", ")}
								</div>
								<small className="text-muted-foreground truncate max-w-[280px] block" title={owners.map(owner => owner.user.email).join(", ")}>
									{owners.map(owner => owner.user.email).join(", ")}
								</small>
							</div>
						);
					}
					
					// 如果没有所有者，显示未指定
					return <span className="text-muted-foreground text-sm">-</span>;
				},
			},
			// 第二列：操作按钮
			{
				accessorKey: "actions", // 访问器键
				header: "", // 表头为空
				// 自定义单元格渲染函数
				cell: ({
					row: {
						original: { id }, // 获取当前行的组织 ID
					},
				}) => {
					return (
						// 单元格内容：包含下拉菜单的操作按钮
						<div className="flex flex-row justify-end gap-2">
							{/* 下拉菜单组件 */}
							<DropdownMenu>
								{/* 触发下拉菜单的按钮 */}
								<DropdownMenuTrigger asChild>
									<Button size="icon" variant="ghost">
										<MoreVerticalIcon className="size-4" />
									</Button>
								</DropdownMenuTrigger>
								{/* 下拉菜单内容 */}
								<DropdownMenuContent>
									{/* 编辑选项 */}
									<DropdownMenuItem asChild>
										<Link
											href={getOrganizationEditPath(id)} // 链接到编辑页
											className="flex items-center"
										>
											<EditIcon className="mr-2 size-4" />
											{t("admin.organizations.edit")}
										</Link>
									</DropdownMenuItem>
									{/* 删除选项 */}
									<DropdownMenuItem
										// 点击时触发确认弹窗
										onClick={() =>
											confirm({
												title: t(
													"admin.organizations.confirmDelete.title",
												),
												message: t(
													"admin.organizations.confirmDelete.message",
												),
												confirmLabel: t(
													"admin.organizations.confirmDelete.confirm",
												),
												destructive: true, // 标记为危险操作（红色按钮）
												// 用户确认后执行删除操作
												onConfirm: () =>
													deleteOrganization(id),
											})
										}
									>
										{/* 删除选项的文本和图标 */}
										<span className="flex items-center text-destructive hover:text-destructive">
											<TrashIcon className="mr-2 size-4" />
											{t("admin.organizations.delete")}
										</span>
									</DropdownMenuItem>
								</DropdownMenuContent>
							</DropdownMenu>
						</div>
					);
				},
			},
		],
		[],
	);

	/**
	 * @constant organizations
	 * @description 从 API 响应数据 `data` 中提取组织列表。
	 * 如果 `data` 或 `data.organizations` 不存在，则返回空数组。
	 * 使用 useMemo 进行缓存，仅在 `data.organizations` 发生变化时重新计算。
	 */
	const organizations = useMemo(
		() => data?.organizations ?? [],
		[data?.organizations], // 依赖于 API 返回的组织数据
	);

	/**
	 * @constant table
	 * @description 使用 useReactTable hook 初始化 react-table 实例。
	 * 配置表格数据、列定义、核心行模型和分页行模型。
	 * `manualPagination: true` 表示分页逻辑由我们手动处理（通过 API 请求）。
	 */
	const table = useReactTable({
		data: organizations, // 表格数据源
		columns, // 列定义
		getCoreRowModel: getCoreRowModel(), // 获取核心行模型
		getPaginationRowModel: getPaginationRowModel(), // 获取分页行模型
		manualPagination: true, // 开启手动分页
	});

	// 组件的 JSX 渲染结构
	return (
		// 使用 Card 组件作为容器
		<Card className="p-6">
			{/* 标题和创建按钮区域 */}
			<div className="mb-4 flex items-center justify-between gap-6">
				<h2 className="font-semibold text-2xl">
					{t("admin.organizations.title")}
				</h2>

				{/* 创建新组织按钮 */}
				<Button asChild>
					<Link href={getAdminPath("/organizations/new")}>
						<PlusIcon className="mr-1.5 size-4" />
						{t("admin.organizations.create")}
					</Link>
				</Button>
			</div>
			{/* 搜索输入框 */}
			<Input
				type="search"
				placeholder={t("admin.organizations.search")}
				value={searchTerm} // 绑定搜索词状态
				onChange={(e) => setSearchTerm(e.target.value)} // 更新搜索词状态
				className="mb-4"
			/>

			{/* 表格容器 */}
			<div className="rounded-md border">
				<Table>
					{/* 表格主体 */}
					<TableBody>
						{/* 判断是否有数据行 */}
						{table.getRowModel().rows?.length ? (
							// 遍历行模型并渲染每一行
							table.getRowModel().rows.map((row) => (
								<TableRow
									key={row.id} // 行的唯一 key
									data-state={
										row.getIsSelected() && "selected"
									} // 设置选中状态
									className="group" // 添加 group 类名，用于可能的 CSS 效果
								>
									{/* 遍历当前行的可见单元格 */}
									{row.getVisibleCells().map((cell) => (
										<TableCell
											key={cell.id} // 单元格的唯一 key
											className="py-2 group-first:rounded-t-md group-last:rounded-b-md" // 单元格样式
										>
											{/* 使用 flexRender 渲染单元格内容 */}
											{flexRender(
												cell.column.columnDef.cell, // 单元格渲染函数
												cell.getContext(), // 单元格上下文
											)}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							// 没有数据或正在加载时的状态
							<TableRow>
								<TableCell
									colSpan={columns.length} // 跨越所有列
									className="h-24 text-center" // 居中显示
								>
									{/* 判断是否正在加载 */}
									{isLoading ? (
										// 加载状态显示 Spinner 和提示文本
										<div className="flex h-full items-center justify-center">
											<Spinner className="mr-2 size-4 text-primary" />
											{t("admin.organizations.loading")}
										</div>
									) : (
										// 加载完成但无数据时显示 "No results."
										<p>No results.</p>
									)}
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>

			{/* 分页组件 */}
			{/* 仅在总条目数大于每页条目数时显示分页 */}
			{!!data?.total && data.total > ITEMS_PER_PAGE && (
				<Pagination
					className="mt-4"
					totalItems={data.total} // 总条目数
					itemsPerPage={ITEMS_PER_PAGE} // 每页条目数
					currentPage={currentPage} // 当前页码
					onChangeCurrentPage={setCurrentPage} // 页码变化时的回调函数
				/>
			)}
		</Card>
	);
}
