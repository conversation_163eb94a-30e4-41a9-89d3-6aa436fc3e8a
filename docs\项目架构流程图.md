# 项目架构流程图

## 文档信息

**创建时间**: 2025年07月07日 13:51:09  
**作者**: hayden  
**版本**: v1.0  
**描述**: 项目架构的Mermaid流程图集合，包含完整的可渲染流程图代码

## 1. 项目技术架构流程图

```mermaid
graph TB
    %% 用户层
    subgraph "用户层 (User Layer)"
        U1[Web浏览器]
        U2[移动端浏览器]
    end

    %% 前端层
    subgraph "前端层 (Frontend Layer)"
        subgraph "Next.js 应用 (apps/web)"
            MW["middleware.ts
            路由中间件"]
            subgraph "App Router"
                MK["营销页面
                (marketing)"]
                SA["SaaS应用
                (saas)"]
                AU["认证页面
                (auth)"]
                AP["应用页面
                (app)"]
            end
            subgraph "功能模块 (modules)"
                I18N["国际化
                (i18n)"]
                UI["UI组件
                (ui)"]
                SH["共享组件
                (shared)"]
                AN["分析模块
                (analytics)"]
            end
        end
    end

    %% API层
    subgraph "API层 (Backend Layer)"
        subgraph "Hono.js API (packages/api)"
            APP["app.ts
            主应用"]
            subgraph "中间件 (middleware)"
                AUTH["认证中间件
                (auth.ts)"]
                CORS["跨域中间件
                (cors.ts)"]
                LOG["日志中间件
                (logger.ts)"]
                CRYPTO["加密中间件
                (shareholder-crypto.ts)"]
            end
            subgraph "API路由 (routes)"
                AR["认证路由
                (auth)"]
                OR["组织路由
                (organizations)"]
                SR["股东名册路由
                (shareholder-registry)"]
                MR["会议路由
                (meeting)"]
                PR["支付路由
                (payments)"]
                NR["n8n代理
                (n8n_proxy)"]
            end
        end
    end

    %% 股东名册核心模块详细展示
    subgraph "股东名册模块 (Core Module)"
        subgraph "路由处理 (Route Handlers)"
            SRR["router.ts
            路由聚合"]
            UPL["upload.ts
            上传处理"]
            LST["list.ts
            列表查询"]
            SHH["shareholders.ts
            股东查询"]
            DEL["delete.ts
            删除操作"]
        end
        subgraph "业务处理器 (Handlers)"
            H01["upload-01.ts
            深市01名册"]
            H05["upload-05.ts
            深市05名册"]
            HT1["upload-t1.ts
            沪市t1名册"]
            HT2["upload-t2.ts
            沪市t2名册"]
            HT3["upload-t3.ts
            沪市t3名册"]
            MRG["merge-*.ts
            名册合并"]
        end
        subgraph "工具库 (Libraries)"
            CFG["config.ts
            配置管理"]
            MAP["field-mapping.ts
            字段映射"]
            VAL["validators.ts
            数据验证"]
            UTL["utils.ts
            工具函数"]
        end
    end

    %% 数据层
    subgraph "数据层 (Data Layer)"
        subgraph "数据库 (PostgreSQL + Prisma)"
            subgraph "核心表"
                USER["User
                用户表"]
                ORG["Organization
                组织表"]
                REG["ShareholderRegistry
                股东名册表"]
                COM["CompanyInfo
                公司信息表"]
                SHA["Shareholder
                股东信息表"]
            end
            subgraph "关联表"
                SES["Session
                会话表"]
                MEM["Member
                成员表"]
                PUR["Purchase
                购买表"]
            end
        end
    end

    %% 外部服务
    subgraph "外部服务 (External Services)"
        N8N[n8n工作流引擎]
        PAY[支付服务]
        EMAIL[邮件服务]
        FILE[文件存储]
    end

    %% 数据流连接
    U1 --> MW
    U2 --> MW
    MW --> MK
    MW --> SA
    MW --> AU
    MW --> AP

    AP --> APP
    AU --> APP

    APP --> AUTH
    APP --> CORS
    APP --> LOG
    APP --> CRYPTO

    AUTH --> AR
    AUTH --> OR
    AUTH --> SR
    AUTH --> MR
    AUTH --> PR
    AUTH --> NR

    SR --> SRR
    SRR --> UPL
    SRR --> LST
    SRR --> SHH
    SRR --> DEL

    UPL --> H01
    UPL --> H05
    UPL --> HT1
    UPL --> HT2
    UPL --> HT3
    UPL --> MRG

    H01 --> CFG
    H01 --> MAP
    H01 --> VAL
    H01 --> UTL

    UPL --> REG
    UPL --> COM
    UPL --> SHA

    LST --> REG
    SHH --> SHA
    DEL --> REG

    OR --> ORG
    AR --> USER
    AR --> SES
    OR --> MEM
    PR --> PUR

    NR --> N8N
    PR --> PAY
    AR --> EMAIL
    UPL --> FILE

    %% 样式定义
    classDef userLayer fill:#e1f5fe
    classDef frontendLayer fill:#f3e5f5
    classDef apiLayer fill:#e8f5e8
    classDef coreModule fill:#fff3e0
    classDef dataLayer fill:#fce4ec
    classDef externalLayer fill:#f1f8e9

    class U1,U2 userLayer
    class MW,MK,SA,AU,AP,I18N,UI,SH,AN frontendLayer
    class APP,AUTH,CORS,LOG,CRYPTO,AR,OR,SR,MR,PR,NR apiLayer
    class SRR,UPL,LST,SHH,DEL,H01,H05,HT1,HT2,HT3,MRG,CFG,MAP,VAL,UTL coreModule
    class USER,ORG,REG,COM,SHA,SES,MEM,PUR dataLayer
    class N8N,PAY,EMAIL,FILE externalLayer
```

## 2. 股东名册模块详细流程图

```mermaid
graph TD
    %% 用户操作入口
    subgraph "用户操作层"
        U1[用户上传Excel文件]
        U2[用户查询名册列表]
        U3[用户查询股东信息]
        U4[用户删除名册]
    end

    %% 前端处理
    subgraph "前端处理层"
        F1[文件上传组件]
        F2[名册列表组件]
        F3[股东查询组件]
        F4[删除确认组件]
        F5[数据加密处理]
    end

    %% API路由层
    subgraph "API路由层"
        R1[POST /upload<br/>上传名册]
        R2[POST /list<br/>查询列表]
        R3[POST /shareholders<br/>查询股东]
        R4[POST /delete<br/>删除名册]
        R5[POST /register-dates<br/>期数查询]
        R6[POST /shareholding-changes<br/>持股变化]
    end

    %% 中间件处理
    subgraph "中间件处理层"
        M1[认证中间件<br/>验证用户身份]
        M2[加密中间件<br/>解密请求数据]
        M3[参数验证<br/>Zod验证]
        M4[组织权限<br/>验证数据权限]
    end

    %% 业务处理层
    subgraph "业务处理层"
        subgraph "上传处理流程"
            B1[文件类型检测]
            B2[Excel数据解析]
            B3[字段映射转换]
            B4[数据验证清洗]
            B5[公司信息提取]
            B6[股东信息处理]
            B7[数据库事务保存]
        end
        
        subgraph "查询处理流程"
            Q1[构建查询条件]
            Q2[执行数据库查询]
            Q3[数据聚合统计]
            Q4[结果格式化]
            Q5[分页处理]
        end
        
        subgraph "删除处理流程"
            D1[验证删除权限]
            D2[检查关联数据]
            D3[执行级联删除]
            D4[清理相关文件]
        end
    end

    %% 名册类型处理器
    subgraph "名册类型处理器"
        H1[深市01名册处理器<br/>upload-01.ts]
        H2[深市05名册处理器<br/>upload-05.ts]
        H3[沪市t1名册处理器<br/>upload-t1.ts]
        H4[沪市t2名册处理器<br/>upload-t2.ts]
        H5[沪市t3名册处理器<br/>upload-t3.ts]
        H6[名册合并处理器<br/>merge-*.ts]
    end

    %% 工具库
    subgraph "工具库"
        T1[字段映射配置<br/>field-mapping.ts]
        T2[数据验证器<br/>validators.ts]
        T3[工具函数<br/>utils.ts]
        T4[配置管理<br/>config.ts]
    end

    %% 数据库操作
    subgraph "数据库操作"
        DB1[ShareholderRegistry<br/>名册基本信息]
        DB2[CompanyInfo<br/>公司详细信息]
        DB3[Shareholder<br/>股东详细信息]
        DB4[Organization<br/>组织信息]
    end

    %% 响应处理
    subgraph "响应处理"
        RES1[数据加密]
        RES2[响应格式化]
        RES3[错误处理]
        RES4[日志记录]
    end

    %% 数据流连接
    U1 --> F1
    U2 --> F2
    U3 --> F3
    U4 --> F4

    F1 --> F5
    F2 --> F5
    F3 --> F5
    F4 --> F5

    F5 --> R1
    F5 --> R2
    F5 --> R3
    F5 --> R4

    R1 --> M1
    R2 --> M1
    R3 --> M1
    R4 --> M1

    M1 --> M2
    M2 --> M3
    M3 --> M4

    %% 上传流程
    M4 --> B1
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> B5
    B5 --> B6
    B6 --> B7

    %% 名册类型分发
    B2 --> H1
    B2 --> H2
    B2 --> H3
    B2 --> H4
    B2 --> H5
    B3 --> H6

    %% 工具库调用
    H1 --> T1
    H1 --> T2
    H1 --> T3
    H1 --> T4

    %% 查询流程
    M4 --> Q1
    Q1 --> Q2
    Q2 --> Q3
    Q3 --> Q4
    Q4 --> Q5

    %% 删除流程
    M4 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> D4

    %% 数据库操作
    B7 --> DB1
    B7 --> DB2
    B7 --> DB3
    Q2 --> DB1
    Q2 --> DB2
    Q2 --> DB3
    D3 --> DB1
    D3 --> DB2
    D3 --> DB3
    M4 --> DB4

    %% 响应处理
    B7 --> RES1
    Q5 --> RES1
    D4 --> RES1
    RES1 --> RES2
    RES2 --> RES3
    RES3 --> RES4

    %% 样式定义
    classDef userOp fill:#e3f2fd
    classDef frontend fill:#f3e5f5
    classDef api fill:#e8f5e8
    classDef middleware fill:#fff3e0
    classDef business fill:#fce4ec
    classDef handler fill:#f1f8e9
    classDef tools fill:#e0f2f1
    classDef database fill:#fdf2e9
    classDef response fill:#f9fbe7

    class U1,U2,U3,U4 userOp
    class F1,F2,F3,F4,F5 frontend
    class R1,R2,R3,R4,R5,R6 api
    class M1,M2,M3,M4 middleware
    class B1,B2,B3,B4,B5,B6,B7,Q1,Q2,Q3,Q4,Q5,D1,D2,D3,D4 business
    class H1,H2,H3,H4,H5,H6 handler
    class T1,T2,T3,T4 tools
    class DB1,DB2,DB3,DB4 database
    class RES1,RES2,RES3,RES4 response
```
