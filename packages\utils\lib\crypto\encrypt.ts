import CryptoJS from 'crypto-js';
import { CRYPTO_CONSTANTS } from './constants';
import { decodeTimeData } from './decrypt';
/**
 * 获取加密所需的密钥和IV
 * 在客户端和服务端兼容的方式获取环境变量
 */
function getEncryptionKeys() {
	// 判断是否在浏览器环境
	const isClient = typeof window !== 'undefined';
	
	let apiKey = '';
	let apiIv = '';
	
	if (isClient) {
		// 客户端直接从window.__ENV对象获取(如果可用)或从process.env获取
		apiKey = 
			(window as any).__ENV?.[CRYPTO_CONSTANTS.ENV_API_KEY] || 
			process.env[CRYPTO_CONSTANTS.ENV_API_KEY] || '';
			
		apiIv = 
			(window as any).__ENV?.[CRYPTO_CONSTANTS.ENV_API_IV] || 
			process.env[CRYPTO_CONSTANTS.ENV_API_IV] || '';
			
		// 如果在客户端但未获取到密钥，尝试直接从全局process获取
		if (!apiKey) {
			apiKey = process.env.NEXT_PUBLIC_SHAREHOLDER_API_KEY || '';
			apiIv = process.env.NEXT_PUBLIC_SHAREHOLDER_API_IV || '';
		}
	} else {
		// 服务端直接从process.env获取
		apiKey = process.env[CRYPTO_CONSTANTS.ENV_API_KEY] || '';
		apiIv = process.env[CRYPTO_CONSTANTS.ENV_API_IV] || '';
	}
	
	return {
		key: CryptoJS.enc.Utf8.parse(apiKey),
		iv: CryptoJS.enc.Utf8.parse(apiIv)
	};
}

/**
 * 使用AES-CBC算法加密数据
 * @param data 需要加密的字符串
 * @returns 加密后的字符串
 */
export function encryptData(data: string): string {
	// 获取加密密钥和IV
	const { key, iv } = getEncryptionKeys();

	// 使用AES-CBC模式加密数据
	const encrypted = CryptoJS.AES.encrypt(data, key, {
		iv: iv,
		mode: CryptoJS.mode.CBC,
		padding: CryptoJS.pad.Pkcs7
	});

	return encrypted.toString();
}

// 缓存的标准时间
interface TimeCache {
	timestamp: number; // 标准时间
	localTime: number; // 本地时间
	lastFetched: number; // 最后获取时间
}

// 时间缓存，用于减少API请求
let standardTimeCache: TimeCache | null = null;

/**
 * 计算本地时间与标准时间的偏移量
 * @returns 返回本地时间与标准时间的偏移量(毫秒)
 */
function getTimeOffset(): number {
	if (!standardTimeCache) {
		return 0;
	}
	// 获取当前本地时间
	const currentLocalTime = Date.now();
	// 计算自上次获取标准时间以来经过的本地时间
	const elapsedLocalTime = currentLocalTime - standardTimeCache.localTime;
	// 估算当前标准时间 = 缓存的标准时间 + 经过的本地时间
	const estimatedStandardTime = standardTimeCache.timestamp + elapsedLocalTime;
	// 返回标准时间与本地时间的偏移量
	return estimatedStandardTime - currentLocalTime;
}

/**
 * 获取标准网络时间
 * 使用服务器时间API获取标准时间，失败时回退到本地时间
 * @param forceRefresh 是否强制刷新缓存
 * @returns Promise<number> 返回标准时间戳（毫秒）
 */
export async function getStandardTime(forceRefresh = false): Promise<number> {
	const currentTime = Date.now();
	
	// 如果存在缓存且未过期且不强制刷新，则使用缓存的时间偏移量计算标准时间
	if (
		standardTimeCache && 
		!forceRefresh && 
		(currentTime - standardTimeCache.lastFetched) < CRYPTO_CONSTANTS.TIME_SYNC.REFRESH_INTERVAL
	) {
		// 使用缓存的时间偏移量计算当前标准时间
		return currentTime + getTimeOffset();
	}
	
	// 需要从服务器获取标准时间
	try {
		// 使用服务器时间API
		const response = await fetch(CRYPTO_CONSTANTS.TIME_SYNC.SERVER_TIME_API);
		if (!response.ok) {
			throw new Error(`服务器时间API响应错误: ${response.status}`);
		}
		const responseData = await response.json();
		
		// 由于时间API现在返回Base64编码的数据，需要解码
		// 导入解码函数		
		// 解码时间数据
		const { timestamp } = decodeTimeData(responseData.data);
		
		// 更新缓存
		standardTimeCache = {
			timestamp,
			localTime: currentTime,
			lastFetched: currentTime
		};
		
		return timestamp;
	} catch (error) {
		// 如果服务器时间API失败，记录错误并使用本地时间
		console.error('获取服务器时间失败，使用本地时间', error);
		
		// 如果之前有缓存，可以使用缓存的偏移量
		if (standardTimeCache) {
			return currentTime + getTimeOffset();
		}
		
		return currentTime;
	}
}

/**
 * 创建包含时间戳的请求结构并加密
 * @param data 业务数据对象
 * @returns 加密后的字符串
 */
export async function encryptRequestData<T>(data: T): Promise<string> {
	
	// 判断是否在浏览器环境
	const isClient = typeof window !== 'undefined';
	let apiKey: string | undefined;
	let apiSecret: string | undefined;
	let apiIv: string | undefined;
	
	if (isClient) {
		// 客户端环境
		apiKey = process.env.NEXT_PUBLIC_SHAREHOLDER_API_KEY || 
			(window as any).__ENV?.NEXT_PUBLIC_SHAREHOLDER_API_KEY || '';
		apiSecret = process.env.NEXT_PUBLIC_SHAREHOLDER_API_SECRET || 
			(window as any).__ENV?.NEXT_PUBLIC_SHAREHOLDER_API_SECRET || '';
		apiIv = process.env.NEXT_PUBLIC_SHAREHOLDER_API_IV || 
			(window as any).__ENV?.NEXT_PUBLIC_SHAREHOLDER_API_IV || '';
	} else {
		// 服务端环境
		apiKey = process.env[CRYPTO_CONSTANTS.ENV_API_KEY] || '';
		apiSecret = process.env[CRYPTO_CONSTANTS.ENV_API_SECRET] || '';
		apiIv = process.env[CRYPTO_CONSTANTS.ENV_API_IV] || '';
	}

	// 使用标准网络时间而非本地时间
	const standardTime = await getStandardTime();

	const requestData = {
		timestamp: standardTime,
		data,
	};

	return encryptData(JSON.stringify(requestData));
}

/**
 * 同步方式获取请求加密数据（兼容旧方法）
 * 适用于不支持异步操作的场景，但会使用时间偏移量调整时间
 * 如果没有缓存，先尝试同步一次服务器时间（仅在客户端环境）
 * @param data 业务数据对象
 * @returns 加密后的字符串
 */
export function encryptRequestDataSync<T>(data: T): string {
	// 判断是否在浏览器环境
	const isClient = typeof window !== 'undefined';
	let apiKey: string | undefined;
	let apiSecret: string | undefined;
	let apiIv: string | undefined;
	
	if (isClient) {
		// 客户端环境
		apiKey = process.env.NEXT_PUBLIC_SHAREHOLDER_API_KEY || 
			(window as any).__ENV?.NEXT_PUBLIC_SHAREHOLDER_API_KEY || '';
		apiSecret = process.env.NEXT_PUBLIC_SHAREHOLDER_API_SECRET || 
			(window as any).__ENV?.NEXT_PUBLIC_SHAREHOLDER_API_SECRET || '';
		apiIv = process.env.NEXT_PUBLIC_SHAREHOLDER_API_IV || 
			(window as any).__ENV?.NEXT_PUBLIC_SHAREHOLDER_API_IV || '';
	} else {
		// 服务端环境
		apiKey = process.env[CRYPTO_CONSTANTS.ENV_API_KEY] || '';
		apiSecret = process.env[CRYPTO_CONSTANTS.ENV_API_SECRET] || '';
		apiIv = process.env[CRYPTO_CONSTANTS.ENV_API_IV] || '';
	}

	// 如果没有时间缓存，并且在客户端环境中，先尝试同步一次
	if (!standardTimeCache && isClient) {
		try {
			// 使用同步方式调用服务器时间API（仅客户端环境）
			const xhr = new XMLHttpRequest();
			xhr.open('GET', CRYPTO_CONSTANTS.TIME_SYNC.SERVER_TIME_API, false); // false表示同步请求
			xhr.send();
			
			if (xhr.status === 200) {
				const responseData = JSON.parse(xhr.responseText);
				// 解码时间数据
				const { timestamp } = decodeTimeData(responseData.data);
				const currentTime = Date.now();
				
				// 更新缓存
				standardTimeCache = {
					timestamp,
					localTime: currentTime,
					lastFetched: currentTime
				};
			}
		} catch (error) {
			console.warn('同步获取服务器时间失败，使用本地时间', error);
			// 同步请求失败，继续使用本地时间
		}
	}

	// 如果在服务端环境，则直接使用服务器的当前时间
	if (!isClient) {
		const serverTime = Date.now();
		return encryptData(JSON.stringify({
			timestamp: serverTime,
			data,
		}));
	}

	// 在客户端环境中，使用本地时间但应用时间偏移量校准
	const currentTime = Date.now();
	const adjustedTime = standardTimeCache ? currentTime + getTimeOffset() : currentTime;
	
	const requestData = {
		timestamp: adjustedTime,
		data,
	};

	return encryptData(JSON.stringify(requestData));
}