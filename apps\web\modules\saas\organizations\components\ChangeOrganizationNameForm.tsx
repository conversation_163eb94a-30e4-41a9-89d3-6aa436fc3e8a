"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { organizationListQueryKey } from "@saas/organizations/lib/api";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { useRouter } from "@shared/hooks/router";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

// 定义表单验证规则
const formSchema = z.object({
	name: z.string().min(2), // 组织名称最少2个字符
});

// 从验证规则中推导出表单类型
type FormSchema = z.infer<typeof formSchema>;

export function ChangeOrganizationNameForm() {
	// 获取国际化翻译函数
	const t = useTranslations();
	// 获取路由实例
	const router = useRouter();
	// 获取查询客户端实例
	const queryClient = useQueryClient();
	// 获取当前活跃组织信息
	const { activeOrganization } = useActiveOrganization();

	// 初始化表单
	const form = useForm<FormSchema>({
		resolver: zodResolver(formSchema), // 使用zod验证
		defaultValues: {
			name: activeOrganization?.name ?? "", // 默认值为当前组织名称
		},
	});

	// 表单提交处理
	const onSubmit = form.handleSubmit(async ({ name }) => {
		// 如果没有活跃组织则返回
		if (!activeOrganization) {
			return;
		}

		try {
			// 调用API更新组织名称
			const { error } = await authClient.organization.update({
				organizationId: activeOrganization.id,
				data: {
					name,
				},
			});

			// 如果有错误则抛出
			if (error) {
				throw error;
			}

			// 显示成功提示
			toast.success(
				t(
					"organizations.settings.notifications.organizationNameUpdated",
				),
			);

			// 使组织列表查询失效,触发重新获取
			queryClient.invalidateQueries({
				queryKey: organizationListQueryKey,
			});
			// 刷新页面
			router.refresh();
		} catch {
			// 显示错误提示
			toast.error(
				t(
					"organizations.settings.notifications.organizationNameNotUpdated",
				),
			);
		}
	});

	return (
		<SettingsItem title={t("organizations.settings.changeName.title")}>
			<form onSubmit={onSubmit}>
				{/* 组织名称输入框 */}
				<Input {...form.register("name")} />

				<div className="mt-4 flex justify-end">
					{/* 提交按钮 */}
					<Button
						type="submit"
						disabled={
							!(
								form.formState.isValid &&
								form.formState.dirtyFields.name
							)
						}
						loading={form.formState.isSubmitting}
					>
						{t("settings.save")}
					</Button>
				</div>
			</form>
		</SettingsItem>
	);
}
