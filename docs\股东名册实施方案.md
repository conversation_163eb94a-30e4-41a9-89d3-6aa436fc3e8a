# 股东名册功能实施方案

## 1. 概述

本文档描述了股东名册功能的技术实施方案，包括数据库设计、API接口和安全加密方案。股东名册功能用于管理和查询上市公司的股东信息，支持导入DBF格式的股东名册文件，并提供多维度的查询和分析功能。

## 2. 系统架构

系统采用模块化的设计思想，完全融入现有的项目结构：

```
项目根目录
├── packages/
│   ├── api/              # API接口模块
│   │   ├── src/
│   │   │   ├── routes/
│   │   │   │   ├── shareholder-registry/ # 股东名册API路由
│   │   │   │   │   ├── router.ts          # 主路由文件
│   │   │   │   │   ├── types.ts           # 类型定义
│   │   │   │   │   ├── upload.ts          # 上传处理
│   │   │   │   │   ├── list.ts            # 列表查询
│   │   │   │   │   ├── report-dates.ts    # 期数查询
│   │   │   │   │   ├── shareholders.ts    # 股东查询
│   │   │   │   │   ├── delete.ts          # 删除处理
│   │   │   │   │   └── lib/               # 辅助函数
│   │   │   ├── middleware/
│   │   │   │   ├── shareholder-crypto.ts  # 股东名册专用加解密中间件
│   │   │   ├── lib/
│   │   │   │   ├── crypto-helper.ts       # 加解密辅助函数
│   ├── database/         # 数据库模块
│   │   ├── prisma/
│   │   │   ├── schema.prisma # 已包含股东名册相关模型
│   ├── utils/            # 通用工具模块
│   │   ├── lib/
│   │   │   ├── crypto/   # 加密工具函数
│   │   │   │   ├── constants.ts  # 加密常量定义
│   │   │   │   ├── encrypt.ts    # 加密工具函数
│   │   │   │   ├── decrypt.ts    # 解密工具函数
│   │   │   │   ├── sign.ts       # 签名生成与验证
│   │   │   │   └── index.ts      # 导出所有加密工具
```

## 3. 数据库设计

数据库已包含三个主要的股东名册相关模型：

### 3.1 股东名册表 (ShareholderRegistry)

记录上传的DBF文件信息，作为其他相关数据的入口。

| 字段名 | 类型 | 描述 | 说明 |
|--------|------|------|------|
| id | String | 主键 | @id @default(cuid()) |
| fileName | String | DBF文件名 | |
| recordCount | Int | 记录数量 | 上传的记录数量 |
| registerDate | DateTime | 报告日期 | 名册报告日期 |
| companyCode | String | 公司代码 | 从文件名提取的公司代码 |
| organizationId | String | 关联的组织ID | 外键，关联Organization表 |
| userId | String | 上传用户ID | 外键，关联User表 |
| uploadedAt | DateTime | 上传时间 | @default(now()) |

### 3.2 公司基本信息表 (CompanyInfo)

存储公司基本信息，从DBF文件的特殊记录中提取。

| 字段名 | 类型 | 描述 | 说明 |
|--------|------|------|------|
| id | String | 主键 | @id @default(cuid()) |
| registryId | String | 关联的股东名册ID | 外键，关联ShareholderRegistry表 |
| organizationId | String | 关联的组织ID | 外键，关联Organization表 |
| companyCode | String | 公司代码 | |
| companyName | String | 公司名称 | |
| totalShares | Decimal | 总股数 | @db.Decimal(17,2) |
| totalShareholders | Int | 总户数 | |
| totalInstitutions | Int | 机构总数 | |
| largeSharesCount | Decimal | 持有万份以上总份数 | @db.Decimal(17,2) |
| largeShareholdersCount | Int | 持有万份以上总户数 | |
| registerDate | DateTime | 报告期日期 | |
| uploadedAt | DateTime | 上传时间 | @default(now()) |

### 3.3 股东信息表 (Shareholder)

存储每个股东的详细信息，基于DBF文件中的记录。

| 字段名 | 类型 | 描述 | 说明 |
|--------|------|------|------|
| id | String | 主键之一 | @default(cuid()) |
| shareholderId | String | 主键之一（优先） | 证件号码，原ZJDM字段 |
| registryId | String | 关联的股东名册ID | 外键，关联ShareholderRegistry表 |
| organizationId | String | 关联的组织ID | 外键，关联Organization表 |
| unifiedAccountNumber | String | 一码通账户号码 | 原YMTH字段 |
| securitiesAccountName | String | 证券账户名称 | 原ZQZHMC字段 |
| shareholderCategory | String | 持有人类别 | 原CYRLBMS字段 |
| numberOfShares | Decimal | 持股数量 | @db.Decimal(17,2)，原CGSL字段 |
| lockedUpShares | Decimal | 限售股数量 | @db.Decimal(17,2)，原XSGSL字段 |
| shareholdingRatio | Decimal | 持股比例 | @db.Decimal(6,2)，原CGBL字段 |
| frozenShares | Decimal | 冻结股数 | @db.Decimal(17,2)，原DJGS字段 |
| cashAccount | String? | 普通证券账户 | 原PTZQZH字段 |
| sharesInCashAccount | Decimal? | 普通账户持股数量 | @db.Decimal(17,2)，原PTZHCGSL字段 |
| marginAccount | String? | 信用证券账户 | 原XYZQZH字段 |
| sharesInMarginAccount | Decimal? | 信用账户持股数量 | @db.Decimal(17,2)，原XYZHCGSL字段 |
| contactAddress | String? | 通讯地址 | 原TXDZ字段 |
| contactNumber | String? | 电话号码 | 原DHHM字段 |
| zipCode | String? | 邮政编码 | 原YZBM字段 |
| relatedPartyIndicator | String? | 关联关系确认标识 | 原GLGXBS字段 |
| clientCategory | String? | 客户类别 | 原KHLB字段 |
| remarks | String? | 备注 | 原BZ字段 |
| registerDate | DateTime | 报告日期 | 与名册报告日期一致 |
| uploadedAt | DateTime | 上传时间 | @default(now()) |

### 3.4 模型间的关联关系

- **组织与各表的关联**：所有表都通过 `organizationId` 与组织表关联，确保数据隔离
- **股东名册与其他表的关联**：公司信息和股东信息都通过 `registryId` 与股东名册关联
- **级联删除机制**：删除上级记录（如组织或股东名册）时，相关的下级记录（如公司信息和股东信息）会被级联删除

## 4. API接口设计

股东名册功能的API接口基于Hono框架实现，遵循RESTful设计原则。所有接口均采用POST请求方式，以便于后续实现请求参数加密和返回数据加密。

### 4.1 接口概览

| 接口路径 | 功能描述 | 权限要求 |
|----------|----------|----------|
| `/api/shareholder-registry/upload` | 上传股东名册 | 已登录用户 |
| `/api/shareholder-registry/list` | 获取股东名册列表 | 已登录用户 |
| `/api/shareholder-registry/report-dates` | 获取报告期日期 | 已登录用户 |
| `/api/shareholder-registry/shareholders` | 获取股东列表 | 已登录用户 |
| `/api/shareholder-registry/delete` | 删除股东名册 | 已登录用户 |

### 4.2 接口详细说明

#### 4.2.1 股东名册上传接口

```
POST /api/shareholder-registry/upload
```

**请求参数**:
```typescript
{
  organizationId: string;  // 组织ID
  fileName: string;        // 原始DBF文件名
  recordCount: number;     // 记录数量
  registerDate: string;      // 报告日期（YYYY-MM-DD格式）
  companyCode: string;     // 公司代码
  companyInfo: {
    companyName: string;           // 公司名称
    totalShares: string;           // 总股数
    totalShareholders: number;     // 总户数
    totalInstitutions: number;     // 机构总数
    largeSharesCount: string;      // 持有万份以上总份数
    largeShareholdersCount: number;// 持有万份以上总户数
  };
  shareholders: [
    {
      shareholderId: string;         // 证件号码
      unifiedAccountNumber: string;   // 一码通账户号码
      securitiesAccountName: string;  // 证券账户名称
      shareholderCategory: string;    // 持有人类别
      numberOfShares: string;         // 持股数量
      lockedUpShares: string;         // 限售股数量
      shareholdingRatio: string;      // 持股比例
      frozenShares: string;           // 冻结股数
      // 以下字段为可选
      cashAccount?: string;           // 普通证券账户
      sharesInCashAccount?: string;   // 普通账户持股数量
      marginAccount?: string;         // 信用证券账户
      sharesInMarginAccount?: string; // 信用账户持股数量
      contactAddress?: string;        // 通讯地址
      contactNumber?: string;         // 电话号码
      zipCode?: string;               // 邮政编码
      relatedPartyIndicator?: string; // 关联关系确认标识
      clientCategory?: string;        // 客户类别
      remarks?: string;               // 备注
    }
  ]
}
```

**响应**:
```typescript
{
  code: 200,
  message: "股东名册上传成功",
  data: {
    id: string,          // 股东名册ID
    fileName: string,    // 文件名
    recordCount: number, // 记录数量
    registerDate: string,  // 报告日期
    uploadedAt: string   // 上传时间
  }
}
```

#### 4.2.2 股东名册列表接口

```
POST /api/shareholder-registry/list
```

**请求参数**:
```typescript
{
  organizationId: string;    // 组织ID
  page?: number;             // 页码，默认为1
  limit?: number;            // 每页条数，默认为10
  companyCode?: string;      // 按公司代码筛选（可选）
}
```

**响应**:
```typescript
{
  code: 200,
  message: "获取股东名册列表成功",
  data: {
    registries: [
      {
        id: string,          // 股东名册ID
        fileName: string,    // 文件名
        recordCount: number, // 记录数量
        registerDate: string,  // 报告日期
        companyCode: string, // 公司代码
        companyName: string, // 公司名称
        uploadedAt: string   // 上传时间
      }
    ],
    pagination: {
      total: number,       // 总记录数
      page: number,        // 当前页码
      limit: number,       // 每页条数
      totalPages: number   // 总页数
    }
  }
}
```

#### 4.2.3 查询期数日期接口

```
POST /api/shareholder-registry/report-dates
```

**请求参数**:
```typescript
{
  organizationId: string;    // 组织ID
  companyCode?: string;      // 按公司代码筛选（可选）
}
```

**响应**:
```typescript
{
  code: 200,
  message: "获取期数日期列表成功",
  data: {
    registerDates: [
      {
        registerDate: string,  // 报告日期
        companyCode: string  // 公司代码
      }
    ]
  }
}
```

#### 4.2.4 股东列表接口

```
POST /api/shareholder-registry/shareholders
```

**请求参数**:
```typescript
{
  registerDate: string;       // 报告期日期
  organizationId: string;   // 组织ID
  page?: number;            // 页码，默认为1
  limit?: number;           // 每页条数，默认为10
  searchTerm?: string;      // 搜索关键词
  sortBy?: string;          // 排序字段，默认为numberOfShares
  sortOrder?: string;       // 排序方向，asc或desc，默认为desc
}
```

**响应**:
```typescript
{
  code: 200,
  message: "获取股东列表成功",
  data: {
    shareholders: [
      {
        id: string,                   // 记录ID
        shareholderId: string,        // 证件号码
        unifiedAccountNumber: string, // 一码通账户
        securitiesAccountName: string,// 证券账户名称
        shareholderCategory: string,  // 持有人类别
        numberOfShares: string,       // 持股数量
        lockedUpShares: string,       // 限售股数量
        shareholdingRatio: string,    // 持股比例
        frozenShares: string,         // 冻结股数
        contactAddress?: string,      // 通讯地址
        contactNumber?: string        // 电话号码
        // ... 其他字段
      }
    ],
    pagination: {
      total: number,       // 总记录数
      page: number,        // 当前页码
      limit: number,       // 每页条数
      totalPages: number   // 总页数
    }
  }
}
```

#### 4.2.5 股东名册删除接口

```
POST /api/shareholder-registry/delete
```

**请求参数**:
```typescript
{
  registryId: string;    // 股东名册ID
  companyCode: string;   // 公司代码（用于确认）
}
```

**响应**:
```typescript
{
  code: 200,
  message: "股东名册已成功删除"
}
```

### 4.3 API路由文件结构

股东名册API的路由文件结构如下：

```
packages/api/src/routes/shareholder-registry/
├── router.ts                # 主路由文件，聚合所有子路由
├── types.ts                 # 共享类型定义
├── lib/                     # 功能辅助库
│   ├── validators.ts        # 请求验证模式定义
│   └── utils.ts             # 辅助工具函数
├── upload.ts                # 上传股东名册处理
├── list.ts                  # 获取名册列表处理
├── report-dates.ts          # 获取期数日期处理
├── shareholders.ts          # 获取股东列表处理  
└── delete.ts                # 删除名册处理
```

主路由文件(router.ts)示例：

```typescript
import { Hono } from "hono";
import { uploadRouter } from "./upload";
import { listRouter } from "./list";
import { registerDatesRouter } from "./report-dates";
import { shareholdersRouter } from "./shareholders";
import { deleteRouter } from "./delete";

export const shareholderRegistryRouter = new Hono()
  .basePath("/shareholder-registry")
  .route("/", uploadRouter)
  .route("/", listRouter)
  .route("/", registerDatesRouter)
  .route("/", shareholdersRouter)
  .route("/", deleteRouter);
```

## 5. API安全加密方案

为保护股东信息API传输的敏感数据，实现了一套基于AES加密和HMAC签名的安全机制。

### 5.1 加密策略

#### 5.1.1 请求加密

所有请求体采用以下格式：

```json
{
  "content": "加密后的请求参数字符串",
  "sign": "请求签名"
}
```

- **content**: 使用AES-CBC加密的原始请求参数JSON字符串
- **sign**: HMAC-SHA256签名，用于验证请求的合法性

原始请求参数在加密前会添加时间戳：

```json
{
  "timestamp": 1621234567890,
  "data": {
    // 实际业务请求参数
  }
}
```

#### 5.1.2 响应加密

成功响应采用以下格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": "加密后的响应数据字符串"
}
```

#### 5.1.3 错误响应格式

错误响应不会加密，以便客户端快速识别和处理错误：

```json
{
  "code": 400,  // 或其他错误状态码
  "message": "错误信息描述",
  "error": {
    "code": "ERROR_CODE",
    "message": "详细错误信息"
  }
}
```

### 5.2 加密中间件实现

系统使用中间件统一处理API请求和响应的加解密逻辑，避免在每个路由处理函数中重复实现加解密代码。

#### 5.2.1 中间件文件结构

```
packages/api/src/middleware/
├── shareholder-crypto.ts       # 股东名册专用加解密中间件
```

#### 5.2.2 中间件实现方式

中间件实现包含以下核心功能：

1. **请求解密**：
   - 验证请求是否包含必要的加密字段
   - 验证请求签名是否有效
   - 解密请求内容并验证时间戳是否在有效期内
   - 将解密后的请求参数注入到请求对象中

2. **响应加密**：
   - 捕获路由处理函数的响应
   - 根据响应状态码判断是否需要加密（成功响应加密，错误响应不加密）
   - 对成功响应的数据进行加密
   - 返回标准格式的响应

#### 5.2.3 中间件代码示例

```typescript
// packages/api/src/middleware/shareholder-crypto.ts
import { Context, MiddlewareHandler, Next } from "hono";
import { 
  decryptData, 
  verifySign, 
  encryptData, 
  isTimestampValid 
} from "@/packages/utils/lib/crypto";
import { HTTPException } from "hono/http-exception";

export const shareholderCryptoMiddleware = (): MiddlewareHandler => {
  return async (c: Context, next: Next) => {
    // 解析请求体
    const requestBody = await c.req.json();

    // 1. 验证请求格式
    if (!requestBody.content || !requestBody.sign) {
      throw new HTTPException(400, { message: "无效的请求格式" });
    }

    // 2. 验证签名
    const { content, sign } = requestBody;
    const isSignValid = verifySign(content, sign);
    if (!isSignValid) {
      throw new HTTPException(400, { message: "无效的请求签名" });
    }

    // 3. 解密内容
    try {
      const decryptedContent = decryptData(content);
      const parsedContent = JSON.parse(decryptedContent);
      
      // 4. 验证时间戳
      if (!parsedContent.timestamp || !isTimestampValid(parsedContent.timestamp)) {
        throw new HTTPException(400, { message: "请求已过期或时间戳无效" });
      }

      // 5. 将解密后的业务数据注入到请求对象
      c.set("originalBody", parsedContent.data);

      // 继续处理请求
      await next();

      // 6. 处理响应加密
      const response = c.get("response");
      if (response && response.code === 200) {
        // 只对成功响应进行加密
        const encryptedData = encryptData(JSON.stringify(response.data));
        c.header("Content-Type", "application/json");
        return c.json({
          code: 200,
          message: response.message || "操作成功",
          data: encryptedData
        });
      }
    } catch (error) {
      throw new HTTPException(400, { message: "请求解密失败" });
    }
  };
};
```

### 5.3 加密工具的实现

加密工具实现是整个加密方案的核心，提供加密、解密和签名验证的核心功能。

#### 5.3.1 加密工具文件结构

```
packages/utils/lib/crypto/
├── constants.ts    # 加密常量定义
├── encrypt.ts      # 加密工具函数
├── decrypt.ts      # 解密工具函数
├── sign.ts         # 签名生成与验证
└── index.ts        # 导出所有加密工具
```

#### 5.3.2 加密常量定义

```typescript
// packages/utils/lib/crypto/constants.ts
export const CRYPTO_CONSTANTS = {
  // 加密算法常量
  ALGORITHM: "AES-CBC",
  
  // 签名算法常量
  SIGN_ALGORITHM: "HmacSHA256",
  
  // 请求有效期(毫秒)，默认60秒
  REQUEST_EXPIRY: 60 * 1000,
  
  // 环境变量名称
  ENV_API_KEY: "NEXT_PUBLIC_SHAREHOLDER_API_KEY",
  ENV_API_SECRET: "NEXT_PUBLIC_SHAREHOLDER_API_SECRET",
  ENV_API_IV: "NEXT_PUBLIC_SHAREHOLDER_API_IV"
};
```

#### 5.3.3 加密工具核心函数

以下是核心加密工具函数的实现示例：

```typescript
// packages/utils/lib/crypto/encrypt.ts
import CryptoJS from 'crypto-js';
import { CRYPTO_CONSTANTS } from './constants';

export function encryptData(data: string): string {
  const key = CryptoJS.enc.Utf8.parse(process.env[CRYPTO_CONSTANTS.ENV_API_KEY] || '');
  const iv = CryptoJS.enc.Utf8.parse(process.env[CRYPTO_CONSTANTS.ENV_API_IV] || '');

  const encrypted = CryptoJS.AES.encrypt(data, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });

  return encrypted.toString();
}

// packages/utils/lib/crypto/decrypt.ts
import CryptoJS from 'crypto-js';
import { CRYPTO_CONSTANTS } from './constants';

export function decryptData(encryptedData: string): string {
  const key = CryptoJS.enc.Utf8.parse(process.env[CRYPTO_CONSTANTS.ENV_API_KEY] || '');
  const iv = CryptoJS.enc.Utf8.parse(process.env[CRYPTO_CONSTANTS.ENV_API_IV] || '');

  const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });

  return decrypted.toString(CryptoJS.enc.Utf8);
}

// packages/utils/lib/crypto/sign.ts
import CryptoJS from 'crypto-js';
import { CRYPTO_CONSTANTS } from './constants';

export function generateSign(content: string): string {
  const secret = process.env[CRYPTO_CONSTANTS.ENV_API_SECRET] || '';
  return CryptoJS.HmacSHA256(content, secret).toString();
}

export function verifySign(content: string, sign: string): boolean {
  const calculatedSign = generateSign(content);
  return calculatedSign === sign;
}

export function isTimestampValid(timestamp: number): boolean {
  const currentTime = Date.now();
  const diff = currentTime - timestamp;
  return diff >= 0 && diff <= CRYPTO_CONSTANTS.REQUEST_EXPIRY;
}

// packages/utils/lib/crypto/index.ts
export * from './constants';
export * from './encrypt';
export * from './decrypt';
export * from './sign';
```

### 5.4 加密流程

#### 5.4.1 前端加密流程

1. **请求加密流程**：
   ```typescript
   import { encryptData, generateSign } from '@/lib/crypto';

   // 原始请求参数
   const requestData = {
     organizationId: "org_123",
     fileName: "example.dbf",
     // ... 其他参数
   };

   // 构建带时间戳的完整请求
   const fullRequest = {
     timestamp: Date.now(),
     data: requestData
   };

   // 将请求对象转为JSON字符串
   const jsonString = JSON.stringify(fullRequest);

   // 加密内容
   const encryptedContent = encryptData(jsonString);

   // 生成签名
   const sign = generateSign(encryptedContent);

   // 最终发送的请求体
   const finalRequest = {
     content: encryptedContent,
     sign: sign
   };

   // 发送请求
   fetch('/api/shareholder-registry/upload', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json'
     },
     body: JSON.stringify(finalRequest)
   });
   ```

2. **响应解密流程**：
   ```typescript
   import { decryptData } from '@/lib/crypto';

   // 接收到的响应
   const response = await fetch('/api/shareholder-registry/list', { /* ... */ });
   const responseJson = await response.json();

   // 处理响应
   if (responseJson.code === 200) {
     // 解密数据
     const decryptedData = decryptData(responseJson.data);
     const parsedData = JSON.parse(decryptedData);
     
     // 使用解密后的数据
     console.log(parsedData);
   } else {
     // 处理错误
     console.error(responseJson.error);
   }
   ```

#### 5.4.2 后端处理流程

1. **中间件解密流程**：
   - 接收包含加密内容和签名的请求
   - 验证签名是否有效
   - 解密内容并验证时间戳
   - 将解密后的业务数据注入到请求上下文

2. **路由处理函数**：
   ```typescript
   import { Hono } from "hono";
   import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";
   import { authMiddleware } from "../../middleware/auth";
   import { z } from "zod";
   import { validator } from "hono-openapi/zod";

   // 定义路由并应用中间件
   export const uploadRouter = new Hono().post(
     "/upload",
     authMiddleware,                 // 先验证认证
     shareholderCryptoMiddleware(),  // 再处理加解密
     validator("json", z.object({
       // 这里的验证是针对解密后的数据
       organizationId: z.string(),
       fileName: z.string(),
       // ... 其他字段验证
     })),
     async (c) => {
       // 从上下文中获取解密后的请求体
       const requestBody = c.get("originalBody");
       
       // 处理业务逻辑
       // ...
       
       // 设置响应，会被中间件自动加密
       c.set("response", {
         code: 200,
         message: "股东名册上传成功",
         data: {
           id: "registry_id",
           fileName: requestBody.fileName,
           // ... 其他响应数据
         }
       });
       
       return c.json({ success: true }); // 这个返回会被中间件替换
     }
   );
   ```

### 5.5 密钥管理

系统使用以下环境变量管理加密密钥：

- **NEXT_PUBLIC_SHAREHOLDER_API_KEY**: AES加密/解密密钥，长度应为16字节或24字节或32字节
- **NEXT_PUBLIC_SHAREHOLDER_API_SECRET**: HMAC签名密钥，建议32字节以上的强密码
- **NEXT_PUBLIC_SHAREHOLDER_API_IV**: AES-CBC初始化向量，必须为16字节

**环境变量的注意事项**：
1. 前缀 `NEXT_PUBLIC_` 表示该环境变量在前端和后端都可访问
2. 由于加密和签名过程在前后端都需要进行，因此所有密钥都需要添加 `NEXT_PUBLIC_` 前缀
3. 这些密钥将暴露在前端代码中，因此还需要额外的安全措施，如请求时效性验证、HTTPS传输等
4. 开发环境可以使用简单密钥，但生产环境必须使用强密钥

### 5.6 API路由的加密集成

所有股东名册相关的API路由都应集成加密中间件，确保数据的安全传输：

```typescript
// packages/api/src/routes/shareholder-registry/router.ts
import { Hono } from "hono";
import { uploadRouter } from "./upload";
import { listRouter } from "./list";
import { registerDatesRouter } from "./report-dates";
import { shareholdersRouter } from "./shareholders";
import { deleteRouter } from "./delete";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";

// 创建主路由并应用中间件
export const shareholderRegistryRouter = new Hono()
  .basePath("/shareholder-registry")
  // 可选：如果希望所有子路由共享加密中间件，可以在这里应用
  // .use(shareholderCryptoMiddleware())
  .route("/", uploadRouter)
  .route("/", listRouter)
  .route("/", registerDatesRouter)
  .route("/", shareholdersRouter)
  .route("/", deleteRouter);
```

**注意**：
- 如果在主路由上应用中间件，所有子路由都会共享相同的加密处理
- 如果希望对不同路由应用不同的加密策略，可以在每个子路由中单独应用中间件

## 6. 实施步骤

### 6.1 数据库配置

1. 在schema.prisma文件中添加股东名册相关模型定义（已完成）
2. 生成数据库迁移文件：
   ```
   pnpm --filter database migrate
   ```
3. 应用迁移到数据库：
   ```
   pnpm --filter database push
   ```
4. 重新生成Prisma客户端：
   ```
   pnpm --filter database generate
   ```

### 6.2 API实现

1. 在packages/api/src/routes/下创建shareholder-registry文件夹
2. 实现上述各个路由文件
3. 在主应用中注册股东名册路由
   ```typescript
   // packages/api/src/app.ts
   import { shareholderRegistryRouter } from "./routes/shareholder-registry/router";
   
   const appRouter = app
     // ... 现有路由 ...
     .route("/", shareholderRegistryRouter)
     // ... 其他路由 ...
   ```

### 6.3 加密工具实现

1. 在packages/utils/lib/下创建crypto文件夹
2. 创建常量定义文件constants.ts，定义加密算法、密钥长度等常量
3. 实现加密函数encrypt.ts，提供AES-CBC加密功能
4. 实现解密函数decrypt.ts，提供AES-CBC解密功能
5. 实现签名函数sign.ts，提供HMAC-SHA256签名生成和验证功能
6. 创建索引文件index.ts，导出所有加密工具

### 6.4 中间件实现

1. 在packages/api/src/middleware/下创建shareholder-crypto.ts文件
2. 实现请求解密和签名验证逻辑
3. 实现响应加密逻辑
4. 实现时效性验证逻辑

### 6.5 API路由集成

1. 在各个API路由中应用加密中间件
2. 对解密后的请求参数进行业务处理
3. 设置响应数据，由中间件自动处理加密

### 6.6 环境变量配置

在项目的`.env.local`文件中添加以下环境变量：

```
# 股东名册API加密密钥
NEXT_PUBLIC_SHAREHOLDER_API_KEY="YOUR_PUBLIC_API_KEY_HERE" # 32字节的AES密钥
NEXT_PUBLIC_SHAREHOLDER_API_SECRET="YOUR_PUBLIC_SECRET_HERE" # 32字节以上的HMAC签名密钥
NEXT_PUBLIC_SHAREHOLDER_API_IV="YOUR_PUBLIC_IV_HERE"       # 16字节的初始化向量
```

## 7. 测试方案

### 7.1 单元测试

测试各个加密函数和API处理逻辑

### 7.2 集成测试

测试完整的API请求流程

### 7.3 安全测试

- **加密解密测试**：测试各种数据的加密解密是否正确
- **签名验证测试**：测试签名生成和验证是否有效
- **时效性验证**：测试过期请求是否被正确拒绝
- **中间件链**：测试加密中间件与其他中间件（如认证中间件）的协同工作
- **错误处理**：测试各种异常情况下的错误处理和响应

#### 7.3.1 安全测试反推案例

以下是两个安全测试的反推案例，用于验证系统的安全性：

**案例1：对签名篡改攻击测试**
```typescript
// 模拟攻击者尝试篡改签名的情况
async function testSignatureManipulation() {
  // 1. 构造正常请求参数
  const validRequest = {
    organizationId: "org_123",
    fileName: "example.dbf"
  };
  
  // 2. 正常加密和签名生成
  const fullRequest = {
    timestamp: Date.now(),
    data: validRequest
  };
  const jsonString = JSON.stringify(fullRequest);
  const encryptedContent = encryptData(jsonString);
  const validSign = generateSign(encryptedContent);
  
  // 3. 篡改签名（改变最后几个字符）
  const tamperSign = validSign.substring(0, validSign.length - 5) + "12345";
  
  // 4. 发送篡改后的请求
  const response = await fetch('/api/shareholder-registry/list', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      content: encryptedContent,
      sign: tamperSign
    })
  });
  
  // 5. 验证服务器是否正确拒绝请求
  const result = await response.json();
  console.assert(result.code === 400, "服务器应当拒绝签名被篡改的请求");
  console.assert(result.message.includes("无效的请求签名"), "错误信息应当指出签名无效");
}
```

**案例2：时间戳攻击测试**
```typescript
// 模拟攻击者重放过期请求的情况
async function testTimestampReplay() {
  // 1. 构造带有过期时间戳的请求
  const validRequest = {
    organizationId: "org_123",
    fileName: "example.dbf"
  };
  
  // 2. 使用过期的时间戳（当前时间减去90秒，超出60秒有效期）
  const expiredTimestamp = Date.now() - 90 * 1000;
  const fullRequest = {
    timestamp: expiredTimestamp,
    data: validRequest
  };
  
  // 3. 正常加密和签名
  const jsonString = JSON.stringify(fullRequest);
  const encryptedContent = encryptData(jsonString);
  const sign = generateSign(encryptedContent);
  
  // 4. 发送带有过期时间戳的请求
  const response = await fetch('/api/shareholder-registry/list', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      content: encryptedContent,
      sign: sign
    })
  });
  
  // 5. 验证服务器是否正确拒绝请求
  const result = await response.json();
  console.assert(result.code === 400, "服务器应当拒绝过期的请求");
  console.assert(result.message.includes("请求已过期"), "错误信息应当指出请求已过期");
}
```

#### 7.3.2 测试加密请求生成代码

以下是使用当前加密方案生成测试请求的代码示例，可以用于API测试工具（如Postman或ApiPost）：

```typescript
// packages/utils/test/crypto-test-helper.ts
import CryptoJS from 'crypto-js';

// 配置信息 - 在实际使用时应从环境变量获取
const API_KEY = process.env.NEXT_PUBLIC_SHAREHOLDER_API_KEY || "test-************************************";
const API_SECRET = process.env.SHAREHOLDER_API_SECRET || "test-secret-12345678901234567890123456789012";
const API_IV = process.env.NEXT_PUBLIC_SHAREHOLDER_API_IV || "1234567890123456"; // 必须是16字节

// AES加密函数
function encryptData(data: string): string {
  const key = CryptoJS.enc.Utf8.parse(API_KEY);
  const iv = CryptoJS.enc.Utf8.parse(API_IV);

  const encrypted = CryptoJS.AES.encrypt(data, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });

  return encrypted.toString();
}

// 生成签名
function generateSign(content: string): string {
  return CryptoJS.HmacSHA256(content, API_SECRET).toString();
}

// 生成加密请求的辅助函数
function generateEncryptedRequest(requestData: any): string {
  // 构建带时间戳的请求
  const fullRequest = {
    timestamp: Date.now(),
    data: requestData
  };

  // 转为JSON并加密
  const jsonString = JSON.stringify(fullRequest);
  const encryptedContent = encryptData(jsonString);
  
  // 生成签名
  const sign = generateSign(encryptedContent);
  
  // 构建最终请求
  const finalRequest = {
    content: encryptedContent,
    sign: sign
  };
  
  return JSON.stringify(finalRequest);
}

// 使用示例：生成加密的名册列表请求
function generateSampleListRequest() {
  const requestData = {
    organizationId: "org_123",
    page: 1,
    limit: 10
  };
  
  return generateEncryptedRequest(requestData);
}

// 使用示例：生成加密的股东查询请求
function generateSampleShareholdersRequest() {
  const requestData = {
    registerDate: "2023-01-01",
    organizationId: "org_123",
    page: 1,
    limit: 15,
    sortBy: "numberOfShares",
    sortOrder: "desc"
  };
  
  return generateEncryptedRequest(requestData);
}

// 控制台输出加密请求结果（测试用）
console.log("加密的名册列表请求体:");
console.log(generateSampleListRequest());

console.log("\n加密的股东查询请求体:");
console.log(generateSampleShareholdersRequest());

// 导出函数供其他测试使用
export {
  encryptData,
  generateSign,
  generateEncryptedRequest,
  generateSampleListRequest,
  generateSampleShareholdersRequest
};
```

此代码可以生成符合系统加密要求的请求体，适用于API测试工具。在实际使用时，应替换环境变量中的密钥和初始化向量。

## 8. 开发注意事项

1. 所有API路由实现应遵循项目现有的目录结构
2. 使用Zod进行请求参数验证和类型定义
3. 所有接口都应使用authMiddleware确保用户已登录
4. 针对批量数据操作（如上传大量股东数据），使用事务处理确保数据一致性
5. 实现详细的错误处理逻辑，返回统一格式的错误响应

## 9. 维护与扩展

本方案设计时考虑了未来的扩展需求，如：

1. 添加更多的分析和报表功能
2. 支持更多格式的数据导入
3. 优化大数据量处理性能
4. 增强数据安全和合规性功能

通过模块化设计和良好的代码组织，确保系统具有高可维护性和可扩展性。

## 10. 前端集成方案

为了使前端能够与加密API正确交互，需要集成相应的加密解密工具。由于加密逻辑在前后端需要保持一致，我们应该直接复用后端的加密工具代码。

### 10.1 利用共享包结构

项目采用了monorepo结构，packages中的utils包是前后端都可以访问的共享代码。因此，我们可以直接导入utils包中的加密工具，确保前后端使用完全相同的加密算法和逻辑。

```
项目根目录
├── packages/
│   ├── utils/            # 通用工具模块（前后端共享）
│   │   ├── lib/
│   │   │   ├── crypto/   # 加密工具函数
│   │   │   │   ├── constants.ts  # 加密常量定义
│   │   │   │   ├── encrypt.ts    # 加密工具函数
│   │   │   │   ├── decrypt.ts    # 解密工具函数
│   │   │   │   ├── sign.ts       # 签名生成与验证
│   │   │   │   └── index.ts      # 导出所有加密工具
```

### 10.2 前端集成加密工具

填写定义的路由地址例：http://localhost:3000/api/shareholders/register/list
NO：携带网页cookie信息、根据请求参数还请求方法，请求测试



### 10.5 共享代码的优势

通过在前后端共享加密工具代码，我们获得以下优势：

1. **代码一致性**：确保前后端使用完全相同的加密/解密算法和逻辑
2. **维护简化**：修改加密逻辑时只需更改一处代码
3. **减少错误**：避免因前后端实现不同导致的加密/解密不匹配问题
4. **更好的类型安全**：共享的TypeScript类型定义确保类型一致性
5. **更高效的开发**：避免重复编写相同的加密逻辑

这种方式充分利用了monorepo架构的优势，使得代码更加模块化、可维护和一致。 