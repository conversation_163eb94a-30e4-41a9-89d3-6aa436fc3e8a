/**
 * AI智能总结骨架屏组件
 * @file AISummarySkeletonComponent.tsx
 * @description 用于在AI智能总结加载时显示的骨架屏，布局与AISummary组件保持一致
 * <AUTHOR>
 * @created 2025-06-18 14:06:44
 */

import { SparklesIcon } from "lucide-react";
import { Skeleton } from "@ui/components/skeleton";

/**
 * AI智能总结骨架屏组件
 * @description 模拟AISummary组件的布局结构，提供加载时的视觉反馈
 * @returns AI智能总结骨架屏UI组件
 */
export function AISummarySkeleton(): JSX.Element {
	return (
		<div className="w-full space-y-2">
			{/* 标题栏骨架 */}
			<div className="flex items-center">
				<div className="flex items-center gap-2">
					<SparklesIcon className="size-5 text-primary" />
					<h2 className="text-lg font-semibold text-foreground">
						AI智能总结
					</h2>
					<Skeleton className="h-6 w-6 rounded" />
				</div>
			</div>

			{/* 内容区域骨架 */}
			<div className="prose prose-sm dark:prose-invert max-w-none space-y-4">
				{/* 模拟主标题 */}
				<Skeleton className="h-6 w-3/4" />
				
				{/* 模拟段落内容 */}
				<div className="space-y-2">
					<Skeleton className="h-4 w-full" />
					<Skeleton className="h-4 w-5/6" />
					<Skeleton className="h-4 w-4/5" />
				</div>

				{/* 模拟二级标题 */}
				<Skeleton className="h-5 w-2/3 mt-6" />
				
				{/* 模拟段落内容 */}
				<div className="space-y-2">
					<Skeleton className="h-4 w-full" />
					<Skeleton className="h-4 w-3/4" />
				</div>

				{/* 模拟列表项 */}
				<div className="space-y-2 pl-4">
					<div className="flex items-start gap-2">
						<Skeleton className="h-2 w-2 rounded-full mt-2 flex-shrink-0" />
						<Skeleton className="h-4 w-5/6" />
					</div>
					<div className="flex items-start gap-2">
						<Skeleton className="h-2 w-2 rounded-full mt-2 flex-shrink-0" />
						<Skeleton className="h-4 w-4/5" />
					</div>
					<div className="flex items-start gap-2">
						<Skeleton className="h-2 w-2 rounded-full mt-2 flex-shrink-0" />
						<Skeleton className="h-4 w-3/4" />
					</div>
				</div>

				{/* 模拟三级标题 */}
				<Skeleton className="h-4 w-1/2 mt-5" />
				
				{/* 模拟段落内容 */}
				<div className="space-y-2">
					<Skeleton className="h-4 w-full" />
					<Skeleton className="h-4 w-4/5" />
					<Skeleton className="h-4 w-2/3" />
				</div>

				{/* 模拟另一个二级标题 */}
				<Skeleton className="h-5 w-3/5 mt-6" />
				
				{/* 模拟段落内容 */}
				<div className="space-y-2">
					<Skeleton className="h-4 w-full" />
					<Skeleton className="h-4 w-5/6" />
					<Skeleton className="h-4 w-3/4" />
				</div>

				{/* 模拟数据统计项 */}
				<div className="space-y-3 mt-4">
					<div className="flex items-center gap-2">
						<Skeleton className="h-4 w-4 rounded" />
						<Skeleton className="h-4 w-32" />
						<Skeleton className="h-4 w-16" />
					</div>
					<div className="flex items-center gap-2">
						<Skeleton className="h-4 w-4 rounded" />
						<Skeleton className="h-4 w-28" />
						<Skeleton className="h-4 w-20" />
					</div>
					<div className="flex items-center gap-2">
						<Skeleton className="h-4 w-4 rounded" />
						<Skeleton className="h-4 w-36" />
						<Skeleton className="h-4 w-14" />
					</div>
				</div>

				{/* 模拟分割线 */}
				<Skeleton className="h-px w-full my-6" />

				{/* 模拟底部说明文字 */}
				<Skeleton className="h-3 w-2/3" />
			</div>
		</div>
	);
}
