1. 架构设计
https://hono.dev/docs/helpers/proxy
1.1 整体流程
前端应用 → /api/n8n_proxy/* → 加密解密中间件 → n8n代理中间件 → 内网n8n
1.2 代理路由设计
- 代理路径: /api/n8n_proxy/*
- 目标地址: 内网n8n服务器
- 路径映射: /api/n8n_proxy/company-overview → http://n8n-server/webhook/v1/company-overview
2. 代理中间件实现
2.1 项目结构
packages/
├── api/                          # API服务和路由
│   ├── src/
│   │   ├── middleware/           # 中间件目录
│   │   │   ├── n8n-proxy.ts      # n8n代理中间件实现
│   │   │   ├── shareholder-crypto.ts  # 加解密中间件
│   │   │   └── ...               # 其他中间件
│   │   ├── routes/               # API路由
│   │   │   └── proxy/            # 代理路由
│   │   │       └── router.ts     # n8n代理路由定义
│   │   └── app.ts                # 应用主文件
│   └── ...
└── ...
2.2 核心代理中间件
// packages/api/src/middleware/n8n-proxy.ts
import { Hono } from "hono";
import { proxy } from "hono/proxy";
import { shareholderCryptoMiddleware } from "./shareholder-crypto";
import { authMiddleware } from "./auth";

/**
 * n8n代理中间件配置
 */interface N8nProxyConfig {
  baseUrl: string;      // n8n服务器基础URL
}

/**
 * 创建n8n代理中间件
 */export function createN8nProxyMiddleware(config: N8nProxyConfig) {
  const app = new Hono();
  
  // 应用通用中间件
  app.use("*", authMiddleware);                    // 认证
  app.use("*", shareholderCryptoMiddleware());     // 加解密// 代理所有请求到n8n
  app.all("*", async (c) => {
    try {
      // 获取解密后的请求数据
      const requestData = c.get("requestData");
      
      // 构建目标URL
      const path = c.req.path.replace("/api/n8n_proxy", "");
      const targetUrl = `${config.baseUrl}${path}`;
      
      // 使用Hono proxy转发请求
      const response = await proxy(targetUrl, {
        method: c.req.method,
        body: requestData ? JSON.stringify(requestData) : undefined,
        headers: {
          "Content-Type": "application/json",
        }
      });
      
      // 解析响应const responseData = await response.json();
      
      // 设置响应数据供加密中间件处理
      c.set("response", {
        code: response.ok ? 200 : response.status,
        message: response.ok ? "操作成功" : "请求失败",
        data: responseData
      });
      
    } catch (error) {
      // 错误处理const isTimeout = error.name === "TimeoutError" || error.name === "AbortError";
      c.set("response", {
        code: isTimeout ? 504 : 500,
        message: isTimeout ? "请求超时" : "服务器内部错误",
        data: null
      });
    }
  });
  
  return app;
}
3. 代理路由实现
2.1 路由定义
// packages/api/src/routes/proxy/router.tsimport { Hono } from "hono";
import { createSimpleN8nProxy } from "../../middleware/n8n-proxy.ts";

const router = new Hono();

// 注册n8n代理中间件
router.route("/*", createSimpleN8nProxy(
  process.env.N8N_BASE_URL || "http://192.168.1.100:5678/webhook/v1"
));

export const proxyRouter = router;
2.2 主应用集成
// packages/api/src/app.tsimport { Hono } from "hono";
import { shareholderRegistryRouter } from "./routes/shareholder-registry/router";
import { timeRouter } from "./routes/time";
import { proxyRouter } from "./routes/proxy/router";
import { corsMiddleware } from "./middleware/cors";
import { loggerMiddleware } from "./middleware/logger";

const app = new Hono().basePath("/api");

// 应用全局中间件
app.use(loggerMiddleware);
app.use(corsMiddleware);

// 注册路由
app.route("/proxy", proxyRouter);
app.route("/shareholder-registry", shareholderRegistryRouter);
app.route("/", timeRouter);

export default app;
2.3 环境变量配置
# .env.local  把/webhook/v1 展示全 /webhook是n8n固定路由固定，v1是API版本
N8N_BASE_URL1=http://localhost:5678/webhook-test/v1
N8N_BASE_URL2=http://localhost:5678/webhook-test/v2
N8N_BASE_URL3=http://localhost:5678/webhook-test/v3...

#其他接口版本...
4. 前端调用
4.1 请求封装示例，或者apipost工具（需携带cookie+加密参数）请求
// 前端调用示例
import { createEncryptedRequest } from "@repo/utils/lib/crypto";

/**
 * 调用n8n代理
 */
async function callN8nProxy<T = any>(path: string, data?: any): Promise<T> {
  const encryptedRequest = createEncryptedRequest(data);
  
  const response = await fetch(`/api/n8n_proxy${path}`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(encryptedRequest)
  });
  
  const result = await response.json();
  if (result.code !== 200) throw new Error(result.message);
  
  return result.data;
}

// 使用示例
const webhookData = await callN8nProxy("/my-workflow", { 
  param: "value" 
});
5. 核心特性
5.1 自动路径映射
- /api/n8n_proxy/webhook/company-overview → http://n8n-server/webhook/v1/company-overview
- /api/n8n_proxy/test-webhook → http://n8n-server/test-webhook
- 支持所有HTTP方法（GET、POST、PUT、DELETE等）
5.2 中间件处理顺序
1. 认证中间件: 验证用户身份
2. 加解密中间件: 处理请求解密和响应加密
3. 代理中间件: 转发请求到n8n并返回响应
5.3 错误处理
- 请求超时: 504错误
- 代理失败: 500错误
- 认证失败: 401错误
- 数据解密失败: 400错误
6. 安全特性
6.1 加密传输
- 请求数据使用AES-CBC加密
- 响应数据自动加密返回
- HMAC-SHA256签名验证
6.2 访问控制
- 用户身份认证
- 权限验证
- 请求来源验证
6.3 配置检查
[] 设置正确的N8N_BASE_URL
[] 确认内网n8n服务可访问
[] 验证加密密钥配置
[] 测试代理功能（浏览器测试）
