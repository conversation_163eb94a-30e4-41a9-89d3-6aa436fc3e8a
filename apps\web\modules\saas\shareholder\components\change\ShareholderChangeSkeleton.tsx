"use client";
import { Skeleton } from "@ui/components/skeleton";
export function ShareholderChangeSkeleton() {
    return (
        <>
                        {/* 工具栏骨架屏 */}
                        <div className="mb-2 p-2">
                            <div className="flex flex-wrap items-center gap-4">
                                {/* 开始日期选择骨架 */}
                                <Skeleton className="h-9 w-[140px] rounded-md" />

                                {/* 结束日期选择骨架 */}
                                <Skeleton className="h-9 w-[140px] rounded-md" />

                                {/* 股东类型选择骨架 */}
                                <Skeleton className="h-9 w-[140px] rounded-md" />

                                {/* 搜索框骨架 */}
                                <Skeleton className="h-9 w-[300px] flex-1 min-w-[200px] max-w-[400px] rounded-md" />

                                {/* 刷新按钮骨架 */}
                                <Skeleton className="h-9 w-9 rounded-md" />
                            </div>
                        </div>

                        {/* 表格骨架屏 */}
                        <div className="rounded-md border">
                            {/* 表头骨架 */}
                            <div className="flex border-b p-2 gap-4">
                                {Array.from({ length: 8 }).map((_, index) => (
                                    <Skeleton
                                        key={`header-${index}`}
                                        className="h-6 flex-1 rounded-md"
                                    />
                                ))}
                            </div>

                            {/* 表格行骨架 - 生成5行 */}
                            {Array.from({ length: 5 }).map((_, rowIndex) => (
                                <div
                                    key={`row-${rowIndex}`}
                                    className="flex border-b p-2 gap-4"
                                >
                                    {Array.from({ length: 8 }).map(
                                        (_, colIndex) => (
                                            <Skeleton
                                                key={`cell-${rowIndex}-${colIndex}`}
                                                className="h-6 flex-1 rounded-md"
                                            />
                                        ),
                                    )}
                                </div>
                            ))}
                        </div>
                    </>
    );
}
