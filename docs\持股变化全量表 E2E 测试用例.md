# 持股变化全量表 E2E 测试用例（优化版）

## 测试环境准备
- 测试环境URL: http://192.168.138.123:3000
- 登录凭据:
  - 邮箱输入框: #«R1ljrlpbl7»-form-item
  - 密码输入框: #«R25jrlpbl7»-form-item > input
  - 登录按钮: body > div.flex.min-h-screen.w-full.py-6 > div > div.container.flex.justify-center > main > div > form > button
  - 登录URL: http://192.168.138.123:3000/auth/login?redirectTo=%2Fapp

### 前置条件
- 用户已登录系统
- 数据库中已存在测试用股东名册数据（至少包含3个报告期的数据）
- 测试数据包含个人股东和机构股东
- 测试数据包含不同的持股变动情况（增持、减持、不变）

## 1. 核心功能测试

### 1.1 页面初始化和数据展示
**测试用例ID**: TC001
**测试描述**: 验证持股变化全量表页面初始化和基本数据展示
**测试步骤**:
1. 登录系统并访问持股变化全量表页面
2. 等待页面完全加载
3. 检查页面初始状态和数据展示

**预期结果**:
- 页面在5秒内完成加载
- 日期选择器显示默认的开始日期（最早报告期）和结束日期（最新报告期）
- 股东类型下拉框默认选中"全部股东"
- 搜索栏为空
- 数据列表显示前30条记录，按净增持股数降序排列
- 表头包含：排名、股东名称、证件号码、一码通、分析范围内净增持、动态日期列
- 动态日期列按时间降序排列（最新日期在左）
- 增持股数显示为绿色，减持股数显示为红色，不变显示为黑色"0"

### 1.2 日期范围筛选功能
**测试用例ID**: TC002
**测试描述**: 验证日期范围选择和边界条件处理
**测试步骤**:
1. 点击开始日期输入框，选择一个有效的开始日期
2. 点击结束日期输入框，选择一个有效的结束日期（晚于开始日期）
3. 观察数据列表变化
4. 测试边界条件：尝试选择早于结束日期的开始日期
5. 测试单日期情况：将开始日期和结束日期设置为同一天

**预期结果**:
- 日历控件正常弹出，只有存在股东名册数据的日期可以选择
- 选择日期后数据列表自动刷新，动态日期列根据选择的日期范围更新
- 排名重新计算
- 系统阻止选择无效的日期范围，显示相应的错误提示
- 单日期情况下只显示一个动态日期列，净增持为0

### 1.3 股东类型筛选功能
**测试用例ID**: TC003
**测试描述**: 验证股东类型筛选功能
**测试步骤**:
1. 选择股东类型为"全部股东"，观察数据列表
2. 选择股东类型为"个人股东"，观察数据列表变化
3. 选择股东类型为"机构股东"，观察数据列表变化

**预期结果**:
- "全部股东"：显示所有类型股东，排名在所有股东中计算
- "个人股东"：只显示个人股东，排名重新在个人股东中计算
- "机构股东"：只显示机构股东，排名重新在机构股东中计算
- 每次切换后数据列表自动刷新

### 1.4 搜索功能测试
**测试用例ID**: TC004
**测试描述**: 验证多字段搜索功能和无结果处理
**测试步骤**:
1. 在搜索栏输入已知股东名称，按回车搜索
2. 清空搜索栏，输入已知一码通，确认搜索
3. 清空搜索栏，输入已知证件号码，确认搜索
4. 输入不存在的股东信息，确认搜索

**预期结果**:
- 股东名称搜索：列表只显示名称匹配的股东，支持模糊搜索
- 一码通搜索：显示对应一码通的股东，精确匹配
- 证件号码搜索：显示对应证件号码的股东，精确匹配
- 无结果搜索：显示"无搜索结果"提示，列表为空但不报错
- 所有搜索结果中排名重新计算

### 1.5 组合筛选和重置功能
**测试用例ID**: TC005
**测试描述**: 验证多条件组合筛选和重置功能
**测试步骤**:
1. 选择特定日期范围和股东类型，观察结果
2. 在上述基础上输入搜索关键词，观察组合筛选结果
3. 点击刷新重置按钮，观察页面状态恢复

**预期结果**:
- 日期+类型筛选：同时满足日期和类型条件的股东显示，排名在筛选结果中计算
- 全维度组合筛选：同时满足所有筛选条件的结果显示，各筛选条件正确联动
- 重置功能：日期恢复到默认范围，股东类型恢复到"全部股东"，搜索栏清空，数据列表恢复到初始状态

## 2. 数据展示和交互测试

### 2.1 数据结构和排序功能
**测试用例ID**: TC006
**测试描述**: 验证数据结构正确性和排序功能
**测试步骤**:
1. 检查表头结构和数据行内容完整性
2. 验证数据计算准确性和格式正确性
3. 测试默认排序和手动排序功能
4. 点击排名列表头测试排序切换
5. 点击日期列表头测试按该日期排序

**预期结果**:
- 表头固定列包含：排名、股东名称、证件号码、一码通、分析范围内净增持
- 动态日期列按时间降序排列，日期格式为YYYY-MM-DD
- 每行数据完整显示，净增持计算正确（结束期-开始期）
- 各期变动计算正确（当期-上期），颜色显示正确（增持绿色、减持红色、不变黑色）
- 默认按净增持股数降序排列，排名列显示正确序号
- 点击排名列：第一次升序，第二次降序，有视觉指示
- 点击日期列：第一次按该日期净增持降序，第二次升序，有视觉指示

### 2.2 数据加载和分页功能
**测试用例ID**: TC007
**测试描述**: 验证数据加载机制和分页处理
**测试步骤**:
1. 页面初始加载，统计显示的数据条数
2. 滚动到列表底部，下拉触发加载更多
3. 观察新数据加载过程和拼接效果
4. 持续下拉直到加载完所有数据，尝试继续下拉

**预期结果**:
- 首次加载显示前30条数据（如果总数不足30条，显示全部数据）
- 下拉加载：自动加载后续数据，新数据正确拼接到列表末尾，加载过程有loading指示
- 加载完成：显示"已加载全部数据"或类似提示，不再触发新的加载请求

## 3. 边界条件和异常处理测试

### 3.1 基础性能和边界条件
**测试用例ID**: TC008
**测试描述**: 验证基础性能要求和边界条件处理
**测试步骤**:
1. 清空缓存，访问页面，记录加载时间
2. 变更筛选条件，记录数据刷新时间
3. 设置筛选条件使结果为空，观察页面表现
4. 筛选条件设置为只返回一条数据，检查显示效果

**预期结果**:
- 页面在5秒内完成加载，前30条数据在5秒内显示
- 筛选后数据在3秒内刷新完成
- 空数据时显示"暂无数据"提示，页面不报错，布局正常
- 单条数据时正常显示，排名显示为1，布局不受影响

### 3.2 异常情况处理
**测试用例ID**: TC009
**测试描述**: 验证网络异常和数据异常的处理机制
**测试步骤**:
1. 准备包含0持股的测试数据，观察数据展示和计算
2. 模拟网络中断，尝试筛选操作，恢复网络后重试
3. 模拟后端返回异常数据，观察前端处理

**预期结果**:
- 0持股股东：正常显示0持股股东，变动计算正确，排名计算正确
- 网络异常：显示网络错误提示，提供重试机制，网络恢复后能正常使用
- 数据异常：显示数据错误提示，页面不崩溃，提供合理的错误信息

## 4. 兼容性和用户体验测试

### 4.1 浏览器兼容性和用户体验
**测试用例ID**: TC010
**测试描述**: 验证主流浏览器兼容性和基本用户体验
**测试步骤**:
1. 在Chrome、Firefox、Edge浏览器中执行核心功能测试
2. 检查UI展示差异和响应式布局
3. 测试移动端适配情况（使用浏览器开发者工具模拟）
4. 检查证件号码脱敏显示
5. 观察操作反馈和加载状态指示

**预期结果**:
- 所有主流浏览器中功能正常，UI展示基本一致
- 移动端布局适配良好，关键功能可正常使用
- 证件号码按需脱敏显示，数据访问权限正确
- 操作有及时的视觉反馈，加载状态有明确指示，操作结果有确认提示

## 测试执行建议

### 测试优先级
1. **P0 (高优先级)**: TC001-TC007 (核心功能流程)
2. **P1 (中优先级)**: TC008-TC009 (边界条件和异常处理)
3. **P2 (低优先级)**: TC010 (兼容性和用户体验)

### 测试数据要求
- 至少准备100个股东的测试数据
- 包含3-5个报告期数据
- 覆盖各种持股变动情况（增持、减持、不变）
- 包含个人和机构股东
- 包含0持股和特殊字符的股东名称

### 自动化建议
- 核心功能测试（TC001-TC007）建议实现自动化
- 使用Playwright进行E2E自动化，配置特定的登录凭据
- 建立持续集成pipeline，每次发布前自动执行核心测试用例

### 测试环境
- 使用指定的测试环境URL和登录凭据
- 准备独立的测试数据库
- 确保测试数据的一致性和可重复性
