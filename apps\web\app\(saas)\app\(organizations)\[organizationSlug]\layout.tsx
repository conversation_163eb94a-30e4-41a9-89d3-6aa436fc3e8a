import { config } from "@repo/config";
import { getActiveOrganization } from "@saas/auth/lib/server";
import { activeOrganizationQueryKey } from "@saas/organizations/lib/api";
import { purchasesQueryKey } from "@saas/payments/lib/api";
import { getPurchases } from "@saas/payments/lib/server";
import { AppWrapper } from "@saas/shared/components/AppWrapper";
import { getServerQueryClient } from "@shared/lib/server";
import { notFound } from "next/navigation";
import type { PropsWithChildren } from "react";

/**
 * 组织布局组件
 * 处理组织相关的布局和数据预取
 * 
 * @param children - 子组件
 * @param params - 包含组织slug的路由参数
 * @returns 带有预取数据的组织布局
 */
export default async function OrganizationLayout({
	children,
	params,
}: PropsWithChildren<{
	params: Promise<{
		organizationSlug: string;
	}>;
}>) {
	// 从路由参数中获取组织slug
	const { organizationSlug } = await params;

	// 获取当前活跃的组织信息
	const organization = await getActiveOrganization(organizationSlug);

	// 如果找不到组织则返回404
	if (!organization) {
		return notFound();
	}

	// 获取服务端查询客户端实例
	const queryClient = getServerQueryClient();

	// 预取组织数据
	await queryClient.prefetchQuery({
		queryKey: activeOrganizationQueryKey(organizationSlug),
		queryFn: () => organization,
	});

	// 如果启用了计费功能,预取购买记录
	if (config.users.enableBilling) {
		await queryClient.prefetchQuery({
			queryKey: purchasesQueryKey(organization.id),
			queryFn: () => getPurchases(organization.id),
		});
	}

	// 使用AppWrapper包装子组件
	return <AppWrapper>{children}</AppWrapper>;
}
