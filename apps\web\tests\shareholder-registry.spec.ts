/**
 * 名册管理模块E2E测试
 * <AUTHOR>
 * @created 2025-06-27 10:11:40
 * @description 测试名册管理模块的登录和导航功能，基于文档要求实现基础测试用例
 */

import { test, expect } from '@playwright/test';
import {
  loginWithCookies,
  navigateAndVerify,
  waitForText,
  waitForElement,
  TEST_CONFIG
} from './utils/test-helpers';

/**
 * 名册管理模块测试套件
 */
test.describe('名册管理模块', () => {

  /**
   * 每个测试前的准备工作
   */
  test.beforeEach(async ({ page }) => {
    // 使用Cookie注入方式快速登录
    await loginWithCookies(page);
    console.log('✅ 测试准备完成：已登录系统');
  });

  /**
   * TC_NM_001: 验证名册管理页面初始状态和导航功能
   * 测试描述: 验证登录后能够成功导航到名册管理页面，并检查页面基本元素
   */
  test('TC_NM_001: 页面初始化和导航测试', async ({ page }) => {
    console.log('🧪 开始测试: TC_NM_001 - 页面初始化和导航测试');

    try {
      // 步骤1: 导航到名册管理页面
      const targetUrl = '/app/hayden/shareholder/manage';
      await navigateAndVerify(page, targetUrl);

      // 步骤2: 验证页面URL正确
      await expect(page).toHaveURL(`${TEST_CONFIG.baseUrl}${targetUrl}`);
      console.log('✅ URL验证通过');

      // 步骤3: 等待页面加载完成，查找关键元素
      // 根据文档，页面应该包含"名册导入"按钮
      await waitForElement(page, 'button', { timeout: 15000 });
      console.log('✅ 页面基本元素加载完成');

      // 步骤4: 检查页面标题或关键文本
      // 尝试查找可能的页面标题或导航标识
      try {
        await waitForText(page, '名册', { timeout: 10000 });
        console.log('✅ 找到名册相关文本');
      } catch (error) {
        console.log('⚠️ 未找到"名册"文本，继续其他验证');
      }

      // 步骤5: 检查是否有数据表格或空状态
      try {
        // 尝试查找表格元素
        await waitForElement(page, 'table, .ant-table, [data-testid="data-table"]', { timeout: 5000 });
        console.log('✅ 找到数据表格');
      } catch (error) {
        try {
          // 如果没有表格，查找空状态
          await waitForText(page, '无数据', { timeout: 5000 });
          console.log('✅ 找到空数据状态');
        } catch (emptyError) {
          console.log('⚠️ 未找到表格或空状态，页面可能还在加载');
        }
      }

      // 步骤6: 尝试查找"名册导入"按钮
      try {
        await waitForText(page, '导入', { timeout: 5000 });
        console.log('✅ 找到导入相关按钮');
      } catch (error) {
        console.log('⚠️ 未找到导入按钮，可能使用不同的文本');
      }

      console.log('✅ TC_NM_001 测试完成：页面导航和基本元素验证通过');

    } catch (error) {
      console.error('❌ TC_NM_001 测试失败:', error);

      // 调试信息：截图保存
      await page.screenshot({
        path: `test-results/TC_NM_001-failure-${Date.now()}.png`,
        fullPage: true
      });

      // 调试信息：打印页面内容
      const pageContent = await page.content();
      console.log('📄 页面内容长度:', pageContent.length);

      throw error;
    }
  });

  /**
   * TC_NM_002: 验证页面响应性和基本交互
   * 测试描述: 验证页面在不同视口下的响应性，以及基本的用户交互
   */
  test('TC_NM_002: 页面响应性和交互测试', async ({ page }) => {
    console.log('🧪 开始测试: TC_NM_002 - 页面响应性和交互测试');

    try {
      // 步骤1: 导航到名册管理页面
      const targetUrl = '/app/hayden/shareholder/manage';
      await navigateAndVerify(page, targetUrl);

      // 步骤2: 测试桌面端视口
      await page.setViewportSize({ width: 1920, height: 1080 });
      await page.waitForTimeout(1000); // 等待布局调整
      console.log('✅ 桌面端视口设置完成');

      // 步骤3: 测试平板端视口
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.waitForTimeout(1000);
      console.log('✅ 平板端视口设置完成');

      // 步骤4: 测试移动端视口
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(1000);
      console.log('✅ 移动端视口设置完成');

      // 步骤5: 恢复桌面端视口进行后续测试
      await page.setViewportSize({ width: 1920, height: 1080 });
      await page.waitForTimeout(1000);

      // 步骤6: 测试页面滚动
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      await page.waitForTimeout(500);
      await page.evaluate(() => window.scrollTo(0, 0));
      console.log('✅ 页面滚动测试完成');

      console.log('✅ TC_NM_002 测试完成：页面响应性和交互验证通过');

    } catch (error) {
      console.error('❌ TC_NM_002 测试失败:', error);

      // 调试信息：截图保存
      await page.screenshot({
        path: `test-results/TC_NM_002-failure-${Date.now()}.png`,
        fullPage: true
      });

      throw error;
    }
  });

  /**
   * TC_NM_003: 验证页面加载性能和稳定性
   * 测试描述: 验证页面加载时间和多次访问的稳定性
   */
  test('TC_NM_003: 页面加载性能测试', async ({ page }) => {
    console.log('🧪 开始测试: TC_NM_003 - 页面加载性能测试');

    try {
      const targetUrl = '/app/hayden/shareholder/manage';

      // 步骤1: 测试首次加载
      const startTime = Date.now();
      await navigateAndVerify(page, targetUrl);
      const firstLoadTime = Date.now() - startTime;
      console.log(`✅ 首次加载时间: ${firstLoadTime}ms`);

      // 步骤2: 测试页面刷新
      const refreshStartTime = Date.now();
      await page.reload();
      await waitForElement(page, 'body', { timeout: 15000 });
      const refreshTime = Date.now() - refreshStartTime;
      console.log(`✅ 页面刷新时间: ${refreshTime}ms`);

      // 步骤3: 测试重复访问
      for (let i = 1; i <= 3; i++) {
        const repeatStartTime = Date.now();
        await page.goto(`${TEST_CONFIG.baseUrl}${targetUrl}`);
        await waitForElement(page, 'body', { timeout: 15000 });
        const repeatTime = Date.now() - repeatStartTime;
        console.log(`✅ 第${i}次重复访问时间: ${repeatTime}ms`);
      }

      // 性能断言（可根据实际情况调整阈值）
      expect(firstLoadTime).toBeLessThan(30000); // 首次加载应在30秒内
      expect(refreshTime).toBeLessThan(20000);   // 刷新应在20秒内

      console.log('✅ TC_NM_003 测试完成：页面加载性能验证通过');

    } catch (error) {
      console.error('❌ TC_NM_003 测试失败:', error);
      throw error;
    }
  });

});