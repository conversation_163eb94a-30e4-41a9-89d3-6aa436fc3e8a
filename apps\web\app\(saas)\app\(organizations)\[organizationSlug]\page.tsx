import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound, redirect } from "next/navigation";

/**
 * 组织首页组件
 * 重定向到市值管理页面
 */
export default async function OrganizationPage({
	params,
}: { params: Promise<{ organizationSlug: string }> }) {
	const { organizationSlug } = await params;

	const activeOrganization = await getActiveOrganization(
		organizationSlug as string,
	);

	if (!activeOrganization) {
		return notFound();
	}

	// 重定向到市值管理页面
	redirect(`/app/${organizationSlug}/market`);
}
