"use client";

import Link from "next/link";
import { ChevronRightIcon } from "lucide-react";
import { useMemo } from "react";
import { cn } from "@ui/lib"; // 导入cn工具函数用于合并类名


export interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
  // 可选的图标组件
  icon?: React.ReactNode;
}

// 自定义样式类型接口
export interface BreadcrumbCustomClass {
  container?: string;    // 容器样式
  separator?: string;    // 分隔符样式
  iconSize?: string;     // 图标尺寸
  activeText?: string;   // 激活项文本样式
  inactiveText?: string; // 非激活项文本样式
  linkText?: string;     // 链接文本样式
}

export interface SharedBreadcrumbProps {
  /** 自定义面包屑项目 */
  items: BreadcrumbItem[];
  /** 自定义样式类 */
  customClassName?: BreadcrumbCustomClass;
}

/**
 * 共享面包屑导航组件
 * 
 * 简化版本，只接收面包屑项目列表
 */
export function SharedBreadcrumb({ 
  items,
  customClassName
}: SharedBreadcrumbProps) {
  // 使用 useMemo 缓存处理后的面包屑项目,避免重复计算
  const breadcrumbItems = useMemo(() => {
    if (!items || items.length === 0) {
      return [];
    }
    
    // 复制一份数组,避免直接修改原数组
    const updatedItems = [...items];
    
    // 确保第一个项目不可点击,移除其 href 属性
    if (updatedItems.length > 0) {
      updatedItems[0] = {
        ...updatedItems[0],
        href: undefined
      };
    }
    
    return updatedItems;
  }, [items]);
  
  // 如果没有面包屑项目则不渲染
  if (!breadcrumbItems || breadcrumbItems.length === 0) {
    return null;
  }

  return (
    // 面包屑导航容器,使用 flex 布局，应用自定义样式
    <nav className={cn("flex items-center space-x-2 text-sm text-muted-foreground mb-4", customClassName?.container)}>
      {/* 遍历渲染面包屑项目 */}
      {breadcrumbItems.map((item, index) => (
        // 每个面包屑项目的容器
        <div key={index} className="flex items-center">
          {/* 除第一项外,其他项目前显示分隔符图标 */}
          {index > 0 && (
            <ChevronRightIcon className={cn("size-4 mx-2", customClassName?.separator, customClassName?.iconSize)} />
          )}
          
          {/* 根据项目状态渲染不同样式 */}
          {item.isActive ? (
            // 当前激活项目 - 显示为加粗文本
            <span className={cn("font-medium text-foreground flex items-center gap-1", customClassName?.activeText)}>
              {/* 如果有图标则显示图标 */}
              {item.icon && <span className="mr-1">{item.icon}</span>}
              <span>{item.label}</span>
            </span>
          ) : item.href ? (
            // 可点击项目 - 显示为链接
            <Link 
              href={item.href}
              className={cn("flex items-center hover:text-foreground transition-colors gap-1", customClassName?.linkText)}
            >
              {item.icon && <span className="mr-1">{item.icon}</span>}
              <span>{item.label}</span>
            </Link>
          ) : (
            // 不可点击项目 - 显示为普通文本
            <span className={cn("flex items-center gap-1 text-muted-foreground cursor-default", customClassName?.inactiveText)}>
              {item.icon && <span className="mr-1">{item.icon}</span>}
              <span>{item.label}</span>
            </span>
          )}
        </div>
      ))}
    </nav>
  );
} 