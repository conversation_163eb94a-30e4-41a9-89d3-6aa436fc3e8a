/**
 * 创建/更新公司筛选配置路由（统一接口）
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 18:22:01 hayden 修复 benchmarkCompanyCodes 类型错误，处理 undefined 值转换为空字符串
 * @description 组织级别的公司筛选配置管理，支持本司代码和对标公司代码的设置
 */
import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { CreateCompanyFilterSchema } from "../lib/validators";

export const companyFilterUpsertRouter = new Hono().post(
  "/upsert",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = CreateCompanyFilterSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { organizationId, companyCode, benchmarkCompanyCodes } = validationResult.data;

      // 处理可选的对标公司代码字段，确保类型安全
      // 2025-07-09 18:22:01 hayden 修复 benchmarkCompanyCodes 类型错误，将 undefined 转换为空字符串
      const safeBenchmarkCompanyCodes = benchmarkCompanyCodes ?? "";

      // 检查是否已存在配置
      const existingFilter = await db.companyFilter.findUnique({
        where: { organizationId }
      });

      let result: { id: string };
      if (existingFilter) {
        // 更新现有配置
        result = await db.companyFilter.update({
          where: { organizationId },
          data: {
            companyCode,
            benchmarkCompanyCodes: safeBenchmarkCompanyCodes,
          }
        });
      } else {
        // 创建新配置
        result = await db.companyFilter.create({
          data: {
            organizationId,
            companyCode,
            benchmarkCompanyCodes: safeBenchmarkCompanyCodes,
          }
        });
      }

      c.set("response", {
        code: 200,
        message: existingFilter ? "公司筛选配置更新成功" : "公司筛选配置创建成功",
        data: {
          id: result.id
        }
      });
      return;

    } catch (error) {
      if (error instanceof HTTPException) {
        c.set("response", {
          code: error.status,
          message: error.message,
          data: null
        });
        return;
      }
      
      console.error("投资人管理API错误:", error);
      c.set("response", {
        code: 500,
        message: "服务器内部错误",
        data: null
      });
      return;
    }
  }
);
