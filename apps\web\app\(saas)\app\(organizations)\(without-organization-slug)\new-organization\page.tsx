import { config } from "@repo/config";
import { getOrganizationList } from "@saas/auth/lib/server";
import { CreateOrganizationForm } from "@saas/organizations/components/CreateOrganizationForm";
import { redirect } from "next/navigation";

export const dynamic = "force-dynamic";

export default async function NewOrganizationPage() {
	const organizations = await getOrganizationList();

	// 保持原有的复杂逻辑，只在特定情况下重定向到无组织提示页面
	if (
		!config.organizations.enable ||
		(!config.organizations.enableUsersToCreateOrganizations &&
			(!config.organizations.requireOrganization ||
				organizations.length > 0))
	) {
		/* 原代码: return redirect("/app");
		 * 修改原因: 当用户无权创建组织且没有组织且要求组织时，重定向到友好提示页面
		 * 修改时间: 2025-06-28
		 * 修改人: LLM
		 * 恢复方法: 删除条件判断，直接使用 redirect("/app")
		 */
		
		// 特殊情况：用户无权创建组织 且 没有组织 且 要求必须有组织
		// 这种情况下显示友好提示页面，否则按原逻辑重定向到 /app
		if (!config.organizations.enableUsersToCreateOrganizations && 
			organizations.length === 0 && 
			config.organizations.requireOrganization) {
			return redirect("/app/no-organization");
		}
		
		return redirect("/app");
	}

	return <CreateOrganizationForm />;
}
