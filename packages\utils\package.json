{"devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.13.13"}, "main": "./index.ts", "name": "@repo/utils", "scripts": {"type-check": "tsc --noEmit", "test:crypto": "ts-node -P tsconfig-cjs.json test-crypto.ts", "decrypt-response": "ts-node -P tsconfig-cjs.json decrypt-response.ts"}, "types": "./**/.tsx", "version": "0.0.0", "dependencies": {"@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "ts-node": "^10.9.2"}}