{"name": "starlink-marketvalue", "private": true, "scripts": {"build": "dotenv -c -- turbo build", "dev": "dotenv -c -- turbo dev --concurrency 15", "start": "dotenv -c -- turbo start", "lint": "biome lint .", "clean": "turbo clean", "format": "biome format . --write", "todev": "./scripts/merge-to-dev.sh", "todev:help": "./scripts/merge-to-dev.sh --help", "todevp": "./scripts/merge-to-dev.sh --dry-run", "fdev": "./scripts/merge-from-dev.sh", "id": "pnpm i && pnpm dev"}, "engines": {"node": ">=20"}, "packageManager": "pnpm@9.3.0", "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.13.13", "dotenv-cli": "^8.0.0", "turbo": "^2.4.4", "typescript": "5.8.2"}, "pnpm": {"overrides": {"@types/react": "19.0.0", "@types/react-dom": "19.0.0"}}, "dependencies": {"jszip": "^3.10.1"}}