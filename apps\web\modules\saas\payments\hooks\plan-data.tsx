import type { config } from "@repo/config";
import { useTranslations } from "next-intl";
import type { ReactNode } from "react";

type ProductReferenceId = keyof (typeof config)["payments"]["plans"];

export function usePlanData() {
	const t = useTranslations();

	const planData: Record<
		ProductReferenceId,
		{
			title: string;
			description: ReactNode;
			features: ReactNode[];
		}
	> = {
		basic: {
			title: t("pricing.products.basic.title"),
			description: t("pricing.products.basic.description"),
			features: [
				t("pricing.products.basic.features.aiReports"),
				t("pricing.products.basic.features.basicSupport"),
				t("pricing.products.basic.features.basicInsights"),
				t("pricing.products.basic.features.limitedSeats"),
			],
		},
		pro: {
			title: t("pricing.products.pro.title"),
			description: t("pricing.products.pro.description"),
			features: [
				t("pricing.products.pro.features.aiReports"),
				t("pricing.products.pro.features.prioritySupport"),
				t("pricing.products.pro.features.competitorAnalysis"),
				t("pricing.products.pro.features.customSeats"),
			],
		},
		advanced: {
			title: t("pricing.products.advanced.title"),
			description: t("pricing.products.advanced.description"),
			features: [
				t("pricing.products.advanced.features.aiReports"),
				t("pricing.products.advanced.features.premiumSupport"),
				t("pricing.products.advanced.features.advancedAnalytics"),
				t("pricing.products.advanced.features.extraSeats"),
			],
		},
		flagship: {
			title: t("pricing.products.flagship.title"),
			description: t("pricing.products.flagship.description"),
			features: [
				t("pricing.products.flagship.features.unlimitedReports"),
				t("pricing.products.flagship.features.dedicatedSupport"),
				t("pricing.products.flagship.features.customAnalytics"),
				t("pricing.products.flagship.features.unlimitedUsers"),
			],
		},
	};

	return { planData };
}
