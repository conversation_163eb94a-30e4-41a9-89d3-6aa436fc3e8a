/**
 * 股东结构历史趋势 API 接口
 * @file shareholders-trend-api.ts
 * @description 股东结构历史趋势相关的 API 接口，使用 n8n 代理中间件和加解密功能
 * <AUTHOR>
 * @created 2025-06-16 16:44:16
 * @modified 2025-06-18 09:40:56 - 重构：使用共用的基础 API 模块
 */

import { createShareholderApiClient, type BaseApiResponse } from "./base-shareholder-api";

/**
 * 股东类型分布数据项接口
 * @interface ShareholderTypeDataItem
 * @description 单个股东类型分布数据项的结构
 * <AUTHOR>
 * @created 2025-06-27 19:31:04
 */
export interface ShareholderTypeDataItem {
	/** 股东类型名称 */
	type: string;
	/** 该类型股东数量 */
	count: number;
	/** 该类型股东持股总数（股） */
	shares: number;
}

/**
 * 股东结构历史趋势数据项接口
 * @interface ShareholdersTrendDataItem
 * @description 单个股东结构历史趋势数据项的结构
 * <AUTHOR>
 * @modified 2025-06-27 19:31:04 - 添加typeData字段支持细分机构股东类型数据
 */
export interface ShareholdersTrendDataItem {
	/** 登记日期（ISO 日期时间字符串） */
	registerDate: string;
	/** 公司名称 */
	companyName: string;
	/** 公司股票代码 */
	companyCode: string;
	/** 总股本（股） */
	total_shares: number;
	/** 股东总人数 */
	total_shareholders: number;
	/** 机构投资者数量 */
	institutional_shareholders: number;
	/** 机构投资者合计持股数（股） */
	institutional_shares: number;
	/** 机构投资者持股占总股本比例（%） */
	institutional_shares_ratio: number;
	/** 个人股东数量（=总股东数-机构投资者数） */
	individual_shareholders: number;
	/** 个人股东合计持股数（股） */
	individual_shares: number;
	/** 个人股东持股占总股本比例（%） */
	individual_shares_ratio: number;
	/** 信用账户股东数量 */
	credit_shareholders: number;
	/** 信用账户持股总数（股） */
	credit_shares: number;
	/** 信用账户持股占总股本比例（%） */
	credit_shares_ratio: number;
	/** 前十大股东合计持股数量（股） */
	top10_shareholding_amount: number;
	/** 前十大股东合计持股数量占总股本比例（%） */
	top10_shareholding_amount_ratio: number;
	/** 前二十大股东合计持股数量（股） */
	top20_shareholding_amount: number;
	/** 前二十大股东合计持股数量占总股本比例（%） */
	top20_shareholding_amount_ratio: number;
	/** 股东类型分布数据 */
	typeData: ShareholderTypeDataItem[];
}

/**
 * 股东结构历史趋势 API 响应数据接口
 * @interface ShareholdersTrendData
 */
export interface ShareholdersTrendData {
	/** 每期股东结构数据列表 */
	trendData: ShareholdersTrendDataItem[];
}

// 响应接口已移至 base-shareholder-api.ts，使用 BaseApiResponse<ShareholdersTrendData>

// 创建 API 客户端实例
const apiClientInstance = createShareholderApiClient();

/**
 * 股东结构历史趋势 API 客户端
 * @description 提供股东结构历史趋势相关的 API 调用方法，使用 n8n 代理中间件
 * <AUTHOR>
 * @created 2025-06-16 16:44:16
 * @modified 2025-06-18 09:40:56 - 重构：使用共用的基础 API 模块
 */
export const shareholdersTrendApi = {
	/**
	 * 获取股东结构历史趋势信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @returns 股东结构历史趋势数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 *
	 * @example
	 * ```typescript
	 * const trendData = await shareholdersTrendApi.getShareholdersTrend("12345");
	 * console.log(trendData.data.trendData);
	 * ```
	 */
	getShareholdersTrend: async (organizationId: string): Promise<BaseApiResponse<ShareholdersTrendData>> => {
		return apiClientInstance.sendBaseRequest<ShareholdersTrendData>(
			"shareholders-trend-analysis",
			organizationId,
			"获取股东结构历史趋势信息失败",
		);
	}
};
