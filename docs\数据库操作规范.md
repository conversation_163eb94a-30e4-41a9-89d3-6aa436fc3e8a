# 数据库操作规范

## 1. 规范目的

本文档旨在规范化项目中的数据库操作流程，确保开发、测试和生产环境的数据库一致性和稳定性，减少数据库迁移过程中可能出现的问题，并为团队成员提供清晰的操作指导。

## 2. 环境区分与操作规范

### 2.1 环境区分与对应操作

| 环境 | 数据库操作命令 | 说明 |
|------|--------------|------|
| 开发环境 | `pnpm --filter database push` | 直接推送修改，不创建迁移文件，适用于快速迭代 |
| 测试环境 | `pnpm --filter database migrate` | 创建并应用迁移文件，记录变更历史 |
| 生产环境 | `pnpm --filter database migrate` | 使用已在测试环境验证过的迁移文件 |

### 2.2 各环境操作详解

#### 2.2.1 开发环境

在开发环境中，允许使用 `push` 命令进行快速迭代：

```powershell
pnpm --filter database push
```

**注意事项：**
- 确保推送前已在本地测试功能正常
- 每次重大更改后运行 `generate` 命令更新类型定义：
  ```powershell
  pnpm --filter database generate
  ```
- 记录数据库结构变更内容，为后续创建迁移文件做准备

#### 2.2.2 测试环境

在测试环境中，必须使用 `migrate` 命令创建迁移记录：

```powershell
pnpm --filter database migrate
```

**操作流程：**
1. 在命令提示中输入有意义的迁移名称，格式建议：`动作_模型_字段`，如：`add_shareholder_contact_info`
2. 确认迁移文件已正确生成在 `prisma/migrations` 目录下
3. 完整测试数据库变更后的应用功能
4. 记录测试结果和可能的注意事项

#### 2.2.3 生产环境

生产环境的迁移必须遵循严格的审查和备份流程：

```powershell
pnpm --filter database migrate
```

**必要流程：**
1. **数据库备份**：迁移前必须完整备份生产数据库
2. **变更审查**：至少一名团队成员审查迁移内容
3. **回滚计划**：准备详细的回滚方案
4. **维护窗口**：在预设的维护时间执行迁移
5. **迁移验证**：迁移后验证数据库结构和应用功能

## 3. 数据模型规范

### 3.1 模型定义规范

在 `schema.prisma` 文件中定义模型时遵循以下规范：

1. **注释规范**
   - 每个模型必须添加中文注释说明其用途
   - 每个字段必须添加中文注释说明其含义
   - 重要关联关系需添加详细说明注释
   - 添加变更时间及人员注释，格式：`//YYYY-MM-DD HH:MM:SS 操作说明`

2. **命名规范**
   - 模型名：采用首字母大写的 PascalCase 命名法
   - 数据库表名：采用小写下划线 snake_case 命名，通过 `@@map` 指定
   - 字段名：采用 camelCase 命名法
   - 索引、主键：根据查询需求命名，表明其用途

3. **类型与约束规范**
   - 所有字段必须明确指定类型和约束
   - ID 字段推荐使用 `@id @default(cuid())`
   - 日期时间字段规范：
     - 创建时间：`@default(now())`
     - 更新时间：`@updatedAt`
   - 必填字段不加问号，可选字段加问号 `?`

4. **关联关系规范**
   - 明确指定 `onDelete` 行为（如 `Cascade`、`SetNull` 等）
   - 为关联字段创建索引
   - 使用有意义的关联名称

### 3.2 当前核心数据模型

项目中的核心数据模型包括：

```prisma
// 股东名册表
model ShareholderRegistry {
  id             String         @id @default(cuid()) // 主键,自动生成
  fileName       String         // DBF文件名
  recordCount    Int            // 记录数量
  registerDate   DateTime       // 名册日期
  companyCode    String         // 公司代码
  organizationId String         // 关联的组织ID
  organization   Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  userId         String         // 上传用户ID
  user           User           @relation(fields: [userId], references: [id], onDelete: SetNull) // 关联用户
  companyInfo    CompanyInfo[]  // 关联的公司信息
  shareholders   Shareholder[]  // 关联的股东信息
  uploadedAt     DateTime       @default(now()) // 上传时间

  @@index([companyCode]) // 公司代码索引
  @@index([organizationId]) // 组织ID索引
  @@index([registerDate]) // 名册日期索引
  @@unique([organizationId, companyCode, registerDate])  // 防止重复导入
  @@map("shareholder_registry") // 映射到数据库表
}

// 公司基本信息表
model CompanyInfo { /* 字段省略 */ }

// 股东信息表
model Shareholder { /* 字段省略 */ }
```

### 3.3 索引优化规范

合理配置索引以提高查询性能：

1. **必要索引**
   - 外键字段必须创建索引
   - 频繁查询条件字段应创建索引
   - 组合查询条件应考虑创建组合索引

2. **避免过度索引**
   - 不要为不常用于查询条件的字段创建索引
   - 考虑索引对写入操作的性能影响

3. **现有索引示例**
   ```prisma
   @@index([shareholderId, registerDate]) // 组合索引：优化查询相同证件号码在不同名册期的记录
   ```

## 4. 数据库迁移管理

### 4.1 迁移创建规范

1. **命名规范**
   - 迁移名称应简洁明了，表明变更内容
   - 格式建议：`动作_模型_特性`（如 `add_shareholder_contact`）

2. **迁移内容规范**
   - 每次迁移应专注于相关的一组变更
   - 避免在单次迁移中进行过多不相关的修改
   - 注意数据完整性，必要时添加数据迁移脚本

3. **敏感操作注意事项**
   - 删除字段前确认无依赖关系
   - 修改字段类型时考虑现有数据兼容性
   - 添加非空字段时提供合理默认值

### 4.2 迁移冲突解决

当多人同时开发导致迁移冲突时：

1. **协调迁移**
   - 团队内协商确定迁移应用顺序
   - 可能需要合并或重写迁移文件

2. **解决方案**
   - 如无冲突，保持各自迁移文件
   - 存在冲突时，重新基于最新状态创建迁移

### 4.3 迁移漂流问题处理

迁移漂流是指数据库实际状态与 Prisma 迁移历史不一致的情况。

#### 4.3.1 漂流检测

可能的漂流迹象：
- 迁移命令报错，提示数据库已有修改
- 应用运行时出现字段不存在等错误

#### 4.3.2 轻微漂流修复

对于开发环境的轻微漂流：

```powershell
# 警告：此操作会重置数据库内容
pnpm --filter database migrate reset
```

#### 4.3.3 严重漂流处理流程

对于生产环境或包含重要数据的环境：

1. **数据库完整备份**
   ```powershell
   # PostgreSQL备份
   pg_dump -U username -d database_name -F c -f backup_file.dump
   ```

2. **创建新数据库**
   ```powershell
   # 创建新数据库
   CREATE DATABASE new_database_name;
   ```

3. **数据迁移方案**

   a. **基础结构迁移**
   - 使用 Prisma 在新数据库创建完整结构：
     ```powershell
     # 修改环境变量指向新数据库
     # 然后执行
     pnpm --filter database migrate reset
     ```

   b. **数据迁移**
   - 对于简单结构，可使用数据库工具直接导出导入
   - 对于复杂关系，建议编写专用迁移脚本

   c. **特殊情况处理**
   - **外键约束**：按正确顺序导入数据，或暂时禁用外键检查
   - **唯一约束**：处理可能的数据冲突
   - **枚举类型**：确保旧数据符合新的枚举定义
   - **触发器**：必要时临时禁用，迁移后重新启用

4. **验证与切换**
   - 全面测试新数据库功能
   - 准备回滚方案
   - 选择适当时间切换到新数据库

## 5. 数据库操作常用命令

### 5.1 项目命令

```powershell
# 生成Prisma客户端和类型定义
pnpm --filter database generate

# 创建并应用迁移
pnpm --filter database migrate

# 直接推送架构变更（开发环境）
pnpm --filter database push

# 启动Prisma Studio
pnpm --filter database studio
```

### 5.2 PostgreSQL常用命令

```sql
-- 列出所有数据库
\l

-- 连接到指定数据库
\c database_name

-- 列出所有表
\dt

-- 查看表结构
\d+ table_name

-- 备份数据库
pg_dump -U username -d database_name -F c -f backup_file.dump

-- 恢复数据库
pg_restore -U username -d new_database_name -F c backup_file.dump
```

## 6. 团队协作规范

### 6.1 开发流程

1. **分支管理**
   - 数据库变更应在功能分支中进行
   - 变更合并前必须通过代码审查

2. **变更记录**
   - 在代码提交信息中明确说明数据库变更
   - 示例：`feat(db): 添加股东联系信息字段`

3. **团队沟通**
   - 重大数据库变更前应告知团队成员
   - 持续集成环境应显示最新的数据库状态

### 6.2 数据安全规范

1. **敏感数据处理**
   - 个人识别信息应考虑加密存储
   - 开发环境使用脱敏数据

2. **权限控制**
   - 生产数据库访问权限严格控制
   - 遵循最小权限原则

3. **审计跟踪**
   - 重要操作记录操作人和时间
   - 考虑添加数据库操作日志

## 7. 故障排查指南

### 7.1 常见错误及解决方案

| 错误类型 | 可能原因 | 解决方案 |
|--------|---------|---------|
| 迁移失败 | 数据库连接问题 | 检查连接字符串和网络 |
| | Schema 语法错误 | 检查 schema.prisma 文件 |
| | 数据库漂移 | 参考漂移解决方案 |
| 类型不匹配 | 缺少 generate | 运行 generate 命令 |
| | 缓存问题 | 清理 node_modules 后重新安装 |
| 外键约束错误 | 数据关联完整性问题 | 检查关联数据是否存在 |

### 7.2 问题上报流程

遇到无法解决的数据库问题时：

1. 记录详细错误信息和重现步骤
2. 确保环境信息完整（数据库版本、Prisma版本等）
3. 通过团队沟通渠道上报
4. 必要时咨询数据库专家

## 8. 附录

### 8.1 项目数据库版本信息

- 数据库类型：PostgreSQL
- ORM工具：Prisma
- 当前迁移历史：
  - 20250520065751_starlink_db：初始数据库设置
  - 20250520070352_test：测试迁移
  - 20250520070732_recover：恢复迁移

### 8.2 相关资源

- [Prisma 官方文档](https://www.prisma.io/docs/)
- [PostgreSQL 官方文档](https://www.postgresql.org/docs/)
- [SupaStarter 数据库指南](https://supastarter.dev/docs/nextjs/database/overview)

---

**文档创建日期**：2025年05月21日  
**最后更新日期**：2025年05月21日  
**作者**：项目开发团队 